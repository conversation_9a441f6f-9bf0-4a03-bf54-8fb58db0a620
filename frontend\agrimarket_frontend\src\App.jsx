import React from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import "./index.css";
import axiosInstance from "./api/axiosInstance";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import ProduceList from "./components/ProduceList";
import LoginPage from "./pages/LoginPage";
import PostProduce from "./pages/PostProduce";
import AdminDashboard from "./pages/AdminDashboard";
import Navbar from "./components/Navbar";
import SearchModal from "./components/SearchModal";
import Home from "./pages/Home";
import ProduceExplore from "./pages/ProduceExplore";
import MarketPrices from "./pages/MarketPrices";
import Categories from "./pages/Categories";
import Profile from "./pages/Profile";
import Favorites from "./pages/Favorites";
import Watchlist from "./pages/Watchlist";
import MyListings from "./pages/MyListings";
import Settings from "./pages/Settings";
import Help from "./pages/Help";
import Partners from "./pages/Partners";
import Activity from "./pages/Activity";
import Taxes from "./pages/Taxes";
import Rankings from "./pages/Rankings";
import Careers from "./pages/Careers";
import Blog from "./pages/Blog";
import Privacy from "./pages/Privacy";
import Terms from "./pages/Terms";
import Organic from "./pages/Organic";
import Fruits from "./pages/Fruits";
import Vegetables from "./pages/Vegetables";
import Grains from "./pages/Grains";
import Collections from "./pages/Collections";
import Orders from "./pages/Orders";
import LogisticsRegister from "./pages/LogisticsRegister";
import About from "./pages/About";
import SuperuserAdminManagement from "./components/SuperuserAdminManagement";
import ComprehensiveAdminDashboard from "./pages/ComprehensiveAdminDashboard";
import Messages from "./pages/Messages";
import LogisticsDashboard from "./pages/LogisticsDashboard";
import LogisticsLogin from "./pages/LogisticsLogin";
import { AuthProvider } from "./api/AuthContext";
import Register from "./pages/Register";
import Footer from "./components/Footer";
import Loader from "./components/Loader";

function App() {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    // Health check - try to connect to backend
    const healthCheck = async () => {
      try {
        // Test the categories endpoint which should always work
        const response = await axiosInstance.get("categories/");
        console.log("✅ Backend connection successful:", response.status);
        console.log("📊 Categories data:", response.data);
        setError(null);
      } catch (err) {
        console.error("❌ Backend connection failed:", err);

        // Provide more specific error messages
        if (err.message?.includes('Network error') || err.code === 'NETWORK_ERROR' || !err.response) {
          setError("Unable to connect to server. Please check if the backend is running on http://127.0.0.1:8000");
        } else if (err.response?.status === 404) {
          setError("API endpoints not found. Please run migrations: python manage.py migrate");
        } else if (err.response?.status >= 500) {
          setError("Server error. Please check the backend logs for details.");
        } else {
          setError(`Connection failed (HTTP ${err.response?.status}): ${err.response?.data?.detail || err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    healthCheck();
  }, []);

  if (loading) return <Loader />;

  if (error) {
    return (
      <div className="container py-5 text-center">
        <div className="alert alert-danger">
          <h4><i className="bi bi-exclamation-triangle-fill me-2"></i>Connection Error</h4>
          <p className="mb-3">{error}</p>

          <div className="mb-3">
            <h6>Troubleshooting Steps:</h6>
            <ol className="text-start d-inline-block">
              <li>Make sure the backend is running: <code>cd backend && python manage.py runserver</code></li>
              <li>Check if you can access: <a href="http://127.0.0.1:8000/api/categories/" target="_blank" rel="noopener noreferrer">http://127.0.0.1:8000/api/categories/</a></li>
              <li>Verify your .env file has correct CORS settings</li>
              <li>Check the browser console (F12) for more details</li>
            </ol>
          </div>

          <div className="btn-group">
            <button
              className="btn btn-primary"
              onClick={() => window.location.reload()}
            >
              <i className="bi bi-arrow-clockwise me-1"></i>
              Retry Connection
            </button>
            <a
              href="http://127.0.0.1:8000/api/categories/"
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-outline-secondary"
            >
              <i className="bi bi-link-45deg me-1"></i>
              Test Backend
            </a>
          </div>
        </div>
      </div>
    );
  }
  return (
    <AuthProvider>
      <Router>
        <Navbar />
        <main className="main-content" style={{ marginTop: '70px' }}>
          <SearchModal />
          <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/post-produce" element={<PostProduce />} />
          <Route path="/admin-dashboard" element={<AdminDashboard />} />
          <Route path="/comprehensive-admin" element={<ComprehensiveAdminDashboard />} />
          <Route path="/messages" element={<Messages />} />
          <Route path="/logistics-dashboard" element={<LogisticsDashboard />} />
          <Route path="/logistics-login" element={<LogisticsLogin />} />
          <Route path="/superuser-admin-management" element={<SuperuserAdminManagement />} />
          <Route path="/produce-explore" element={<ProduceExplore />} />
          <Route path="/market-prices" element={<MarketPrices />} />
          <Route path="/categories" element={<Categories />} />
          <Route path="/about" element={<About />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/favorites" element={<Favorites />} />
          <Route path="/watchlist" element={<Watchlist />} />
          <Route path="/my-listings" element={<MyListings />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/help" element={<Help />} />
          <Route path="/partners" element={<Partners />} />
          <Route path="/activity" element={<Activity />} />
          <Route path="/taxes" element={<Taxes />} />
          <Route path="/rankings" element={<Rankings />} />
          <Route path="/careers" element={<Careers />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/privacy" element={<Privacy />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/organic" element={<Organic />} />
          <Route path="/fruits" element={<Fruits />} />
          <Route path="/vegetables" element={<Vegetables />} />
          <Route path="/grains" element={<Grains />} />
          <Route path="/collections" element={<Collections />} />
          <Route path="/register" element={<Register />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/logistics-register" element={<LogisticsRegister />} />
        </Routes>
        </main>
        <Footer />
      </Router>
    </AuthProvider>
  );
}

export default App;
