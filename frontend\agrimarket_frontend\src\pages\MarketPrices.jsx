import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";
import Loader from "../components/Loader";

function MarketPrices() {
  const [marketPrices, setMarketPrices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchMarketPrices();
  }, []);

  const fetchMarketPrices = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get("/market-prices/");
      setMarketPrices(response.data.results || response.data);
      setError("");
    } catch (err) {
      console.error("Error fetching market prices:", err);
      setError("Failed to load market prices. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) return <Loader />;

  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <h1 className="mb-4 text-primary">Market Prices</h1>
        <p className="lead">
          Check the latest market prices for popular produce in Zimbabwe. Prices are
          updated daily for your convenience.
        </p>

        {error && (
          <div className="alert alert-danger" role="alert">
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            {error}
          </div>
        )}

        <div className="table-responsive mt-4">
          <table className="table table-bordered table-striped">
            <thead className="table-success">
              <tr>
                <th>Produce</th>
                <th>Price (USD)</th>
                <th>Source</th>
                <th>Last Updated</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {marketPrices.length === 0 ? (
                <tr>
                  <td colSpan="5" className="text-center text-muted py-4">
                    {error ? "Unable to load market prices" : "No market prices available at the moment"}
                  </td>
                </tr>
              ) : (
                marketPrices.map((price) => (
                  <tr key={price.id}>
                    <td className="fw-semibold">{price.crop_name}</td>
                    <td className="text-success fw-bold">${price.price}</td>
                    <td>{price.source || "N/A"}</td>
                    <td>{formatDate(price.date)}</td>
                    <td>
                      <span className={`badge ${price.approved ? 'bg-success' : 'bg-warning'}`}>
                        {price.approved ? 'Verified' : 'Pending'}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {marketPrices.length === 0 && !error && (
          <div className="alert alert-info">
            <i className="bi bi-info-circle-fill me-2"></i>
            Market prices will be displayed here once they are added by administrators.
          </div>
        )}
      </div>
    </>
  );
}

export default MarketPrices;
