import React, { useState, useEffect, useCallback } from "react";
import axiosInstance from "../api/axiosInstance";
import Loader from "../components/Loader";
import "../assets/css/market-prices.css?v=2";

// Debounce utility function
const debounce = (func, wait) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

function MarketPrices() {
  const [marketPrices, setMarketPrices] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [nextPage, setNextPage] = useState(null);
  const [previousPage, setPreviousPage] = useState(null);
  const [currentPageUrl, setCurrentPageUrl] = useState("/market-prices/");
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    fetchMarketPrices(currentPageUrl);
  }, [currentPageUrl]);

  const fetchMarketPrices = async (url) => {
    try {
      if (initialLoading) {
        setInitialLoading(true);
      } else {
        setSearching(true);
      }
      const response = await axiosInstance.get(url, {
        params: { search: searchTerm }
      });
      setMarketPrices(response.data.results || response.data);
      setNextPage(response.data.next);
      setPreviousPage(response.data.previous);
      setTotalPages(Math.ceil(response.data.count / 10)); // Assuming 10 items per page
      
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const page = urlParams.get('page');
      setCurrentPage(page ? parseInt(page, 10) : 1);

      setError("");
    } catch (err) {
      console.error("Error fetching market prices:", err);
      setError("Failed to load market prices. Please try again later.");
    } finally {
      if (initialLoading) setInitialLoading(false);
      setSearching(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value) => {
      setCurrentPageUrl(`/market-prices/?search=${value}`);
    }, 500),
    []
  );

  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    setCurrentPageUrl(`/market-prices/?search=${searchTerm}`);
  };

  const handlePageChange = (pageNumber) => {
    const url = `/market-prices/?page=${pageNumber}`;
    setCurrentPageUrl(url);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (initialLoading) return <Loader />;

  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <h1 className="mb-4 text-primary">Market Prices</h1>
        <p className="lead">
          Check the latest market prices for popular produce in Zimbabwe. Prices are
          updated weekly for your convenience.
        </p>

        {error && (
          <div className="alert alert-danger" role="alert">
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            {error}
          </div>
        )}

        <form onSubmit={handleSearchSubmit} className="mb-4">
          <div className="input-group">
            <input
              type="text"
              className="form-control"
              placeholder="Search by produce name..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <button className="btn btn-primary" type="submit">Search</button>
          </div>
        </form>

        <div className="table-responsive mt-4">
          <table className="table table-bordered table-striped">
            <thead className="table-success">
              <tr>
                <th>Produce</th>
                <th>Price (USD)</th>
                <th>Unit/Quantity</th>
                <th>Source</th>
                <th>Last Updated</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {marketPrices.length === 0 ? (
                <tr>
                  <td colSpan="6" className="text-center text-muted py-4">
                    {searchTerm ? "No matching produce found" : "No market prices available"}
                  </td>
                </tr>
              ) : (
                marketPrices.map((price) => (
                  <tr key={price.id}>
                    <td className="fw-semibold">{price.crop_name}</td>
                    <td className="text-success fw-bold">${price.price}</td>
                    <td>{price.unit}</td>
                    <td>{price.source || "N/A"}</td>
                    <td>{formatDate(price.date)}</td>
                    <td>
                      <span className={`badge ${price.approved ? 'bg-success' : 'bg-warning'}`}>
                        {price.approved ? 'Verified' : 'Pending'}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <nav>
            <ul className="pagination justify-content-center">
              <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                <button className="page-link" onClick={() => handlePageChange(currentPage - 1)}>
                  Previous
                </button>
              </li>
              {[...Array(totalPages).keys()].map(number => (
                <li key={number + 1} className={`page-item ${currentPage === number + 1 ? 'active' : ''}`}>
                  <button className="page-link" onClick={() => handlePageChange(number + 1)}>
                    {number + 1}
                  </button>
                </li>
              ))}
              <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                <button className="page-link" onClick={() => handlePageChange(currentPage + 1)}>
                  Next
                </button>
              </li>
            </ul>
          </nav>
        )}

        {marketPrices.length === 0 && !error && (
          <div className="alert alert-info">
            <i className="bi bi-info-circle-fill me-2"></i>
            Market prices will be displayed here once they are added by administrators.
          </div>
        )}
      </div>
    </>
  );
}

export default MarketPrices;
