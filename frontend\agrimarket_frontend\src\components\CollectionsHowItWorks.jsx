import React from "react";
import { FaWallet, FaSeedling, FaTag } from "react-icons/fa";

function CollectionsHowItWorks() {
  return (
    <section className="position-relative mb-5">
      <div className="container py-5 position-relative">
        <div className="row pt-4">
          <div className="col-lg-4 mb-4 mb-lg-0">
            <div className="card hover-lift border-0 shadow-sm step-container">
              <div className="step-icon">
                <FaWallet size={32} className="text-white" />
              </div>
              <h5 className="mb-4 text-primary">Set up your AgriMarket wallet</h5>
              <p className="mb-0">Create your AgriMarket account and set up your digital wallet to start collecting and trading produce collections securely.</p>
            </div>
          </div>
          <div className="col-lg-4 mb-4 mb-lg-0">
            <div className="card hover-lift border-0 shadow-sm step-container">
              <div className="step-icon">
                <FaSeedling size={32} className="text-white" />
              </div>
              <h5 className="mb-4 text-primary">Add your produce collections</h5>
              <p className="mb-0">List your best produce or join curated collections to reach more buyers and boost your farm's visibility.</p>
            </div>
          </div>
          <div className="col-lg-4 mb-4 mb-lg-0">
            <div className="card hover-lift border-0 shadow-sm step-container">
              <div className="step-icon">
                <FaTag size={32} className="text-white" />
              </div>
              <h5 className="mb-4 text-primary">List for sale or trade</h5>
              <p className="mb-0">Set your price, negotiate with buyers, and complete secure transactions directly on AgriMarket.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default CollectionsHowItWorks;
