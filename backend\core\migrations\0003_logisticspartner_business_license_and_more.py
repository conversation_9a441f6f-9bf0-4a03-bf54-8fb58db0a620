# Generated by Django 5.2 on 2025-07-05 19:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_conversation_message_messagenotification_messageread'),
    ]

    operations = [
        migrations.AddField(
            model_name='logisticspartner',
            name='business_license',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='logisticspartner',
            name='capacity',
            field=models.CharField(blank=True, help_text="e.g., '5 tons', '1000 kg'", max_length=100),
        ),
        migrations.AddField(
            model_name='logisticspartner',
            name='insurance_details',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='logisticspartner',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='logisticspartner',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='logistics_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='logisticspartner',
            name='vehicle_types',
            field=models.CharField(blank=True, help_text='Comma-separated list of vehicle types', max_length=500),
        ),
        migrations.AddField(
            model_name='user',
            name='is_logistics_partner',
            field=models.BooleanField(default=False),
        ),
    ]
