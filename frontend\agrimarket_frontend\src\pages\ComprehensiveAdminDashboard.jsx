import React, { useState, useEffect } from 'react';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';
import Navbar from '../components/Navbar';

const ComprehensiveAdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [pendingApprovals, setPendingApprovals] = useState(null);
  const [allUsers, setAllUsers] = useState([]);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const { user, hasRole } = useAuth();

  useEffect(() => {
    if (!hasRole('admin')) {
      setError('Access denied. Admin privileges required.');
      setLoading(false);
      return;
    }
    
    fetchDashboardData();
  }, [hasRole]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statsRes, approvalsRes, usersRes] = await Promise.all([
        axiosInstance.get('admin/dashboard/stats/'),
        axiosInstance.get('admin/pending-approvals/'),
        axiosInstance.get('admin/users/')
      ]);
      
      setStats(statsRes.data);
      setPendingApprovals(approvalsRes.data);
      setAllUsers(usersRes.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching admin data:', err);
      setError('Failed to load admin dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveItem = async (type, id, action) => {
    try {
      let endpoint = '';
      if (type === 'produce') {
        endpoint = `admin/approve-produce/${id}/`;
      } else if (type === 'logistics') {
        endpoint = `admin/approve-logistics/${id}/`;
      }
      
      const response = await axiosInstance.post(endpoint, { action });
      setSuccessMessage(response.data.message);
      
      // Refresh data
      fetchDashboardData();
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error('Error processing approval:', err);
      setError(err.response?.data?.error || 'Failed to process approval');
    }
  };

  const StatCard = ({ title, value, subtitle, color = 'primary' }) => (
    <div className="col-md-3 mb-4">
      <div className={`card border-${color} h-100`}>
        <div className="card-body text-center">
          <h5 className={`card-title text-${color}`}>{title}</h5>
          <h2 className={`text-${color}`}>{value}</h2>
          {subtitle && <p className="card-text text-muted">{subtitle}</p>}
        </div>
      </div>
    </div>
  );

  const ApprovalCard = ({ item, type, onApprove }) => (
    <div className="card mb-3">
      <div className="card-body">
        <div className="row align-items-center">
          <div className="col-md-8">
            <h6 className="card-title mb-1">
              {type === 'produce' ? item.name : item.name}
            </h6>
            <p className="card-text text-muted mb-1">
              {type === 'produce' ? `By: ${item.farmer?.username}` : `Email: ${item.email}`}
            </p>
            <small className="text-muted">
              {type === 'produce' ? `Price: $${item.price}` : `Phone: ${item.contact_phone}`}
            </small>
          </div>
          <div className="col-md-4 text-end">
            <button 
              className="btn btn-success btn-sm me-2"
              onClick={() => onApprove(type, item.id, 'approve')}
            >
              Approve
            </button>
            <button 
              className="btn btn-danger btn-sm"
              onClick={() => onApprove(type, item.id, 'reject')}
            >
              Reject
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (!hasRole('admin')) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="alert alert-danger">
            <h4>Access Denied</h4>
            <p>You need admin privileges to access this page.</p>
          </div>
        </div>
      </>
    );
  }

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading admin dashboard...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container-fluid dashboard-content">
        <div className="row">
          {/* Sidebar */}
          <div className="col-md-2">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Admin Panel</h6>
              </div>
              <div className="list-group list-group-flush">
                <button 
                  className={`list-group-item list-group-item-action ${activeTab === 'dashboard' ? 'active' : ''}`}
                  onClick={() => setActiveTab('dashboard')}
                >
                  📊 Dashboard
                </button>
                <button 
                  className={`list-group-item list-group-item-action ${activeTab === 'approvals' ? 'active' : ''}`}
                  onClick={() => setActiveTab('approvals')}
                >
                  ⏳ Pending Approvals
                </button>
                <button 
                  className={`list-group-item list-group-item-action ${activeTab === 'users' ? 'active' : ''}`}
                  onClick={() => setActiveTab('users')}
                >
                  👥 User Management
                </button>
                <button 
                  className={`list-group-item list-group-item-action ${activeTab === 'reports' ? 'active' : ''}`}
                  onClick={() => setActiveTab('reports')}
                >
                  📈 Reports
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="col-md-10">
            {error && (
              <div className="alert alert-danger alert-dismissible fade show">
                {error}
                <button type="button" className="btn-close" onClick={() => setError(null)}></button>
              </div>
            )}

            {successMessage && (
              <div className="alert alert-success alert-dismissible fade show">
                {successMessage}
                <button type="button" className="btn-close" onClick={() => setSuccessMessage(null)}></button>
              </div>
            )}

            {/* Dashboard Tab */}
            {activeTab === 'dashboard' && stats && (
              <div>
                <h2 className="mb-4">Admin Dashboard</h2>
                
                {/* User Statistics */}
                <h4 className="mb-3">User Statistics</h4>
                <div className="row">
                  <StatCard title="Total Users" value={stats.users.total} color="primary" />
                  <StatCard title="Farmers" value={stats.users.farmers} color="success" />
                  <StatCard title="Buyers" value={stats.users.buyers} color="info" />
                  <StatCard title="Admins" value={stats.users.admins} color="warning" />
                </div>

                {/* Produce Statistics */}
                <h4 className="mb-3 mt-4">Produce Statistics</h4>
                <div className="row">
                  <StatCard title="Total Produce" value={stats.produce.total} color="primary" />
                  <StatCard title="Approved" value={stats.produce.approved} color="success" />
                  <StatCard title="Pending" value={stats.produce.pending} color="warning" />
                  <StatCard title="Organic" value={stats.produce.organic} color="info" />
                </div>

                {/* Logistics Statistics */}
                <h4 className="mb-3 mt-4">Logistics Partners</h4>
                <div className="row">
                  <StatCard title="Total Partners" value={stats.logistics.total} color="primary" />
                  <StatCard title="Verified" value={stats.logistics.verified} color="success" />
                  <StatCard title="Pending" value={stats.logistics.pending} color="warning" />
                  <StatCard title="Orders" value={stats.orders.total} color="info" />
                </div>
              </div>
            )}

            {/* Pending Approvals Tab */}
            {activeTab === 'approvals' && pendingApprovals && (
              <div>
                <h2 className="mb-4">Pending Approvals</h2>
                
                {/* Pending Produce */}
                <h4 className="mb-3">Pending Produce ({pendingApprovals.produce.length})</h4>
                {pendingApprovals.produce.length > 0 ? (
                  pendingApprovals.produce.map(item => (
                    <ApprovalCard 
                      key={item.id} 
                      item={item} 
                      type="produce" 
                      onApprove={handleApproveItem} 
                    />
                  ))
                ) : (
                  <div className="alert alert-info">No pending produce approvals</div>
                )}

                {/* Pending Logistics Partners */}
                <h4 className="mb-3 mt-4">Pending Logistics Partners ({pendingApprovals.logistics_partners.length})</h4>
                {pendingApprovals.logistics_partners.length > 0 ? (
                  pendingApprovals.logistics_partners.map(item => (
                    <ApprovalCard 
                      key={item.id} 
                      item={item} 
                      type="logistics" 
                      onApprove={handleApproveItem} 
                    />
                  ))
                ) : (
                  <div className="alert alert-info">No pending logistics partner approvals</div>
                )}
              </div>
            )}

            {/* User Management Tab */}
            {activeTab === 'users' && (
              <div>
                <h2 className="mb-4">User Management</h2>
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {allUsers.map(user => (
                        <tr key={user.id}>
                          <td>{user.username}</td>
                          <td>{user.email}</td>
                          <td>
                            {user.is_staff ? 'Admin' : 
                             user.is_farmer ? 'Farmer' : 
                             user.is_buyer ? 'Buyer' : 'User'}
                          </td>
                          <td>
                            <span className={`badge ${user.is_active ? 'bg-success' : 'bg-danger'}`}>
                              {user.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td>{new Date(user.created_at).toLocaleDateString()}</td>
                          <td>
                            <button className="btn btn-sm btn-outline-primary me-1">
                              Edit
                            </button>
                            <button className="btn btn-sm btn-outline-warning">
                              {user.is_active ? 'Deactivate' : 'Activate'}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Reports Tab */}
            {activeTab === 'reports' && (
              <div>
                <h2 className="mb-4">Reports & Analytics</h2>
                <div className="row">
                  <div className="col-md-6">
                    <div className="card">
                      <div className="card-header">
                        <h5>Quick Reports</h5>
                      </div>
                      <div className="card-body">
                        <button className="btn btn-outline-primary d-block mb-2 w-100">
                          📊 User Activity Report
                        </button>
                        <button className="btn btn-outline-success d-block mb-2 w-100">
                          🌾 Produce Performance Report
                        </button>
                        <button className="btn btn-outline-info d-block mb-2 w-100">
                          🚚 Logistics Report
                        </button>
                        <button className="btn btn-outline-warning d-block w-100">
                          💰 Revenue Report
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="card">
                      <div className="card-header">
                        <h5>System Health</h5>
                      </div>
                      <div className="card-body">
                        <div className="d-flex justify-content-between mb-2">
                          <span>Database Status:</span>
                          <span className="badge bg-success">Healthy</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>API Response Time:</span>
                          <span className="badge bg-success">Fast</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Active Sessions:</span>
                          <span className="badge bg-info">{stats?.users?.total || 0}</span>
                        </div>
                        <div className="d-flex justify-content-between">
                          <span>System Load:</span>
                          <span className="badge bg-success">Normal</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ComprehensiveAdminDashboard;
