#!/usr/bin/env python3
"""
Quick script to check if Django and React servers are running
"""

import requests
import subprocess
import sys
import time

def check_django_server():
    """Check if Django server is running"""
    print("🔍 Checking Django Server...")
    
    try:
        response = requests.get("http://12*******:8000/api/", timeout=5)
        print(f"✅ Django server is running!")
        print(f"   Status: {response.status_code}")
        print(f"   URL: http://12*******:8000/api/")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Django server is NOT running!")
        print("   Error: Connection refused")
        print("   Solution: Run 'python manage.py runserver 12*******:8000' in backend directory")
        return False
    except requests.exceptions.Timeout:
        print("❌ Django server is slow to respond!")
        print("   Error: Request timeout")
        return False
    except Exception as e:
        print(f"❌ Django server error: {e}")
        return False

def check_react_server():
    """Check if React server is running"""
    print("\n🔍 Checking React Server...")
    
    try:
        response = requests.get("http://12*******:5173/", timeout=5)
        print(f"✅ React server is running!")
        print(f"   Status: {response.status_code}")
        print(f"   URL: http://12*******:5173/")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ React server is NOT running!")
        print("   Error: Connection refused")
        print("   Solution: Run 'npm run dev' in frontend/agrimarket_frontend directory")
        return False
    except requests.exceptions.Timeout:
        print("❌ React server is slow to respond!")
        print("   Error: Request timeout")
        return False
    except Exception as e:
        print(f"❌ React server error: {e}")
        return False

def test_login_endpoint():
    """Test the login endpoint specifically"""
    print("\n🧪 Testing Login Endpoint...")
    
    try:
        # Test with admin credentials
        login_data = {
            "username": "admin",
            "password": "admin123",
            "role": "admin"
        }
        
        response = requests.post(
            "http://12*******:8000/api/login/",
            json=login_data,
            timeout=10
        )
        
        print(f"✅ Login endpoint is accessible!")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Login successful with admin credentials!")
            data = response.json()
            print(f"   Token received: {bool(data.get('token'))}")
            print(f"   User data: {bool(data.get('user'))}")
        elif response.status_code == 401:
            print("⚠️  Login endpoint works but credentials invalid")
            print("   This is normal if admin user doesn't exist")
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to login endpoint!")
        print("   Make sure Django server is running")
        return False
    except Exception as e:
        print(f"❌ Login endpoint error: {e}")
        return False

def check_ports():
    """Check what's running on the expected ports"""
    print("\n🔍 Checking Ports...")
    
    import socket
    
    def check_port(host, port, service):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open ({service})")
            return True
        else:
            print(f"❌ Port {port} is closed ({service})")
            return False
    
    django_running = check_port("12*******", 8000, "Django")
    react_running = check_port("12*******", 5173, "React")
    
    return django_running, react_running

def main():
    """Main function"""
    print("🚀 AgriMarket Server Status Check")
    print("=" * 50)
    
    # Check ports first
    django_port, react_port = check_ports()
    
    # Check Django server
    django_ok = check_django_server()
    
    # Check React server
    react_ok = check_react_server()
    
    # Test login endpoint if Django is running
    if django_ok:
        test_login_endpoint()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    if django_ok and react_ok:
        print("🎉 Both servers are running correctly!")
        print("\n✅ You can now test login at:")
        print("   http://12*******:5173/login")
        
    elif django_ok and not react_ok:
        print("⚠️  Django is running but React is not")
        print("\n🔧 To start React server:")
        print("   cd frontend/agrimarket_frontend")
        print("   npm run dev")
        
    elif not django_ok and react_ok:
        print("⚠️  React is running but Django is not")
        print("\n🔧 To start Django server:")
        print("   cd backend")
        print("   python manage.py runserver 12*******:8000")
        
    else:
        print("❌ Neither server is running!")
        print("\n🔧 To start both servers:")
        print("\n   Terminal 1 (Django):")
        print("   cd backend")
        print("   python manage.py runserver 12*******:8000")
        print("\n   Terminal 2 (React):")
        print("   cd frontend/agrimarket_frontend")
        print("   npm run dev")
    
    print("\n🔍 If servers are running but login still fails:")
    print("   1. Check browser console for JavaScript errors")
    print("   2. Check Django terminal for server errors")
    print("   3. Try clearing browser cache")
    print("   4. Try incognito/private browsing mode")

if __name__ == "__main__":
    main()
