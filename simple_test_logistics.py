#!/usr/bin/env python3
"""
Simple test for logistics registration using only built-in Python modules
"""

import urllib.request
import urllib.parse
import json
import sys

def test_endpoint(url, data=None, method="GET"):
    """Test an endpoint using urllib"""
    try:
        if data:
            # POST request
            data_encoded = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(
                url, 
                data=data_encoded,
                headers={'Content-Type': 'application/json'}
            )
        else:
            # GET request
            req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req, timeout=10) as response:
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            try:
                json_data = json.loads(response_data)
                return status_code, json_data
            except:
                return status_code, response_data
                
    except urllib.error.HTTPError as e:
        try:
            error_data = e.read().decode('utf-8')
            try:
                json_error = json.loads(error_data)
                return e.code, json_error
            except:
                return e.code, error_data
        except:
            return e.code, str(e)
    except Exception as e:
        return None, str(e)

def main():
    """Main test function"""
    print("🔧 Simple Logistics Registration Test")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("🔍 Testing server connection...")
    status, data = test_endpoint("http://127.0.0.1:8000/api/categories/")
    
    if status == 200:
        print("✅ Django server is running")
    elif status is None:
        print("❌ Cannot connect to Django server")
        print("   Make sure it's running: python manage.py runserver 127.0.0.1:8000")
        sys.exit(1)
    else:
        print(f"⚠️  Server responded with status {status}")
    
    # Test 2: Check regions
    print("\n🌍 Testing regions endpoint...")
    status, data = test_endpoint("http://127.0.0.1:8000/api/regions/")
    
    if status == 200:
        if isinstance(data, dict) and 'results' in data:
            regions = data['results']
        elif isinstance(data, list):
            regions = data
        else:
            regions = []
        
        print(f"✅ Regions endpoint working - {len(regions)} regions found")
        
        if len(regions) == 0:
            print("⚠️  No regions found!")
            print("   Run: cd backend && python create_regions_simple.py")
            sys.exit(1)
        
        # Show sample regions
        print("📋 Sample regions:")
        for region in regions[:3]:
            print(f"   - {region.get('name')} ({region.get('type')})")
        
        # Use first region for test
        test_region = regions[0]
    else:
        print(f"❌ Regions endpoint failed with status {status}")
        print(f"   Response: {data}")
        sys.exit(1)
    
    # Test 3: Test logistics registration
    print("\n🧪 Testing logistics registration...")
    
    test_data = {
        "name": "Test Logistics Company",
        "description": "A test logistics company",
        "contact_phone": "+263771234567",
        "email": "<EMAIL>",
        "base_rate": 15.50,
        "website": "https://testlogistics.co.zw",
        "service_regions": [
            {
                "id": test_region["id"],
                "name": test_region["name"],
                "type": test_region["type"]
            }
        ]
    }
    
    print(f"📦 Test data:")
    print(f"   Name: {test_data['name']}")
    print(f"   Email: {test_data['email']}")
    print(f"   Phone: {test_data['contact_phone']}")
    print(f"   Region: {test_region['name']} ({test_region['type']})")
    
    status, response = test_endpoint(
        "http://127.0.0.1:8000/api/logistics-partners/register/",
        test_data
    )
    
    print(f"\n📊 Response Status: {status}")
    print(f"📋 Response Data:")
    if isinstance(response, dict):
        print(json.dumps(response, indent=2))
    else:
        print(response)
    
    if status == 201:
        print("\n✅ SUCCESS! Logistics registration is working!")
        print("\n🎉 The 400 error has been fixed!")
        print("\n📋 Next steps:")
        print("1. Test the frontend form: http://127.0.0.1:5173/logistics-register")
        print("2. Fill out all required fields")
        print("3. Select at least one region")
        print("4. Submit the form")
    elif status == 400:
        print("\n❌ Still getting 400 Bad Request")
        print("🔧 Check the error details above for specific field issues")
    else:
        print(f"\n⚠️  Unexpected status: {status}")
        print("🔧 Check Django server logs for more details")

if __name__ == "__main__":
    main()
