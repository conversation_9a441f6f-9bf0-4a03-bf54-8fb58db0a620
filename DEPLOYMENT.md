# AgriMarket Deployment Guide

This guide covers deploying the AgriMarket platform to production environments.

## 🚀 Production Deployment

### Prerequisites
- Ubuntu 20.04+ or similar Linux distribution
- Python 3.10+
- Node.js 18+
- PostgreSQL 12+
- Nginx
- SSL certificate (Let's Encrypt recommended)

### Backend Deployment (Django)

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install python3-pip python3-venv postgresql postgresql-contrib nginx supervisor -y

# Create application user
sudo adduser agrimarket
sudo usermod -aG sudo agrimarket
```

#### 2. Database Setup
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE agrimarket;
CREATE USER agrimarket_user WITH PASSWORD 'your_secure_password';
ALTER ROLE agrimarket_user SET client_encoding TO 'utf8';
ALTER ROLE agrimarket_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE agrimarket_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE agrimarket TO agrimarket_user;
\q
```

#### 3. Application Setup
```bash
# Switch to application user
sudo su - agrimarket

# Clone repository
git clone https://github.com/yourusername/agrimarket.git
cd agrimarket

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
cd backend
pip install -r requirements.txt
pip install gunicorn psycopg2-binary

# Configure environment
cp ../.env.example .env
# Edit .env with production settings
```

#### 4. Django Configuration
```bash
# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

#### 5. Gunicorn Configuration
Create `/home/<USER>/agrimarket/backend/gunicorn.conf.py`:
```python
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

#### 6. Supervisor Configuration
Create `/etc/supervisor/conf.d/agrimarket.conf`:
```ini
[program:agrimarket]
command=/home/<USER>/agrimarket/venv/bin/gunicorn agrimarket_backend.wsgi:application -c gunicorn.conf.py
directory=/home/<USER>/agrimarket/backend
user=agrimarket
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/agrimarket.log
```

### Frontend Deployment (React)

#### 1. Build Frontend
```bash
# On your local machine or CI/CD
cd frontend/agrimarket_frontend
npm install
npm run build

# Upload dist folder to server
scp -r dist/ agrimarket@your-server:/home/<USER>/agrimarket/frontend/
```

### Nginx Configuration

Create `/etc/nginx/sites-available/agrimarket`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Frontend
    location / {
        root /home/<USER>/agrimarket/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Django Admin
    location /admin/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files
    location /static/ {
        alias /home/<USER>/agrimarket/backend/staticfiles/;
    }
    
    # Media files
    location /media/ {
        alias /home/<USER>/agrimarket/backend/media/;
    }
}
```

#### Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/agrimarket /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Start Services

```bash
# Start supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start agrimarket

# Start nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## 🐳 Docker Deployment (Alternative)

### Docker Compose Setup

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: agrimarket
      POSTGRES_USER: agrimarket_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data/

  backend:
    build: ./backend
    command: gunicorn agrimarket_backend.wsgi:application --bind 0.0.0.0:8000
    volumes:
      - ./backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=*********************************************************/agrimarket

  frontend:
    build: ./frontend/agrimarket_frontend
    volumes:
      - ./frontend/agrimarket_frontend/dist:/usr/share/nginx/html

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

### Deploy with Docker
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 Monitoring & Maintenance

### Log Monitoring
```bash
# Application logs
sudo tail -f /var/log/agrimarket.log

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Database Backup
```bash
# Create backup script
#!/bin/bash
pg_dump -h localhost -U agrimarket_user agrimarket > backup_$(date +%Y%m%d_%H%M%S).sql

# Schedule with cron
0 2 * * * /home/<USER>/backup.sh
```

### Updates
```bash
# Update application
cd /home/<USER>/agrimarket
git pull origin main
source venv/bin/activate
pip install -r backend/requirements.txt
python backend/manage.py migrate
python backend/manage.py collectstatic --noinput
sudo supervisorctl restart agrimarket
```

## 🔒 Security Checklist

- [ ] Use strong passwords for database and admin accounts
- [ ] Enable firewall (UFW) and close unnecessary ports
- [ ] Keep system and dependencies updated
- [ ] Use HTTPS with valid SSL certificates
- [ ] Configure proper CORS settings
- [ ] Set up regular backups
- [ ] Monitor logs for suspicious activity
- [ ] Use environment variables for sensitive data
- [ ] Enable Django security middleware
- [ ] Configure rate limiting

## 📈 Performance Optimization

- Use Redis for caching
- Configure database connection pooling
- Optimize static file serving
- Enable gzip compression
- Use CDN for media files
- Monitor application performance
- Set up database indexing
- Configure proper logging levels

---

For additional support, refer to the main README.md or create an issue in the repository.