#!/usr/bin/env python3
"""
Test the categories endpoint specifically
"""

import requests
import json

def test_categories():
    """Test the categories endpoint"""
    url = "http://127.0.0.1:8000/api/categories/"
    
    print(f"🧪 Testing: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Data: {json.dumps(data, indent=2)}")
                return True
            except json.JSONDecodeError:
                print(f"✅ Success but not JSON: {response.text}")
                return True
        else:
            print(f"❌ Failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is Django server running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Categories Endpoint")
    print("=" * 40)
    
    success = test_categories()
    
    if success:
        print("\n🎉 Categories endpoint is working!")
        print("The frontend should now connect successfully.")
    else:
        print("\n❌ Categories endpoint is not working.")
        print("Please check:")
        print("1. Django server is running: python manage.py runserver")
        print("2. No errors in Django terminal")
        print("3. Migrations have been run: python manage.py migrate")
