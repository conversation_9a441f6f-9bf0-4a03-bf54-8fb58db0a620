#!/usr/bin/env python3
"""
Complete AgriMarket Setup Script
This script will set up the entire AgriMarket project from scratch.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a command and handle errors"""
    try:
        print(f"🔧 Running: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=check)
        
        if result.stdout.strip():
            print(f"✅ Output: {result.stdout.strip()}")
        
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False, e.stdout, e.stderr
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, "", str(e)

def setup_backend():
    """Set up the Django backend"""
    print("\n🔧 Setting up Backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    # Check if virtual environment exists
    venv_dir = backend_dir / "myenv"
    if venv_dir.exists():
        print("✅ Virtual environment found")
        if platform.system() == "Windows":
            python_exe = venv_dir / "Scripts" / "python.exe"
            pip_exe = venv_dir / "Scripts" / "pip.exe"
        else:
            python_exe = venv_dir / "bin" / "python"
            pip_exe = venv_dir / "bin" / "pip"
    else:
        print("⚠️  No virtual environment found, using system Python")
        python_exe = "python"
        pip_exe = "pip"
    
    # Install requirements
    print("📦 Installing requirements...")
    success, _, _ = run_command(f"{pip_exe} install -r requirements.txt", cwd=backend_dir)
    if not success:
        print("⚠️  Requirements installation failed, continuing anyway...")
    
    # Check Django
    print("🔍 Checking Django...")
    success, _, _ = run_command(f"{python_exe} manage.py check", cwd=backend_dir)
    if not success:
        print("❌ Django check failed")
        return False
    
    # Create migrations
    print("🗄️  Creating migrations...")
    run_command(f"{python_exe} manage.py makemigrations", cwd=backend_dir)
    
    # Run migrations
    print("🗄️  Running migrations...")
    success, _, _ = run_command(f"{python_exe} manage.py migrate", cwd=backend_dir)
    if not success:
        print("❌ Migration failed")
        return False
    
    # Create initial data
    print("📊 Creating initial data...")
    create_initial_data(python_exe, backend_dir)
    
    print("✅ Backend setup complete!")
    return True

def create_initial_data(python_exe, backend_dir):
    """Create initial data for the application"""
    script = '''
import os
import django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agrimarket_backend.settings")
django.setup()

from core.models import ProduceCategory, User
from django.contrib.auth import get_user_model

User = get_user_model()

# Create superuser if it doesn't exist
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        is_farmer=False,
        is_buyer=False
    )
    print("Created superuser: admin/admin123")

# Create basic categories
categories = [
    {"name": "Vegetables", "description": "Fresh vegetables and leafy greens"},
    {"name": "Fruits", "description": "Fresh fruits and berries"},
    {"name": "Grains", "description": "Cereals, rice, wheat, and other grains"},
    {"name": "Herbs", "description": "Herbs, spices, and medicinal plants"},
    {"name": "Livestock", "description": "Cattle, poultry, and other livestock"},
    {"name": "Dairy", "description": "Milk, cheese, and dairy products"},
]

for cat_data in categories:
    category, created = ProduceCategory.objects.get_or_create(
        name=cat_data["name"],
        defaults={"description": cat_data["description"]}
    )
    if created:
        print(f"Created category: {category.name}")
    else:
        print(f"Category already exists: {category.name}")

print("✅ Initial data setup complete!")
'''
    
    # Write and run the script
    script_path = backend_dir / "setup_data.py"
    with open(script_path, "w") as f:
        f.write(script)
    
    success, output, error = run_command(f"{python_exe} setup_data.py", cwd=backend_dir)
    
    # Clean up
    try:
        script_path.unlink()
    except:
        pass
    
    if success:
        print("✅ Initial data created")
    else:
        print(f"⚠️  Initial data creation failed: {error}")

def setup_frontend():
    """Set up the React frontend"""
    print("\n🔧 Setting up Frontend...")
    
    frontend_dir = Path("frontend/agrimarket_frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    # Install npm packages
    print("📦 Installing npm packages...")
    success, _, _ = run_command("npm install", cwd=frontend_dir)
    if not success:
        print("❌ npm install failed")
        return False
    
    print("✅ Frontend setup complete!")
    return True

def test_setup():
    """Test if everything is working"""
    print("\n🧪 Testing setup...")
    
    # Test backend
    backend_dir = Path("backend")
    venv_dir = backend_dir / "myenv"
    
    if venv_dir.exists():
        if platform.system() == "Windows":
            python_exe = venv_dir / "Scripts" / "python.exe"
        else:
            python_exe = venv_dir / "bin" / "python"
    else:
        python_exe = "python"
    
    print("🔍 Testing Django...")
    success, _, _ = run_command(f"{python_exe} manage.py check", cwd=backend_dir)
    if success:
        print("✅ Django is working")
    else:
        print("❌ Django test failed")
        return False
    
    # Test if we can import models
    print("🔍 Testing models...")
    test_script = '''
import os, django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agrimarket_backend.settings")
django.setup()
from core.models import ProduceCategory
print(f"Categories: {ProduceCategory.objects.count()}")
'''
    
    script_path = backend_dir / "test_models.py"
    with open(script_path, "w") as f:
        f.write(test_script)
    
    success, output, _ = run_command(f"{python_exe} test_models.py", cwd=backend_dir)
    
    try:
        script_path.unlink()
    except:
        pass
    
    if success:
        print("✅ Models are working")
        print(f"   {output.strip()}")
    else:
        print("❌ Models test failed")
    
    return True

def main():
    """Main setup function"""
    print("🌾 AgriMarket Complete Setup")
    print("=" * 50)
    
    # Check current directory
    if not Path("backend").exists() or not Path("frontend").exists():
        print("❌ Please run this script from the AgriMarket root directory")
        sys.exit(1)
    
    # Setup backend
    if not setup_backend():
        print("❌ Backend setup failed!")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("❌ Frontend setup failed!")
        sys.exit(1)
    
    # Test everything
    test_setup()
    
    print("\n" + "=" * 50)
    print("🎉 Setup Complete!")
    print("\n📋 Next steps:")
    print("1. Start backend:")
    print("   cd backend")
    if Path("backend/myenv").exists():
        if platform.system() == "Windows":
            print("   myenv\\Scripts\\activate")
        else:
            print("   source myenv/bin/activate")
    print("   python manage.py runserver 127.0.0.1:8000")
    print("\n2. Start frontend (in new terminal):")
    print("   cd frontend/agrimarket_frontend")
    print("   npm run dev")
    print("\n3. Access the application:")
    print("   🌐 Frontend: http://127.0.0.1:5173")
    print("   🔧 Backend: http://127.0.0.1:8000")
    print("   📚 API Docs: http://127.0.0.1:8000/swagger/")
    print("   👤 Admin: http://127.0.0.1:8000/admin/ (admin/admin123)")

if __name__ == "__main__":
    main()
