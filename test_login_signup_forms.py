#!/usr/bin/env python3
"""
Quick test script to verify login and signup endpoints are working
"""

import requests
import json

# Base URL for the API
BASE_URL = "http://127.0.0.1:8000/api"

def test_register_endpoint():
    """Test the registration endpoint"""
    print("🧪 Testing Registration Endpoint...")
    
    # Test data
    test_user = {
        "username": "testuser_" + str(int(__import__('time').time())),
        "email": "<EMAIL>",
        "password": "testpass123",
        "confirm_password": "testpass123",
        "is_farmer": True,
        "is_buyer": False
    }
    
    try:
        response = requests.post(f"{BASE_URL}/register/", json=test_user)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 201:
            print("✅ Registration endpoint is working!")
            return test_user["username"]
        else:
            print("❌ Registration failed")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server. Make sure it's running on port 8000")
        return None
    except Exception as e:
        print(f"❌ Error testing registration: {e}")
        return None

def test_login_endpoint(username=None):
    """Test the login endpoint"""
    print("\n🧪 Testing Login Endpoint...")
    
    # Use admin credentials or the newly created user
    if username:
        login_data = {
            "username": username,
            "password": "testpass123"
        }
    else:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
    
    try:
        response = requests.post(f"{BASE_URL}/login/", json=login_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Login endpoint is working!")
            return True
        else:
            print("❌ Login failed")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server. Make sure it's running on port 8000")
        return False
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return False

def test_invalid_login():
    """Test login with invalid credentials"""
    print("\n🧪 Testing Invalid Login...")
    
    invalid_data = {
        "username": "nonexistentuser",
        "password": "wrongpassword"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/login/", json=invalid_data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 401:
            print("✅ Invalid login properly rejected!")
            return True
        else:
            print("❌ Invalid login should return 401")
            return False
            
    except Exception as e:
        print(f"❌ Error testing invalid login: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Login and Signup Form Functionality")
    print("=" * 50)
    
    # Test registration
    new_username = test_register_endpoint()
    
    # Test login with admin
    test_login_endpoint()
    
    # Test login with new user (if registration worked)
    if new_username:
        test_login_endpoint(new_username)
    
    # Test invalid login
    test_invalid_login()
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("If you see ✅ for all tests, the forms should be working correctly!")
    print("If you see ❌, there might be issues with the backend or database.")
    print("\nTo test the frontend forms:")
    print("1. Go to: http://127.0.0.1:5173/register")
    print("2. Fill out the form and submit")
    print("3. Go to: http://127.0.0.1:5173/login")
    print("4. Login with your credentials")

if __name__ == "__main__":
    main()
