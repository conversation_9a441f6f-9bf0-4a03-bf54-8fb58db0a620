/* Modern AgriMarket Theme 2025 */

/* ===== NAVBAR SPACING FIX ===== */
/* Fix navbar overlap with page content */
.navbar.fixed-top {
  z-index: 1030;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Add top padding to body to account for fixed navbar */
body {
  padding-top: 80px !important; /* Adjust based on navbar height */
}

/* Responsive navbar spacing */
@media (max-width: 768px) {
  body {
    padding-top: 70px !important; /* Smaller padding on mobile */
  }
}

/* Page container spacing */
.container, .container-fluid {
  margin-top: 0; /* Remove any conflicting margins */
}

/* Specific page content spacing */
.page-content {
  padding-top: 20px;
  min-height: calc(100vh - 80px);
}

/* Dashboard and admin pages */
.dashboard-content {
  padding-top: 20px;
}

/* Form pages spacing */
.form-container {
  margin-top: 20px;
}

/* Card spacing for pages */
.main-card {
  margin-top: 20px;
}

/* Theme Variables */
:root,
[data-bs-theme="light"] {
  /* Primary Colors */
  --bs-primary: #2E7D32;
  --bs-primary-rgb: 46, 125, 50;
  --bs-secondary: #66BB6A;
  --bs-secondary-rgb: 102, 187, 106;
  --bs-success: #43A047;
  --bs-success-rgb: 67, 160, 71;
  
  /* Accent Colors */
  --bs-accent: #FFB74D;
  --bs-accent-rgb: 255, 183, 77;
  --bs-warning: #F9A825;
  --bs-warning-rgb: 249, 168, 37;
  --bs-info: #29B6F6;
  --bs-info-rgb: 41, 182, 246;
  --bs-danger: #EF5350;
  --bs-danger-rgb: 239, 83, 80;

  /* UI Colors */
  --bs-light: #F5F5F5;
  --bs-light-rgb: 245, 245, 245;
  --bs-dark: #263238;
  --bs-dark-rgb: 38, 50, 56;
  
  /* Modern UI Colors */
  --bs-body-bg: #F8FAFC;
  --bs-body-color: #1E293B;
  --bs-navbar-bg: rgba(255, 255, 255, 0.95);
  --bs-card-bg: rgba(255, 255, 255, 0.9);
  --bs-input-bg: rgba(255, 255, 255, 0.9);
  --bs-input-color: #1E293B;
  
  /* Glass Effect */
  --glass-border: 1px solid rgba(255, 255, 255, 0.4);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

[data-bs-theme="dark"] {
  --bs-primary: #66BB6A;
  --bs-primary-rgb: 102, 187, 106;
  --bs-secondary: #81C784;
  --bs-secondary-rgb: 129, 199, 132;
  
  /* Modern Dark UI */
  --bs-body-bg: #0F172A;
  --bs-body-color: #E2E8F0;
  --bs-navbar-bg: rgba(15, 23, 42, 0.95);
  --bs-card-bg: rgba(30, 41, 59, 0.9);
  --bs-input-bg: rgba(30, 41, 59, 0.9);
  --bs-input-color: #E2E8F0;
  
  /* Dark Glass Effect */
  --glass-border: 1px solid rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Animation Variables */
:root {
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  --transition-slide: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}

/* Modern Components */

/* Navbar */
.navbar {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.navbar-brand {
  font-weight: 700;
  letter-spacing: -0.5px;
}

.brand-title {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Cards */
.card {
  background: var(--bs-card-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: var(--transition-smooth);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.card:hover {
  transform: translateY(-8px) translateZ(0);
  box-shadow: 0 16px 32px -4px rgba(var(--bs-primary-rgb), 0.15);
}

@media (max-width: 768px) {
  .card:hover {
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 8px 16px -4px rgba(var(--bs-primary-rgb), 0.1);
  }
}

/* --- Enhanced Responsive Card, Image, and Layout Styles --- */
.card, .collection-item, .stats-card, .newsletter-container {
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  padding: clamp(0.75rem, 2vw, 2rem);
}

.card-img-top, .produce-image, .collection-image-container img {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: cover;
  border-radius: 1rem;
  min-height: 120px;
  max-height: 320px;
}

@media (max-width: 768px) {
  .card, .collection-item, .stats-card, .newsletter-container {
    padding: clamp(0.5rem, 2vw, 1.25rem);
    font-size: 0.95rem;
  }
  .card-img-top, .produce-image, .collection-image-container img {
    min-height: 100px;
    max-height: 200px;
  }
}

@media (max-width: 576px) {
  .card, .collection-item, .stats-card, .newsletter-container {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }
  .card-img-top, .produce-image, .collection-image-container img {
    min-height: 80px;
    max-height: 140px;
  }
  .brand-title, .display-6, h2, h3, h4 {
    font-size: 1.1rem !important;
  }
  .navbar {
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 400px) {
  .card, .collection-item, .stats-card, .newsletter-container {
    padding: 0.25rem 0.1rem;
    font-size: 0.78rem;
  }
  .card-img-top, .produce-image, .collection-image-container img {
    min-height: 60px;
    max-height: 100px;
  }
}

/* Responsive Typography */
.card h5, .collection-item h6, .stats-value {
  font-size: clamp(1rem, 2vw, 1.25rem);
}

/* Responsive Padding & Spacing */
.card, .collection-item, .stats-card, .newsletter-container {
  padding: clamp(1rem, 3vw, 2rem);
}

/* Buttons */
.btn {
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  transition: var(--transition-bounce);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.btn-primary {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-secondary));
  border: none;
  box-shadow: 0 4px 15px rgba(var(--bs-primary-rgb), 0.2);
}

.btn-primary:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow: 0 8px 16px rgba(var(--bs-primary-rgb), 0.25);
}

.btn:active {
  transform: scale(0.95) translateZ(0);
  transition-duration: 0.1s;
}

@media (max-width: 768px) {
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Tables */
.table-responsive {
  background: var(--bs-card-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 16px;
  border: var(--glass-border);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  margin: 0 -1rem;
  padding: 0 1rem;
  transition: var(--transition-smooth);
}

.table {
  margin-bottom: 0;
}

.table thead th {
  background: linear-gradient(
    to bottom,
    rgba(var(--bs-primary-rgb), 0.1) 0%,
    rgba(var(--bs-primary-rgb), 0.05) 100%
  );
  color: var(--bs-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 1px;
  padding: 1rem 1.5rem;
  border-bottom: 2px solid rgba(var(--bs-primary-rgb), 0.1);
}

.table tbody td {
  padding: 1.25rem 1.5rem;
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.05);
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background: linear-gradient(
    to right,
    rgba(var(--bs-primary-rgb), 0.05) 0%,
    rgba(var(--bs-primary-rgb), 0.02) 100%
  );
}

@media (max-width: 768px) {
  .table thead th {
    padding: 0.75rem;
    font-size: 0.7rem;
  }

  .table tbody td {
    padding: 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Forms */
.form-control {
  background: var(--bs-input-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  transition: var(--transition-smooth);
  will-change: transform, box-shadow;
}

.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 4px rgba(var(--bs-primary-rgb), 0.1);
  transform: translateY(-1px);
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 4px 16px -2px rgba(var(--bs-primary-rgb), 0.15);
}

@media (max-width: 768px) {
  .form-control {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* Stats Cards */
.stats-card {
  background: linear-gradient(135deg,
    rgba(var(--bs-primary-rgb), 0.1) 0%,
    rgba(var(--bs-secondary-rgb), 0.1) 100%
  );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  transition: var(--transition-bounce);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.stats-card:hover {
  transform: translateY(-6px) scale(1.02) translateZ(0);
}

@media (max-width: 768px) {
  .stats-card {
    padding: 1rem;
  }
  
  .stats-value {
    font-size: 1.5rem;
  }
}

/* Collection Items */
.collection-item {
  background: var(--bs-card-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  transition: var(--transition-slide);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.collection-item:hover {
  transform: translateX(8px) translateZ(0);
}

@media (max-width: 768px) {
  .collection-item {
    padding: 1rem;
  }
  
  .collection-item:hover {
    transform: translateX(4px) translateZ(0);
  }
}

/* Alerts */
.alert {
  background: var(--bs-card-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-weight: 500;
  animation: slideIn 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform, opacity;
}

.alert-danger {
  border-left: 4px solid var(--bs-danger);
  color: var(--bs-danger);
}

.alert-success {
  border-left: 4px solid var(--bs-success);
  color: var(--bs-success);
}

/* Loading States */
.spinner-border {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 0.25rem;
  color: var(--bs-primary) !important;
  animation: spin 0.8s linear infinite;
  will-change: transform;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Search Components */
.search-input {
  background: var(--bs-input-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 16px;
  padding: 1rem 3rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  transition: var(--transition-smooth);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.search-input:focus {
  transform: scale(1.01) translateZ(0);
}

.search-icon {
  color: var(--bs-primary);
  opacity: 0.7;
}

/* Newsletter Section */
.newsletter-container {
  background: linear-gradient(135deg,
    rgba(var(--bs-primary-rgb), 0.05) 0%,
    rgba(var(--bs-secondary-rgb), 0.05) 100%
  );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: var(--glass-border);
  border-radius: 24px;
  padding: 3rem;
  position: relative;
  overflow: hidden;
  transition: var(--transition-smooth);
}

@media (max-width: 768px) {
  .newsletter-container {
    padding: 2rem 1rem;
    border-radius: 20px;
  }
}

/* Restore Material Icons and Font Awesome visibility and style */
.material-icons, .navbar-icon, .fa, [class^="fa-"], [class*=" fa-"] {
  font-family: 'Material Icons', 'FontAwesome', Arial, sans-serif !important;
  font-style: normal;
  font-weight: normal;
  font-size: 1.25em;
  line-height: 1;
  vertical-align: middle;
  display: inline-block;
  color: inherit;
  opacity: 1;
  transition: color 0.2s;
}

.navbar .material-icons, .navbar .navbar-icon, .navbar .fa {
  color: var(--bs-primary);
  opacity: 0.85;
}

.navbar .nav-link:hover .material-icons,
.navbar .nav-link:hover .navbar-icon,
.navbar .nav-link:hover .fa {
  color: var(--bs-secondary);
  opacity: 1;
}

/* ===== NAVBAR LINK COLOR CONSISTENCY ===== */
/* Make all navbar links have consistent colors */
.navbar .nav-link {
  color: var(--bs-dark) !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.navbar .nav-link:hover,
.navbar .nav-link:focus {
  color: var(--bs-primary) !important;
  transform: translateY(-1px);
}

/* Dropdown toggle links (Marketplace, Services, Role Hubs) */
.navbar .nav-link.dropdown-toggle {
  color: var(--bs-dark) !important;
  font-weight: 500;
}

.navbar .nav-link.dropdown-toggle:hover,
.navbar .nav-link.dropdown-toggle:focus {
  color: var(--bs-primary) !important;
}

/* Ensure dropdown toggle arrows match */
.navbar .nav-link.dropdown-toggle::after {
  color: inherit;
}

/* Login/Register and About links - keep consistent */
.navbar .nav-link:not(.dropdown-toggle) {
  color: var(--bs-dark) !important;
}

.navbar .nav-link:not(.dropdown-toggle):hover {
  color: var(--bs-primary) !important;
}

/* Active/current page styling */
.navbar .nav-link.active {
  color: var(--bs-primary) !important;
  font-weight: 600;
}

/* Brand link styling */
.navbar-brand {
  color: var(--bs-dark) !important;
}

.navbar-brand:hover {
  color: var(--bs-primary) !important;
}

/* ===== SPECIFIC NAVBAR LINK TARGETING ===== */
/* Ensure Marketplace and Services dropdowns match other links */
.navbar .nav-item .nav-link[href="#"],
.navbar .nav-item .nav-link.dropdown-toggle {
  color: var(--bs-dark) !important;
  font-weight: 500;
  text-decoration: none;
}

.navbar .nav-item .nav-link[href="#"]:hover,
.navbar .nav-item .nav-link.dropdown-toggle:hover {
  color: var(--bs-primary) !important;
}

/* Login, Register, About links */
.navbar .nav-item .nav-link[href*="login"],
.navbar .nav-item .nav-link[href*="register"],
.navbar .nav-item .nav-link[href*="about"] {
  color: var(--bs-dark) !important;
  font-weight: 500;
}

.navbar .nav-item .nav-link[href*="login"]:hover,
.navbar .nav-item .nav-link[href*="register"]:hover,
.navbar .nav-item .nav-link[href*="about"]:hover {
  color: var(--bs-primary) !important;
}

/* Remove any conflicting Bootstrap navbar styles */
.navbar-nav .nav-link {
  color: var(--bs-dark) !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
  color: var(--bs-primary) !important;
}

/* Ensure icons in navbar links match text color */
.navbar .nav-link .material-icons,
.navbar .nav-link .navbar-icon {
  color: inherit !important;
  opacity: 0.8;
}

.navbar .nav-link:hover .material-icons,
.navbar .nav-link:hover .navbar-icon {
  color: inherit !important;
  opacity: 1;
}

.btn .material-icons, .btn .fa {
  margin-right: 0.5em;
  font-size: 1.1em;
  vertical-align: middle;
}

/* Prevent global font-family from overriding icon fonts */
.material-icons, .fa, [class^="fa-"], [class*=" fa-"] {
  font-family: inherit;
} 

/* Dark Mode Enhancements */
[data-bs-theme="dark"] {
  .card,
  .table-responsive,
  .collection-item,
  .alert,
  .search-input,
  .newsletter-container {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .table thead th {
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
  }

  .table tbody tr:hover {
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
  }

  .form-control {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .btn-primary {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }

  .btn-primary:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* Smooth Page Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: var(--transition-smooth);
}

.page-transition-exit {
  opacity: 1;
}

.page-transition-exit-active {
  opacity: 0;
  transition: var(--transition-smooth);
}

/* Print Media Optimization */
@media print {
  * {
    transition: none !important;
    animation: none !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
  }
}

/* Performance Optimizations */
* {
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Grid Layout Responsiveness */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
  gap: 1.5rem;
  transition: var(--transition-smooth);
}

@media (max-width: 576px) {
  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Enhanced Mobile Navigation */
@media (max-width: 991.98px) {
  .navbar {
    padding: 0.5rem 1rem;
  }

  .navbar-collapse {
    background: var(--bs-navbar-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 1rem;
    margin-top: 0.5rem;
    box-shadow: var(--glass-shadow);
  }

  .nav-link {
    padding: 0.75rem 1rem !important;
    margin: 0.25rem 0;
  }
}

@media (max-width: 576px) {
  .card, .collection-item, .stats-card, .newsletter-container {
    padding: 0.75rem 0.5rem;
    font-size: 0.95rem;
  }
  .card-img-top, .produce-image, .collection-image-container img {
    height: auto;
    min-height: 120px;
    max-height: 220px;
  }
  .brand-title, .display-6, h2, h3, h4 {
    font-size: 1.2rem !important;
  }
  .navbar {
    padding: 0.5rem 0.5rem;
  }
}

@media (max-width: 400px) {
  .card, .collection-item, .stats-card, .newsletter-container {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }
  .card-img-top, .produce-image, .collection-image-container img {
    min-height: 80px;
    max-height: 140px;
  }
}

/* Responsive grid gap for layout containers */
.grid-responsive, .row {
  gap: clamp(0.5rem, 2vw, 2rem);
}
