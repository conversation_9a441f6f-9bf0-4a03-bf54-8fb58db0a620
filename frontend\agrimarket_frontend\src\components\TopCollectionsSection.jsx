import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import collection1 from "../assets/img/collection/1.png";
import collection2 from "../assets/img/collection/2.png";
import collection3 from "../assets/img/collection/3.png";
import collection4 from "../assets/img/collection/4.png";
import collection5 from "../assets/img/collection/5.gif";
import collection6 from "../assets/img/collection/6.gif";
import collection7 from "../assets/img/collection/7.gif";

const timeOptions = [
  "Last 24 Hours",
  "Last 7 Days",
  "Last 30 Days",
];

const collections = [
  {
    id: 1,
    image: collection1,
    name: "Organic Veggie Box",
    owner: "@zimfarmer1",
    count: 12,
    floorPrice: "$8.00",
    floorChange: "+12%",
    floorChangeType: "up",
    volume: "$320.00",
    volumeChange: "+30%",
    volumeChangeType: "up",
    verified: true,
  },
  {
    id: 2,
    image: collection2,
    name: "<PERSON>ze Bulk Deals",
    owner: "@zimfarmer2",
    count: 20,
    floorPrice: "$5.50",
    floorChange: "+8%",
    floorChangeType: "up",
    volume: "$210.00",
    volumeChange: "+15%",
    volumeChangeType: "up",
    verified: false,
  },
  {
    id: 3,
    image: collection3,
    name: "Groundnut Specials",
    owner: "@zimfarmer3",
    count: 18,
    floorPrice: "$6.20",
    floorChange: "-3%",
    floorChangeType: "down",
    volume: "$180.00",
    volumeChange: "-5%",
    volumeChangeType: "down",
    verified: true,
  },
];

function TopCollectionsSection() {
  const [selectedTime, setSelectedTime] = useState(timeOptions[0]);

  return (
    <section className="position-relative py-5">
      <div className="container position-relative">
        <div className="px-4 py-5 py-lg-5 rounded-5 bg-body-tertiary position-relative shadow-sm">
          <div className="d-flex mb-5 flex-column flex-md-row align-items-center justify-content-center">
            <div className="d-flex flex-column flex-sm-row align-items-center">
              <h2 className="mb-3 mb-sm-0 me-sm-3 display-6 text-success">Top Produce Collections over</h2>
              <select 
                className="form-select fs-5 opacity-75 border-0 fw-bold bg-transparent time-select"
                value={selectedTime}
                onChange={e => setSelectedTime(e.target.value)}
              >
                {timeOptions.map(opt => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="row justify-content-center">
            {collections.map(col => (
              <div className="col-lg-4 mb-4" key={col.id}>
                <div className="collection-item">
                  <div className="collection-image-container">
                    <img src={col.image} alt="" className="produce-image" />
                    {col.verified && (
                      <span className="collection-icon-badge text-primary" title="Verified Collection">
                        <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M20 2.7L25.3 0L28.5 5.1L34.7 5.3L34.9 11.5L40 14.7L37.3 20L40 25.3L34.9 28.5L34.7 34.7L28.5 34.9L25.3 40L20 37.3L14.7 40L11.5 34.9L5.3 34.7L5.1 28.5L0 25.3L2.7 20L0 14.7L5.1 11.5L5.3 5.3L11.5 5.1L14.7 0L20 2.7Z"></path>
                          <path d="M17.5 25.5L12 20.6L14.4 18.4L17.5 21.1L26.1 13.5L28.5 15.7L17.5 25.5Z" fill="white"></path>
                        </svg>
                      </span>
                    )}
                  </div>
                  <div className="flex-grow-1 ps-md-4">
                    <h6 className="mb-2 text-truncate">
                      <span className="text-body">{col.name} by</span> <span className="text-primary">{col.owner}</span>
                    </h6>
                    <span className="badge mb-3 bg-body-tertiary text-body">{col.count} Items</span>
                    <span className="d-flex mb-2 pb-2 border-bottom w-100 justify-content-between">
                      <small className="opacity-75">Floor Price:</small>
                      <span className="flex-grow-1 text-end small">
                        {col.floorPrice} <span className={`ms-2 badge bg-transparent text-${col.floorChangeType === 'up' ? 'success' : 'danger'}`}>{col.floorChange}</span>
                      </span>
                    </span>
                    <span className="d-flex w-100 justify-content-between">
                      <small className="opacity-75">Volume:</small>
                      <span className="flex-grow-1 text-end small">
                        {col.volume}<span className={`ms-2 badge px-1 lh-sm bg-${col.volumeChangeType === 'up' ? 'success' : 'danger'}`}>{col.volumeChange}</span>
                      </span>
                    </span>
                  </div>
                  <a href="#" className="stretched-link" tabIndex={-1} aria-label="View Collection Details"></a>
                </div>
              </div>
            ))}
          </div>
          <div className="col-12 pt-4 text-center">
            <Link to="/collections" className="btn btn-outline-success px-lg-4 py-lg-3 hover-lift">
              Explore all collections
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

export default TopCollectionsSection;
