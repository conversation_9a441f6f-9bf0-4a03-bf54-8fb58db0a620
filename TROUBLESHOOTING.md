# AgriMarket Troubleshooting Guide

## Common Issues and Solutions

### 1. Frontend Console Errors

#### Missing JavaScript Files (404 errors)
**Error**: `theme.bundle.min.js`, `jquery.min.js`, etc. not found

**Solution**: ✅ **FIXED** - Removed unnecessary script references from `index.html`

#### ProduceList.jsx Error: "produce.map is not a function"
**Error**: `TypeError: produce.map is not a function`

**Solution**: ✅ **FIXED** - Updated ProduceList component to handle API responses properly

### 2. Backend Issues

#### Missing Requirements
**Error**: Module not found errors

**Solution**: 
```bash
cd backend
pip install -r requirements.txt
```

#### Database Issues
**Error**: Database table doesn't exist

**Solution**:
```bash
cd backend
python manage.py migrate
```

#### No Data in Frontend
**Error**: "No produce available" message

**Solution**:
```bash
cd backend
python manage.py create_sample_data
```

### 3. CORS Issues

#### Frontend Can't Connect to Backend
**Error**: CORS policy errors in browser console

**Solution**: ✅ **FIXED** - CORS is properly configured in Django settings

### 4. Setup Instructions

#### Complete Setup Process

1. **Backend Setup**:
   ```bash
   cd backend
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py create_sample_data
   python manage.py runserver
   ```

2. **Frontend Setup**:
   ```bash
   cd frontend/agrimarket_frontend
   npm install
   npm run dev
   ```

3. **Access the Application**:
   - Frontend: http://localhost:5173
   - Backend API: http://127.0.0.1:8000/api
   - Admin Panel: http://127.0.0.1:8000/admin

### 5. Sample Users

After running `create_sample_data`:
- **farmer1** / password123 (Farmer role)
- **farmer2** / password123 (Farmer role)

### 6. API Testing

Test the API directly:
```bash
# Get all produce
curl http://127.0.0.1:8000/api/produce/

# Get categories
curl http://127.0.0.1:8000/api/categories/
```

### 7. Development Tips

1. **Check Backend is Running**: Visit http://127.0.0.1:8000/api/produce/ in browser
2. **Check Frontend Console**: Open browser dev tools to see any errors
3. **Check Network Tab**: See if API calls are being made successfully
4. **Verify CORS**: Ensure backend allows frontend origin

### 8. File Structure Check

Ensure your project structure looks like this:
```
AgriMarket/
├── backend/
│   ├── manage.py
│   ├── requirements.txt
│   ├── db.sqlite3
│   └── core/
├── frontend/
│   └── agrimarket_frontend/
│       ├── package.json
│       ├── index.html
│       └── src/
└── README.md
```

### 9. Common Commands

```bash
# Backend
cd backend
python manage.py runserver          # Start backend
python manage.py migrate            # Run migrations
python manage.py createsuperuser    # Create admin user
python manage.py create_sample_data  # Add sample data

# Frontend
cd frontend/agrimarket_frontend
npm run dev                         # Start frontend
npm install                         # Install dependencies
```

### 10. Still Having Issues?

1. Check that both servers are running on correct ports
2. Verify no firewall is blocking the connections
3. Clear browser cache and reload
4. Check browser console for detailed error messages
5. Ensure virtual environment is activated for Python