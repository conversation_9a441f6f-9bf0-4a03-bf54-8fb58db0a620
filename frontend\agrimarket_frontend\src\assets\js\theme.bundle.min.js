!function i(o,r,a){function s(t,e){if(!r[t]){if(!o[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(c)return c(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=r[t]={exports:{}},o[t][0].call(n.exports,function(e){return s(o[t][1][e]||e)},n,n.exports,i,o,r,a)}return r[t].exports}for(var c="function"==typeof require&&require,e=0;e<a.length;e++)s(a[e]);return s}({1:[function(t,n,e){"use strict";function b(e){var t;return null==e?window:"[object Window]"!==e.toString()?(t=e.ownerDocument)&&t.defaultView||window:e}function p(e){return e instanceof b(e).Element||e instanceof Element}function c(e){return e instanceof b(e).HTMLElement||e instanceof HTMLElement}function r(e){return"undefined"!=typeof ShadowRoot&&(e instanceof b(e).ShadowRoot||e instanceof ShadowRoot)}Object.defineProperty(e,"__esModule",{value:!0});var T=Math.max,C=Math.min,_=Math.round;function a(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function O(){return!/^((?!chrome|android).)*safari/i.test(a())}function f(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),o=1,r=1;t&&c(e)&&(o=0<e.offsetWidth&&_(i.width)/e.offsetWidth||1,r=0<e.offsetHeight&&_(i.height)/e.offsetHeight||1);t=(p(e)?b(e):window).visualViewport,e=!O()&&n,n=(i.left+(e&&t?t.offsetLeft:0))/o,e=(i.top+(e&&t?t.offsetTop:0))/r,t=i.width/o,o=i.height/r;return{width:t,height:o,top:e,right:n+t,bottom:e+o,left:n,x:n,y:e}}function d(e){e=b(e);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function l(e){return e?(e.nodeName||"").toLowerCase():null}function w(e){return((p(e)?e.ownerDocument:e.document)||window.document).documentElement}function h(e){return f(w(e)).left+d(e).scrollLeft}function k(e){return b(e).getComputedStyle(e)}function u(e){var e=k(e),t=e.overflow,n=e.overflowX,e=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+e+n)}function A(e,t,n){void 0===n&&(n=!1);var i=c(t),o=c(t)&&(a=(o=t).getBoundingClientRect(),r=_(a.width)/o.offsetWidth||1,a=_(a.height)/o.offsetHeight||1,1!==r||1!==a),r=w(t),a=f(e,o,n),e={scrollLeft:0,scrollTop:0},s={x:0,y:0};return!i&&n||("body"===l(t)&&!u(r)||(e=(i=t)!==b(i)&&c(i)?{scrollLeft:i.scrollLeft,scrollTop:i.scrollTop}:d(i)),c(t)?((s=f(t,!0)).x+=t.clientLeft,s.y+=t.clientTop):r&&(s.x=h(r))),{x:a.left+e.scrollLeft-s.x,y:a.top+e.scrollTop-s.y,width:a.width,height:a.height}}function x(e){var t=f(e),n=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function s(e){return"html"===l(e)?e:e.assignedSlot||e.parentNode||(r(e)?e.host:null)||w(e)}function m(e,t){void 0===t&&(t=[]);var n=function e(t){return 0<=["html","body","#document"].indexOf(l(t))?t.ownerDocument.body:c(t)&&u(t)?t:e(s(t))}(e),e=n===(null==(e=e.ownerDocument)?void 0:e.body),i=b(n),i=e?[i].concat(i.visualViewport||[],u(n)?n:[]):n,n=t.concat(i);return e?n:n.concat(m(s(i)))}function o(e){return c(e)&&"fixed"!==k(e).position?e.offsetParent:null}function S(e){for(var t,n=b(e),i=o(e);i&&(t=i,0<=["table","td","th"].indexOf(l(t)))&&"static"===k(i).position;)i=o(i);return(!i||"html"!==l(i)&&("body"!==l(i)||"static"!==k(i).position))&&(i||function(e){var t=/firefox/i.test(a()),n=/Trident/i.test(a());if(!n||!c(e)||"fixed"!==k(e).position){var i=s(e);for(r(i)&&(i=i.host);c(i)&&["html","body"].indexOf(l(i))<0;){var o=k(i);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||t&&"filter"===o.willChange||t&&o.filter&&"none"!==o.filter)return i;i=i.parentNode}}return null}(e))||n}var L="top",j="bottom",N="right",D="left",M="auto",P=[L,j,N,D],I="start",E="end",R="clippingParents",B="viewport",v="popper",W="reference",U=P.reduce(function(e,t){return e.concat([t+"-"+I,t+"-"+E])},[]),Y=[].concat(P,[M]).reduce(function(e,t){return e.concat([t,t+"-"+I,t+"-"+E])},[]),X=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Q(e){var n=new Map,i=new Set,o=[];return e.forEach(function(e){n.set(e.name,e)}),e.forEach(function(e){i.has(e.name)||!function t(e){i.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){i.has(e)||(e=n.get(e))&&t(e)}),o.push(e)}(e)}),o}function K(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&r(n)){var i=t;do{if(i&&e.isSameNode(i))return!0}while(i=i.parentNode||i.host)}return!1}function g(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function $(e,t,n){return t===B?g((o=n,a=b(i=e),s=w(i),a=a.visualViewport,c=s.clientWidth,s=s.clientHeight,u=l=0,a&&(c=a.width,s=a.height,(r=O())||!r&&"fixed"===o)&&(l=a.offsetLeft,u=a.offsetTop),{width:c,height:s,x:l+h(i),y:u})):p(t)?((o=f(r=t,!1,"fixed"===(o=n))).top=o.top+r.clientTop,o.left=o.left+r.clientLeft,o.bottom=o.top+r.clientHeight,o.right=o.left+r.clientWidth,o.width=r.clientWidth,o.height=r.clientHeight,o.x=o.left,o.y=o.top,o):g((a=w(e),c=w(a),s=d(a),l=null==(l=a.ownerDocument)?void 0:l.body,i=T(c.scrollWidth,c.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),u=T(c.scrollHeight,c.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),a=-s.scrollLeft+h(a),s=-s.scrollTop,"rtl"===k(l||c).direction&&(a+=T(c.clientWidth,l?l.clientWidth:0)-i),{width:i,height:u,x:a,y:s}));var i,o,r,a,s,c,l,u}function J(n,e,t,i){var o,r="clippingParents"===e?(a=m(s(r=n)),p(o=0<=["absolute","fixed"].indexOf(k(r).position)&&c(r)?S(r):r)?a.filter(function(e){return p(e)&&K(e,o)&&"body"!==l(e)}):[]):[].concat(e),a=[].concat(r,[t]),e=a[0],t=a.reduce(function(e,t){t=$(n,t,i);return e.top=T(t.top,e.top),e.right=C(t.right,e.right),e.bottom=C(t.bottom,e.bottom),e.left=T(t.left,e.left),e},$(n,e,i));return t.width=t.right-t.left,t.height=t.bottom-t.top,t.x=t.left,t.y=t.top,t}function V(e){return e.split("-")[0]}function F(e){return e.split("-")[1]}function G(e){return 0<=["top","bottom"].indexOf(e)?"x":"y"}function Z(e){var t,n=e.reference,i=e.element,e=e.placement,o=e?V(e):null,e=e?F(e):null,r=n.x+n.width/2-i.width/2,a=n.y+n.height/2-i.height/2;switch(o){case L:t={x:r,y:n.y-i.height};break;case j:t={x:r,y:n.y+n.height};break;case N:t={x:n.x+n.width,y:a};break;case D:t={x:n.x-i.width,y:a};break;default:t={x:n.x,y:n.y}}var s=o?G(o):null;if(null!=s){var c="y"===s?"height":"width";switch(e){case I:t[s]=t[s]-(n[c]/2-i[c]/2);break;case E:t[s]=t[s]+(n[c]/2-i[c]/2)}}return t}function ee(){return{top:0,right:0,bottom:0,left:0}}function te(e){return Object.assign({},ee(),e)}function ne(n,e){return e.reduce(function(e,t){return e[t]=n,e},{})}function H(e,t){var i,t=t=void 0===t?{}:t,n=t.placement,n=void 0===n?e.placement:n,o=t.strategy,o=void 0===o?e.strategy:o,r=t.boundary,r=void 0===r?R:r,a=t.rootBoundary,a=void 0===a?B:a,s=t.elementContext,s=void 0===s?v:s,c=t.altBoundary,c=void 0!==c&&c,t=t.padding,t=void 0===t?0:t,t=te("number"!=typeof t?t:ne(t,P)),l=e.rects.popper,c=e.elements[c?s===v?W:v:s],c=J(p(c)?c:c.contextElement||w(e.elements.popper),r,a,o),r=f(e.elements.reference),a=Z({reference:r,element:l,strategy:"absolute",placement:n}),o=g(Object.assign({},l,a)),l=s===v?o:r,u={top:c.top-l.top+t.top,bottom:l.bottom-c.bottom+t.bottom,left:c.left-l.left+t.left,right:l.right-c.right+t.right},a=e.modifiersData.offset;return s===v&&a&&(i=a[n],Object.keys(u).forEach(function(e){var t=0<=[N,j].indexOf(e)?1:-1,n=0<=[L,j].indexOf(e)?"y":"x";u[e]+=i[n]*t})),u}var ie={placement:"bottom",modifiers:[],strategy:"absolute"};function oe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function i(e){var e=e=void 0===e?{}:e,t=e.defaultModifiers,f=void 0===t?[]:t,t=e.defaultOptions,d=void 0===t?ie:t;return function(i,o,t){void 0===t&&(t=d);var n,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},ie,d),modifiersData:{},elements:{reference:i,popper:o},attributes:{},styles:{}},s=[],c=!1,l={state:a,setOptions:function(e){var n,t,e="function"==typeof e?e(a.options):e,e=(u(),a.options=Object.assign({},d,a.options,e),a.scrollParents={reference:p(i)?m(i):i.contextElement?m(i.contextElement):[],popper:m(o)},e=[].concat(f,a.options.modifiers),t=e.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{}),e=Object.keys(t).map(function(e){return t[e]}),n=Q(e),X.reduce(function(e,t){return e.concat(n.filter(function(e){return e.phase===t}))},[]));return a.orderedModifiers=e.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,e=e.effect;"function"==typeof e&&(e=e({state:a,name:t,instance:l,options:void 0===n?{}:n}),s.push(e||function(){}))}),l.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,e=e.popper;if(oe(t,e)){a.rects={reference:A(t,S(e),"fixed"===a.options.strategy),popper:x(e)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var n,i,o,r=0;r<a.orderedModifiers.length;r++)!0===a.reset?(a.reset=!1,r=-1):(n=(o=a.orderedModifiers[r]).fn,i=o.options,o=o.name,"function"==typeof n&&(a=n({state:a,options:void 0===i?{}:i,name:o,instance:l})||a))}}},update:(n=function(){return new Promise(function(e){l.forceUpdate(),e(a)})},function(){return r=r||new Promise(function(e){Promise.resolve().then(function(){r=void 0,e(n())})})}),destroy:function(){u(),c=!0}};return oe(i,o)&&l.setOptions(t).then(function(e){!c&&t.onFirstUpdate&&t.onFirstUpdate(e)}),l;function u(){s.forEach(function(e){return e()}),s=[]}}}var y={passive:!0};var re={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,i=(e=e.options).scroll,o=void 0===i||i,r=void 0===(i=e.resize)||i,a=b(t.elements.popper),s=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&s.forEach(function(e){e.addEventListener("scroll",n.update,y)}),r&&a.addEventListener("resize",n.update,y),function(){o&&s.forEach(function(e){e.removeEventListener("scroll",n.update,y)}),r&&a.removeEventListener("resize",n.update,y)}},data:{}};var ae={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,e=e.name;t.modifiersData[e]=Z({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},se={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ce(e){var t,n=e.popper,i=e.popperRect,o=e.placement,r=e.variation,a=e.offsets,s=e.position,c=e.gpuAcceleration,l=e.adaptive,u=e.roundOffsets,e=e.isFixed,f=a.x,f=void 0===f?0:f,d=a.y,d=void 0===d?0:d,p="function"==typeof u?u({x:f,y:d}):{x:f,y:d},p=(f=p.x,d=p.y,a.hasOwnProperty("x")),a=a.hasOwnProperty("y"),h=D,m=L,v=window,g=(l&&(g="clientHeight",t="clientWidth",(y=S(n))===b(n)&&"static"!==k(y=w(n)).position&&"absolute"===s&&(g="scrollHeight",t="scrollWidth"),o!==L&&(o!==D&&o!==N||r!==E)||(m=j,d=(d-((e&&y===v&&v.visualViewport?v.visualViewport.height:y[g])-i.height))*(c?1:-1)),o!==D&&(o!==L&&o!==j||r!==E)||(h=N,f=(f-((e&&y===v&&v.visualViewport?v.visualViewport.width:y[t])-i.width))*(c?1:-1))),Object.assign({position:s},l&&se)),y=!0===u?(o={x:f,y:d},r=b(n),e=o.x,o=o.y,r=r.devicePixelRatio||1,{x:_(e*r)/r||0,y:_(o*r)/r||0}):{x:f,y:d};return f=y.x,d=y.y,c?Object.assign({},g,((t={})[m]=a?"0":"",t[h]=p?"0":"",t.transform=(v.devicePixelRatio||1)<=1?"translate("+f+"px, "+d+"px)":"translate3d("+f+"px, "+d+"px, 0)",t)):Object.assign({},g,((i={})[m]=a?d+"px":"",i[h]=p?f+"px":"",i.transform="",i))}var le={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,e=e.options,n=void 0===(n=e.gpuAcceleration)||n,i=void 0===(i=e.adaptive)||i,e=void 0===(e=e.roundOffsets)||e,n={placement:V(t.placement),variation:F(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:n,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ce(Object.assign({},n,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:e})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ce(Object.assign({},n,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:e})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ue={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var o=e.state;Object.keys(o.elements).forEach(function(e){var t=o.styles[e]||{},n=o.attributes[e]||{},i=o.elements[e];c(i)&&l(i)&&(Object.assign(i.style,t),Object.keys(n).forEach(function(e){var t=n[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var i=e.state,o={popper:{position:i.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(i.elements.popper.style,o.popper),i.styles=o,i.elements.arrow&&Object.assign(i.elements.arrow.style,o.arrow),function(){Object.keys(i.elements).forEach(function(e){var t=i.elements[e],n=i.attributes[e]||{},e=Object.keys((i.styles.hasOwnProperty(e)?i.styles:o)[e]).reduce(function(e,t){return e[t]="",e},{});c(t)&&l(t)&&(Object.assign(t.style,e),Object.keys(n).forEach(function(e){t.removeAttribute(e)}))})}},requires:["computeStyles"]};var fe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var a=e.state,t=e.options,e=e.name,s=void 0===(t=t.offset)?[0,0]:t,t=Y.reduce(function(e,t){var n,i,o,r;return e[t]=(t=t,n=a.rects,i=s,o=V(t),r=0<=[D,L].indexOf(o)?-1:1,t=(n="function"==typeof i?i(Object.assign({},n,{placement:t})):i)[0]||0,i=(n[1]||0)*r,0<=[D,N].indexOf(o)?{x:i,y:t}:{x:t,y:i}),e},{}),n=(i=t[a.placement]).x,i=i.y;null!=a.modifiersData.popperOffsets&&(a.modifiersData.popperOffsets.x+=n,a.modifiersData.popperOffsets.y+=i),a.modifiersData[e]=t}},de={left:"right",right:"left",bottom:"top",top:"bottom"};function q(e){return e.replace(/left|right|bottom|top/g,function(e){return de[e]})}var pe={start:"end",end:"start"};function he(e){return e.replace(/start|end/g,function(e){return pe[e]})}var me={name:"flip",enabled:!0,phase:"main",fn:function(e){var f=e.state,t=e.options,e=e.name;if(!f.modifiersData[e]._skip){for(var n=t.mainAxis,i=void 0===n||n,n=t.altAxis,o=void 0===n||n,n=t.fallbackPlacements,d=t.padding,p=t.boundary,h=t.rootBoundary,r=t.altBoundary,a=t.flipVariations,m=void 0===a||a,v=t.allowedAutoPlacements,a=f.options.placement,t=V(a),n=n||(t===a||!m?[q(a)]:V(n=a)===M?[]:(t=q(n),[he(n),t,he(t)])),s=[a].concat(n).reduce(function(e,t){return e.concat(V(t)===M?(n=f,i=(e=e=void 0===(e={placement:t,boundary:p,rootBoundary:h,padding:d,flipVariations:m,allowedAutoPlacements:v})?{}:e).placement,o=e.boundary,r=e.rootBoundary,a=e.padding,s=e.flipVariations,c=void 0===(e=e.allowedAutoPlacements)?Y:e,l=F(i),e=l?s?U:U.filter(function(e){return F(e)===l}):P,u=(i=0===(i=e.filter(function(e){return 0<=c.indexOf(e)})).length?e:i).reduce(function(e,t){return e[t]=H(n,{placement:t,boundary:o,rootBoundary:r,padding:a})[V(t)],e},{}),Object.keys(u).sort(function(e,t){return u[e]-u[t]})):t);var n,i,o,r,a,s,c,l,u},[]),c=f.rects.reference,l=f.rects.popper,u=new Map,g=!0,y=s[0],b=0;b<s.length;b++){var _=s[b],w=V(_),k=F(_)===I,E=0<=[L,j].indexOf(w),O=E?"width":"height",A=H(f,{placement:_,boundary:p,rootBoundary:h,altBoundary:r,padding:d}),E=E?k?N:D:k?j:L,k=(c[O]>l[O]&&(E=q(E)),q(E)),O=[];if(i&&O.push(A[w]<=0),o&&O.push(A[E]<=0,A[k]<=0),O.every(function(e){return e})){y=_,g=!1;break}u.set(_,O)}if(g)for(var T=m?3:1;0<T;T--)if("break"===function(t){var e=s.find(function(e){e=u.get(e);if(e)return e.slice(0,t).every(function(e){return e})});if(e)return y=e,"break"}(T))break;f.placement!==y&&(f.modifiersData[e]._skip=!0,f.placement=y,f.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function z(e,t,n){return T(e,C(t,n))}var ve={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t,n,i,o,r,a,s,c,l,u=e.state,f=e.options,e=e.name,d=void 0===(d=f.mainAxis)||d,p=void 0!==(p=f.altAxis)&&p,h=f.boundary,m=f.rootBoundary,v=f.altBoundary,g=f.padding,y=void 0===(y=f.tether)||y,f=void 0===(f=f.tetherOffset)?0:f,h=H(u,{boundary:h,rootBoundary:m,padding:g,altBoundary:v}),m=V(u.placement),v=!(g=F(u.placement)),b=G(m),_="x"===b?"y":"x",w=u.modifiersData.popperOffsets,k=u.rects.reference,E=u.rects.popper,f="number"==typeof(f="function"==typeof f?f(Object.assign({},u.rects,{placement:u.placement})):f)?{mainAxis:f,altAxis:f}:Object.assign({mainAxis:0,altAxis:0},f),O=u.modifiersData.offset?u.modifiersData.offset[u.placement]:null,A={x:0,y:0};w&&(d&&(d="y"===b?"height":"width",a=(s=w[b])+h[n="y"===b?L:D],c=s-h[l="y"===b?j:N],t=y?-E[d]/2:0,o=(g===I?k:E)[d],g=g===I?-E[d]:-k[d],r=u.elements.arrow,r=y&&r?x(r):{width:0,height:0},n=(i=u.modifiersData["arrow#persistent"]?u.modifiersData["arrow#persistent"].padding:ee())[n],i=i[l],l=z(0,k[d],r[d]),r=v?k[d]/2-t-l-n-f.mainAxis:o-l-n-f.mainAxis,o=v?-k[d]/2+t+l+i+f.mainAxis:g+l+i+f.mainAxis,v=(n=u.elements.arrow&&S(u.elements.arrow))?"y"===b?n.clientTop||0:n.clientLeft||0:0,g=s+o-(t=null!=(d=null==O?void 0:O[b])?d:0),l=z(y?C(a,s+r-t-v):a,s,y?T(c,g):c),w[b]=l,A[b]=l-s),p&&(i="y"==_?"height":"width",o=(n=w[_])+h["x"===b?L:D],d=n-h["x"===b?j:N],r=-1!==[L,D].indexOf(m),v=null!=(t=null==O?void 0:O[_])?t:0,a=r?o:n-k[i]-E[i]-v+f.altAxis,g=r?n+k[i]+E[i]-v-f.altAxis:d,s=y&&r?(c=z(c=a,n,l=g),l<c?l:c):z(y?a:o,n,y?g:d),w[_]=s,A[_]=s-n),u.modifiersData[e]=A)},requiresIfExists:["offset"]};var ge={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n,i,o,r=e.state,a=e.name,e=e.options,s=r.elements.arrow,c=r.modifiersData.popperOffsets,l=G(u=V(r.placement)),u=0<=[D,N].indexOf(u)?"height":"width";s&&c&&(e=e.padding,n=r,n=te("number"!=typeof(e="function"==typeof e?e(Object.assign({},n.rects,{placement:n.placement})):e)?e:ne(e,P)),e=x(s),o="y"===l?L:D,i="y"===l?j:N,t=r.rects.reference[u]+r.rects.reference[l]-c[l]-r.rects.popper[u],c=c[l]-r.rects.reference[l],s=(s=S(s))?"y"===l?s.clientHeight||0:s.clientWidth||0:0,o=n[o],n=s-e[u]-n[i],o=z(o,i=s/2-e[u]/2+(t/2-c/2),n),r.modifiersData[a]=((s={})[l]=o,s.centerOffset=o-i,s))},effect:function(e){var t=e.state;null!=(e=void 0===(e=e.options.element)?"[data-popper-arrow]":e)&&("string"!=typeof e||(e=t.elements.popper.querySelector(e)))&&K(t.elements.popper,e)&&(t.elements.arrow=e)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ye(e,t,n){return{top:e.top-t.height-(n=void 0===n?{x:0,y:0}:n).y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function be(t){return[L,N,j,D].some(function(e){return 0<=t[e]})}var _e={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,e=e.name,n=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,r=H(t,{elementContext:"reference"}),a=H(t,{altBoundary:!0}),r=ye(r,n),n=ye(a,i,o),a=be(r),i=be(n);t.modifiersData[e]={referenceClippingOffsets:r,popperEscapeOffsets:n,isReferenceHidden:a,hasPopperEscaped:i},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":a,"data-popper-escaped":i})}},we=i({defaultModifiers:[re,ae,le,ue]}),ke=[re,ae,le,ue,fe,me,ve,ge,_e],Ee=i({defaultModifiers:ke});e.applyStyles=ue,e.arrow=ge,e.computeStyles=le,e.createPopper=Ee,e.createPopperLite=we,e.defaultModifiers=ke,e.detectOverflow=H,e.eventListeners=re,e.flip=me,e.hide=_e,e.offset=fe,e.popperGenerator=i,e.popperOffsets=ae,e.preventOverflow=ve},{}],2:[function(e,t,n){!function(ue){!function(){"use strict";function le(e){return(le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var e;e=function(){function y(){return F.Date.now()}var e="undefined"!=typeof window?window:void 0!==ue?ue:"undefined"!=typeof self?self:{},b="Expected a function",n=NaN,i="[object Symbol]",o=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,I=/^0o[0-7]+$/i,V=parseInt,t="object"==le(e)&&e&&e.Object===Object&&e,s="object"==("undefined"==typeof self?"undefined":le(self))&&self&&self.Object===Object&&self,F=t||s||Function("return this")(),H=Object.prototype.toString,q=Math.max,z=Math.min;function R(i,n,e){var o,r,a,s,c,l,u=0,f=!1,d=!1,t=!0;if("function"!=typeof i)throw new TypeError(b);function p(e){var t=o,n=r;return o=r=void 0,u=e,s=i.apply(n,t)}function h(e){var t=e-l;return void 0===l||n<=t||t<0||d&&a<=e-u}function m(){var e,t=y();if(h(t))return v(t);c=setTimeout(m,(e=n-(t-l),d?z(e,a-(t-u)):e))}function v(e){return c=void 0,t&&o?p(e):(o=r=void 0,s)}function g(){var e=y(),t=h(e);if(o=arguments,r=this,l=e,t){if(void 0===c)return u=e=l,c=setTimeout(m,n),f?p(e):s;if(d)return c=setTimeout(m,n),p(l)}return void 0===c&&(c=setTimeout(m,n)),s}return n=w(n)||0,_(e)&&(f=!!e.leading,a=(d="maxWait"in e)?q(w(e.maxWait)||0,n):a,t="trailing"in e?!!e.trailing:t),g.cancel=function(){void 0!==c&&clearTimeout(c),o=l=r=c=void(u=0)},g.flush=function(){return void 0===c?s:v(y())},g}function _(e){var t=le(e);return e&&("object"==t||"function"==t)}function w(e){if("number"==typeof e)return e;if("symbol"==le(t=e)||t&&"object"==le(t)&&H.call(t)==i)return n;if("string"!=typeof(e=_(e)?_(t="function"==typeof e.valueOf?e.valueOf():e)?t+"":t:e))return 0===e?e:+e;e=e.replace(o,"");var t=a.test(e);return t||I.test(e)?V(e.slice(2),t?2:8):r.test(e)?n:+e}function k(){return $.Date.now()}var B=function(e,t,n){var i=!0,o=!0;if("function"!=typeof e)throw new TypeError(b);return _(n)&&(i="leading"in n?!!n.leading:i,o="trailing"in n?!!n.trailing:o),R(e,t,{leading:i,maxWait:t,trailing:o})},c=NaN,W="[object Symbol]",U=/^\s+|\s+$/g,Y=/^[-+]0x[0-9a-f]+$/i,X=/^0b[01]+$/i,Q=/^0o[0-7]+$/i,K=parseInt,t="object"==le(e)&&e&&e.Object===Object&&e,s="object"==("undefined"==typeof self?"undefined":le(self))&&self&&self.Object===Object&&self,$=t||s||Function("return this")(),J=Object.prototype.toString,G=Math.max,Z=Math.min;function E(e){var t=le(e);return e&&("object"==t||"function"==t)}function O(e){if("number"==typeof e)return e;if("symbol"==le(t=e)||t&&"object"==le(t)&&J.call(t)==W)return c;if("string"!=typeof(e=E(e)?E(t="function"==typeof e.valueOf?e.valueOf():e)?t+"":t:e))return 0===e?e:+e;e=e.replace(U,"");var t=X.test(e);return t||Q.test(e)?K(e.slice(2),t?2:8):Y.test(e)?c:+e}function l(i,n,e){var o,r,a,s,c,l,u=0,f=!1,d=!1,t=!0;if("function"!=typeof i)throw new TypeError("Expected a function");function p(e){var t=o,n=r;return o=r=void 0,u=e,s=i.apply(n,t)}function h(e){var t=e-l;return void 0===l||n<=t||t<0||d&&a<=e-u}function m(){var e,t=k();if(h(t))return v(t);c=setTimeout(m,(e=n-(t-l),d?Z(e,a-(t-u)):e))}function v(e){return c=void 0,t&&o?p(e):(o=r=void 0,s)}function g(){var e=k(),t=h(e);if(o=arguments,r=this,l=e,t){if(void 0===c)return u=e=l,c=setTimeout(m,n),f?p(e):s;if(d)return c=setTimeout(m,n),p(l)}return void 0===c&&(c=setTimeout(m,n)),s}return n=O(n)||0,E(e)&&(f=!!e.leading,a=(d="maxWait"in e)?G(O(e.maxWait)||0,n):a,t="trailing"in e?!!e.trailing:t),g.cancel=function(){void 0!==c&&clearTimeout(c),o=l=r=c=void(u=0)},g.flush=function(){return void 0===c?s:v(k())},g}var u=function(){};function ee(e){e&&e.forEach(function(e){var t=Array.prototype.slice.call(e.addedNodes),e=Array.prototype.slice.call(e.removedNodes);if(function e(t){for(var n,i=void 0,i=0;i<t.length;i+=1){if((n=t[i]).dataset&&n.dataset.aos)return 1;if(n.children&&e(n.children))return 1}}(t.concat(e)))return u()})}function f(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}var te=function(){return!!f()},ne=function(e,t){var n=window.document,i=new(f())(ee);u=t,i.observe(n.documentElement,{childList:!0,subtree:!0,removedNodes:!0})},e=function(e,t,n){return t&&d(e.prototype,t),n&&d(e,n),e},ie=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},oe=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,re=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,ae=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,se=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;function d(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function p(){return navigator.userAgent||navigator.vendor||window.opera||""}function h(e,t){var n=void 0;L.ie11()?(n=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,{detail:t}):n=new CustomEvent(e,{detail:t}),document.dispatchEvent(n)}function m(e){for(var t=0,n=0;e&&!isNaN(e.offsetLeft)&&!isNaN(e.offsetTop);)t+=e.offsetLeft-("BODY"!=e.tagName?e.scrollLeft:0),n+=e.offsetTop-("BODY"!=e.tagName?e.scrollTop:0),e=e.offsetParent;return{top:n,left:t}}function v(e,t,n){if(void 0!==(e=e.getAttribute("data-aos-"+t))){if("true"===e)return!0;if("false"===e)return!1}return e||n}function g(){var e=document.querySelectorAll("[data-aos]");return Array.prototype.map.call(e,function(e){return{node:e}})}function A(){return document.all&&!window.atob}function T(){(D=0<arguments.length&&void 0!==arguments[0]&&arguments[0]?!0:D)&&(N=ce(N,M),j(N),window.addEventListener("scroll",B(function(){j(N,M.once)},M.throttleDelay)))}function C(){if(N=g(),S(M.disable)||A())return x();T()}function x(){N.forEach(function(e,t){e.node.removeAttribute("data-aos"),e.node.removeAttribute("data-aos-easing"),e.node.removeAttribute("data-aos-duration"),e.node.removeAttribute("data-aos-delay"),M.initClassName&&e.node.classList.remove(M.initClassName),M.animatedClassName&&e.node.classList.remove(M.animatedClassName)})}function S(e){return!0===e||"mobile"===e&&L.mobile()||"phone"===e&&L.phone()||"tablet"===e&&L.tablet()||"function"==typeof e&&!0===e()}e(P,[{key:"phone",value:function(){var e=p();return!(!oe.test(e)&&!re.test(e.substr(0,4)))}},{key:"mobile",value:function(){var e=p();return!(!ae.test(e)&&!se.test(e.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}},{key:"ie11",value:function(){return"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style}}]);var L=new P,j=function(e){return e.forEach(function(e,t){var n=e,e=window.pageYOffset;function i(){var t,e;n.animated&&(t=s,(e=r.animatedClassNames)&&e.forEach(function(e){return t.classList.remove(e)}),h("aos:out",s),n.options.id&&h("aos:in:"+n.options.id,s),n.animated=!1)}var o,r=n.options,a=n.position,s=n.node;n.data,r.mirror&&e>=a.out&&!r.once?i():e>=a.in?n.animated||(o=s,(e=r.animatedClassNames)&&e.forEach(function(e){return o.classList.add(e)}),h("aos:in",s),n.options.id&&h("aos:in:"+n.options.id,s),n.animated=!0):n.animated&&!r.once&&i()})},ce=function(e,l){return e.forEach(function(e,t){var n,i,o,r=v(e.node,"mirror",l.mirror),a=v(e.node,"once",l.once),s=v(e.node,"id"),c=l.useClassNames&&e.node.getAttribute("data-aos"),c=[l.animatedClassName].concat(c?c.split(" "):[]).filter(function(e){return"string"==typeof e});l.initClassName&&e.node.classList.add(l.initClassName),e.position={in:function(e,t,n){var i=window.innerHeight,o=v(e,"anchor"),r=v(e,"anchor-placement"),t=Number(v(e,"offset",r?0:t)),r=r||n,a=e,s=(o&&document.querySelectorAll(o)&&(a=document.querySelectorAll(o)[0]),m(a).top-i);switch(r){case"top-bottom":break;case"center-bottom":s+=a.offsetHeight/2;break;case"bottom-bottom":s+=a.offsetHeight;break;case"top-center":s+=i/2;break;case"center-center":s+=i/2+a.offsetHeight/2;break;case"bottom-center":s+=i/2+a.offsetHeight;break;case"top-top":s+=i;break;case"bottom-top":s+=i+a.offsetHeight;break;case"center-top":s+=i+a.offsetHeight/2}return s+t}(e.node,l.offset,l.anchorPlacement),out:r&&(n=e.node,i=l.offset,window.innerHeight,o=v(n,"anchor"),i=v(n,"offset",i),o&&document.querySelectorAll(o)&&(n=document.querySelectorAll(o)[0]),m(n).top+n.offsetHeight-i)},e.options={once:a,mirror:r,animatedClassNames:c,id:s}}),e},N=[],D=!1,M={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,mirror:!1,anchorPlacement:"top-bottom",startEvent:"DOMContentLoaded",animatedClassName:"aos-animate",initClassName:"aos-init",useClassNames:!1,disableMutationObserver:!1,throttleDelay:99,debounceDelay:50};function P(){if(!(this instanceof P))throw new TypeError("Cannot call a class as a function")}return{init:function(e){return M=ie(M,e),N=g(),M.disableMutationObserver||te()||(console.info('\n      aos: MutationObserver is not supported on this browser,\n      code mutations observing has been disabled.\n      You may have to call "refreshHard()" by yourself.\n    '),M.disableMutationObserver=!0),M.disableMutationObserver||ne("[data-aos]",C),S(M.disable)||A()?x():(document.querySelector("body").setAttribute("data-aos-easing",M.easing),document.querySelector("body").setAttribute("data-aos-duration",M.duration),document.querySelector("body").setAttribute("data-aos-delay",M.delay),-1===["DOMContentLoaded","load"].indexOf(M.startEvent)?document.addEventListener(M.startEvent,function(){T(!0)}):window.addEventListener("load",function(){T(!0)}),"DOMContentLoaded"===M.startEvent&&-1<["complete","interactive"].indexOf(document.readyState)&&T(!0),window.addEventListener("resize",l(T,M.debounceDelay,!0)),window.addEventListener("orientationchange",l(T,M.debounceDelay,!0)),N)},refresh:T,refreshHard:C}},"object"==(void 0===n?"undefined":le(n))&&void 0!==t?t.exports=e():"function"==typeof define&&define.amd?define(e):(void 0).AOS=e()}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(e,t,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o;o=function(){var c=new Map;function n(o){var r,a,e,t,n;function s(e){var t,n=e.restoreTextAlign,n=void 0===n?null:n,e=e.testForHeightReduction,e=void 0===e||e,i=a.overflowY;0===o.scrollHeight||("vertical"===a.resize?o.style.resize="none":"both"===a.resize&&(o.style.resize="horizontal"),e&&(t=function(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push([e.parentNode,e.parentNode.scrollTop]),e=e.parentNode;return function(){return t.forEach(function(e){var t=e[0],e=e[1];t.style.scrollBehavior="auto",t.scrollTop=e,t.style.scrollBehavior=null})}}(o),o.style.height=""),e="content-box"===a.boxSizing?o.scrollHeight-(parseFloat(a.paddingTop)+parseFloat(a.paddingBottom)):o.scrollHeight+parseFloat(a.borderTopWidth)+parseFloat(a.borderBottomWidth),"none"!==a.maxHeight&&e>parseFloat(a.maxHeight)?("hidden"===a.overflowY&&(o.style.overflow="scroll"),e=parseFloat(a.maxHeight)):"hidden"!==a.overflowY&&(o.style.overflow="hidden"),o.style.height=e+"px",n&&(o.style.textAlign=n),t&&t(),r!==e&&(o.dispatchEvent(new Event("autosize:resized",{bubbles:!0})),r=e),i===a.overflow)||n||(t=a.textAlign,"hidden"===a.overflow&&(o.style.textAlign="start"===t?"end":"start"),s({restoreTextAlign:t,testForHeightReduction:!0}))}function i(){s({testForHeightReduction:!0,restoreTextAlign:null})}o&&o.nodeName&&"TEXTAREA"===o.nodeName&&!c.has(o)&&(r=null,a=window.getComputedStyle(o),t=o.value,e=function(){s({testForHeightReduction:""===t||!o.value.startsWith(t),restoreTextAlign:null}),t=o.value},n=function(t){o.removeEventListener("autosize:destroy",n),o.removeEventListener("autosize:update",i),o.removeEventListener("input",e),window.removeEventListener("resize",i),Object.keys(t).forEach(function(e){return o.style[e]=t[e]}),c.delete(o)}.bind(o,{height:o.style.height,resize:o.style.resize,textAlign:o.style.textAlign,overflowY:o.style.overflowY,overflowX:o.style.overflowX,wordWrap:o.style.wordWrap}),o.addEventListener("autosize:destroy",n),o.addEventListener("autosize:update",i),o.addEventListener("input",e),window.addEventListener("resize",i),o.style.overflowX="hidden",o.style.wordWrap="break-word",c.set(o,{destroy:n,update:i}),i())}function t(e){e=c.get(e);e&&e.destroy()}function i(e){e=c.get(e);e&&e.update()}var e=null;return"undefined"==typeof window?((e=function(e){return e}).destroy=function(e){return e},e.update=function(e){return e}):((e=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],n),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],t),e},e.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],i),e}),e},"object"===(void 0===n?"undefined":i(n))&&void 0!==t?t.exports=o():"function"==typeof define&&define.amd?define(o):self.autosize=o()},{}],4:[function(e,t,n){"use strict";function ti(){return(ti="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=oi(e)););return e}(e,t);if(i)return(i=Object.getOwnPropertyDescriptor(i,t)).get?i.get.call(arguments.length<3?e:n):i.value}).apply(this,arguments)}function ni(e,t,n){return t=oi(t),ii(e,i()?Reflect.construct(t,n||[],oi(e).constructor):t.apply(e,n))}function ii(e,t){if(t&&("object"===hi(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ri(e)}function i(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i=function(){return!!e})()}function oi(e){return(oi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ri(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ai(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)}function o(e,t){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function r(t,e){var n,i=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)),i}function si(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach(function(e){pi(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function ci(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,l(i.key),i)}}function li(e,t,n){t&&a(e.prototype,t),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1})}function ui(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,o,r,a,s=[],c=!0,l=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(i=r.call(n)).done)&&(s.push(i.value),s.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}(e,t)||s(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fi(e,t){var n,i,o,r,a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return i=!(n=!0),{s:function(){a=a.call(e)},n:function(){var e=a.next();return n=e.done,e},e:function(e){i=!0,o=e},f:function(){try{n||null==a.return||a.return()}finally{if(i)throw o}}};if(Array.isArray(e)||(a=s(e))||t&&e&&"number"==typeof e.length)return a&&(e=a),r=0,{s:t=function(){},n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function di(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||s(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){var n;if(e)return"string"==typeof e?c(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function pi(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){e=function(e,t){if("object"!=hi(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=hi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==hi(e)?e:String(e)}function hi(e){return(hi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var u,f;f=function(e){function I(n){var i=Object.create(null,pi({},Symbol.toStringTag,{value:"Module"}));if(n)for(var e in n)!function(e){var t;"default"!==e&&(t=Object.getOwnPropertyDescriptor(n,e),Object.defineProperty(i,e,t.get?t:{enumerable:!0,get:function(){return n[e]}}))}(e);return i.default=n,Object.freeze(i)}function V(e){e.dispatchEvent(new Event(Y))}function o(e){return d(e)?e.jquery?e[0]:e:"string"==typeof e&&0<e.length?document.querySelector(X(e)):null}function r(e){if(!d(e)||0===e.getClientRects().length)return!1;var t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(n&&n!==e){e=e.closest("summary");if(e&&e.parentNode!==n)return!1;if(null===e)return!1}return t}function a(e){return!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))}function F(e){var t;return document.documentElement.attachShadow?"function"==typeof e.getRootNode?(t=e.getRootNode())instanceof ShadowRoot?t:null:e instanceof ShadowRoot?e:e.parentNode?F(e.parentNode):null:null}function s(){}function u(e){e.offsetHeight}function H(){return window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null}function c(){return"rtl"===document.documentElement.dir}function t(i){var e;e=function(){var e,t,n=H();n&&(e=i.NAME,t=n.fn[e],n.fn[e]=i.jQueryInterface,n.fn[e].Constructor=i,n.fn[e].noConflict=function(){return n.fn[e]=t,i.jQueryInterface})},"loading"===document.readyState?(Q.length||document.addEventListener("DOMContentLoaded",function(){for(var e=0,t=Q;e<t.length;e++)(0,t[e])()}),Q.push(e)):e()}function l(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:e;return"function"==typeof e?e.call.apply(e,di(t)):n}function q(n,i){var e,t,o,r,a;2<arguments.length&&void 0!==arguments[2]&&!arguments[2]?l(n):(o=5+((e=i)&&(t=(e=window.getComputedStyle(e)).transitionDuration,e=e.transitionDelay,o=Number.parseFloat(t),r=Number.parseFloat(e),o||r)?(t=t.split(",")[0],e=e.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(e))*U):0),a=!1,i.addEventListener(Y,function e(t){t.target===i&&(a=!0,i.removeEventListener(Y,e),l(n))}),setTimeout(function(){a||V(i)},o))}function z(e,t,n,i){var o=e.length;return-1===(t=e.indexOf(t))?!n&&i?e[o-1]:e[0]:(t+=n?1:-1,i&&(t=(t+o)%o),e[Math.max(0,Math.min(t,o-1))])}var i=I(e),f=new Map,R=function(e,t,n){f.has(e)||f.set(e,new Map);e=f.get(e);e.has(t)||0===e.size?e.set(t,n):console.error("Bootstrap doesn't allow more than one instance per element. Bound instance: ".concat(Array.from(e.keys())[0],"."))},B=function(e,t){return f.has(e)&&f.get(e).get(t)||null},W=function(e,t){var n;f.has(e)&&((n=f.get(e)).delete(t),0===n.size)&&f.delete(e)},U=1e3,Y="transitionend",X=function(e){return e=e&&window.CSS&&window.CSS.escape?e.replace(/#([^\s"#']+)/g,function(e,t){return"#".concat(CSS.escape(t))}):e},d=function(e){return!(!e||"object"!==hi(e))&&void 0!==(e=void 0!==e.jquery?e[0]:e).nodeType},Q=[],K=/[^.]*(?=\..*)\.|.*/,$=/\..*/,J=/::\d+$/,G={},Z=1,ee={mouseenter:"mouseover",mouseleave:"mouseout"},te=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function ne(e,t){return t&&"".concat(t,"::").concat(Z++)||e.uidEvent||Z++}function ie(e){var t=ne(e);return e.uidEvent=t,G[t]=G[t]||{},G[t]}function oe(e,t,n){var i=2<arguments.length&&void 0!==n?n:null;return Object.values(e).find(function(e){return e.callable===t&&e.delegationSelector===i})}function re(e,t,n){var i="string"==typeof t,t=!i&&t||n,n=ce(e);return[i,t,n=te.has(n)?n:e]}function ae(e,t,n,i,o){var r,a,s,c,l,u,f,d,p,h;"string"==typeof t&&e&&(r=(i=ui(re(t,n,i),3))[0],a=i[1],i=i[2],t in ee&&(s=a,a=function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return s.call(this,e)}),(l=oe(c=(c=ie(e))[i]||(c[i]={}),a,r?n:null))?l.oneOff=l.oneOff&&o:(l=ne(a,t.replace(K,"")),(t=r?(d=e,p=n,h=a,function e(t){for(var n=d.querySelectorAll(p),i=t.target;i&&i!==this;i=i.parentNode){var o,r=fi(n);try{for(r.s();!(o=r.n()).done;){var a=o.value;if(a===i)return le(t,{delegateTarget:i}),e.oneOff&&m.off(d,t.type,p,h),h.apply(i,[t])}}catch(e){r.e(e)}finally{r.f()}}}):(u=e,f=a,function e(t){return le(t,{delegateTarget:u}),e.oneOff&&m.off(u,t.type,f),f.apply(u,[t])})).delegationSelector=r?n:null,t.callable=a,t.oneOff=o,c[t.uidEvent=l]=t,e.addEventListener(i,t,r)))}function se(e,t,n,i,o){i=oe(t[n],i,o);i&&(e.removeEventListener(n,i,Boolean(o)),delete t[n][i.uidEvent])}function ce(e){return e=e.replace($,""),ee[e]||e}var m={on:function(e,t,n,i){ae(e,t,n,i,!1)},one:function(e,t,n,i){ae(e,t,n,i,!0)},off:function(e,t,n,i){if("string"==typeof t&&e){var i=ui(re(t,n,i),3),o=i[0],r=i[1],a=i[2],s=a!==t,c=ie(e),i=c[a]||{},l=t.startsWith(".");if(void 0!==r)return Object.keys(i).length?void se(e,c,a,r,o?n:null):void 0;if(l)for(var u=0,f=Object.keys(c);u<f.length;u++)for(var d=f[u],p=(w=_=b=y=g=v=m=h=p=void 0,e),h=c,m=d,v=t.slice(1),g=h[m]||{},y=0,b=Object.entries(g);y<b.length;y++){var _=ui(b[y],2),w=_[0],_=_[1];w.includes(v)&&se(p,h,m,_.callable,_.delegationSelector)}for(var k=0,E=Object.entries(i);k<E.length;k++){var O=ui(E[k],2),A=O[0],O=O[1],A=A.replace(J,"");s&&!t.includes(A)||se(e,c,a,O.callable,O.delegationSelector)}}},trigger:function(e,t,n){var i,o,r,a,s;return"string"==typeof t&&e?(s=H(),a=!(r=o=!(i=null)),t!==ce(t)&&s&&(i=s.Event(t,n),s(e).trigger(i),o=!i.isPropagationStopped(),r=!i.isImmediatePropagationStopped(),a=i.isDefaultPrevented()),s=le(new Event(t,{bubbles:o,cancelable:!0}),n),a&&s.preventDefault(),r&&e.dispatchEvent(s),s.defaultPrevented&&i&&i.preventDefault(),s):null}};function le(i,e){for(var o=0,r=Object.entries(1<arguments.length&&void 0!==e?e:{});o<r.length;o++)!function(){var e=ui(r[o],2),t=e[0],n=e[1];try{i[t]=n}catch(e){Object.defineProperty(i,t,{configurable:!0,get:function(){return n}})}}();return i}function ue(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function fe(e){return e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())})}function de(e){var t=e.getAttribute("data-bs-target");if(!t||"#"===t){e=e.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;t=(e=e.includes("#")&&!e.startsWith("#")?"#".concat(e.split("#")[1]):e)&&"#"!==e?e.trim():null}return t?t.split(",").map(function(e){return X(e)}).join(","):null}function n(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"hide",e="click.dismiss".concat(t.EVENT_KEY),i=t.NAME;m.on(document,e,'[data-bs-dismiss="'.concat(i,'"]'),function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),a(this)||(e=v.getElementFromSelector(this)||this.closest(".".concat(i)),t.getOrCreateInstance(e)[n]())})}var pe=function(e,t,n){e.setAttribute("data-bs-".concat(fe(t)),n)},he=function(e,t){e.removeAttribute("data-bs-".concat(fe(t)))},me=function(e){if(!e)return{};var t,n={},i=fi(Object.keys(e.dataset).filter(function(e){return e.startsWith("bs")&&!e.startsWith("bsConfig")}));try{for(i.s();!(t=i.n()).done;){var o=t.value,r=o.replace(/^bs/,"");n[r=r.charAt(0).toLowerCase()+r.slice(1)]=ue(e.dataset[o])}}catch(e){i.e(e)}finally{i.f()}return n},ve=function(e,t){return ue(e.getAttribute("data-bs-".concat(fe(t))))},p=function(){function e(){ci(this,e)}return li(e,[{key:"_getConfig",value:function(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}},{key:"_configAfterMerge",value:function(e){return e}},{key:"_mergeConfigObj",value:function(e,t){var n=d(t)?ve(t,"config"):{};return si(si(si(si({},this.constructor.Default),"object"===hi(n)?n:{}),d(t)?me(t):{}),"object"===hi(e)?e:{})}},{key:"_typeCheckConfig",value:function(e){for(var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.constructor.DefaultType,n=0,i=Object.entries(t);n<i.length;n++){var o=ui(i[n],2),r=o[0],o=o[1],a=e[r],a=d(a)?"element":null==(a=a)?"".concat(a):Object.prototype.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(a))throw new TypeError("".concat(this.constructor.NAME.toUpperCase(),': Option "').concat(r,'" provided type "').concat(a,'" but expected type "').concat(o,'".'))}}}],[{key:"Default",get:function(){return{}}},{key:"DefaultType",get:function(){return{}}},{key:"NAME",get:function(){throw new Error('You have to implement the static method "NAME", for each component!')}}]),e}(),h=function(){function i(e,t){var n;return ci(this,i),n=ni(this,i),(e=o(e))?(n._element=e,n._config=n._getConfig(t),R(n._element,n.constructor.DATA_KEY,ri(n)),n):ii(n)}return ai(i,p),li(i,[{key:"dispose",value:function(){W(this._element,this.constructor.DATA_KEY),m.off(this._element,this.constructor.EVENT_KEY);var e,t=fi(Object.getOwnPropertyNames(this));try{for(t.s();!(e=t.n()).done;)this[e.value]=null}catch(e){t.e(e)}finally{t.f()}}},{key:"_queueCallback",value:function(e,t){q(e,t,!(2<arguments.length&&void 0!==arguments[2])||arguments[2])}},{key:"_getConfig",value:function(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}}],[{key:"getInstance",value:function(e){return B(o(e),this.DATA_KEY)}},{key:"getOrCreateInstance",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return this.getInstance(e)||new this(e,"object"===hi(t)?t:null)}},{key:"VERSION",get:function(){return"5.3.6"}},{key:"DATA_KEY",get:function(){return"bs.".concat(this.NAME)}},{key:"EVENT_KEY",get:function(){return".".concat(this.DATA_KEY)}},{key:"eventName",value:function(e){return"".concat(e).concat(this.EVENT_KEY)}}]),i}(),v={find:function(e){var t,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:document.documentElement;return(t=[]).concat.apply(t,di(Element.prototype.querySelectorAll.call(n,e)))},findOne:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:document.documentElement;return Element.prototype.querySelector.call(t,e)},children:function(e,t){var n;return(n=[]).concat.apply(n,di(e.children)).filter(function(e){return e.matches(t)})},parents:function(e,t){for(var n=[],i=e.parentNode.closest(t);i;)n.push(i),i=i.parentNode.closest(t);return n},prev:function(e,t){for(var n=e.previousElementSibling;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next:function(e,t){for(var n=e.nextElementSibling;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren:function(e){var t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(function(e){return"".concat(e,':not([tabindex^="-"])')}).join(",");return this.find(t,e).filter(function(e){return!a(e)&&r(e)})},getSelectorFromElement:function(e){e=de(e);return e&&v.findOne(e)?e:null},getElementFromSelector:function(e){e=de(e);return e?v.findOne(e):null},getMultipleElementsFromSelector:function(e){e=de(e);return e?v.find(e):[]}},e=".".concat("bs.alert"),ge="close".concat(e),ye="closed".concat(e),e=function(){function n(){return ci(this,n),ni(this,n,arguments)}return ai(n,h),li(n,[{key:"close",value:function(){var e,t=this;m.trigger(this._element,ge).defaultPrevented||(this._element.classList.remove("show"),e=this._element.classList.contains("fade"),this._queueCallback(function(){return t._destroyElement()},this._element,e))}},{key:"_destroyElement",value:function(){this._element.remove(),m.trigger(this._element,ye),this.dispose()}}],[{key:"NAME",get:function(){return"alert"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError('No method named "'.concat(t,'"'));e[t](this)}})}}]),n}(),g=(n(e,"close"),t(e),".".concat("bs.button")),be='[data-bs-toggle="button"]',g="click".concat(g).concat(".data-api"),_e=function(){function n(){return ci(this,n),ni(this,n,arguments)}return ai(n,h),li(n,[{key:"toggle",value:function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}}],[{key:"NAME",get:function(){return"button"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this);"toggle"===t&&e[t]()})}}]),n}(),y=(m.on(document,g,be,function(e){e.preventDefault();e=e.target.closest(be);_e.getOrCreateInstance(e).toggle()}),t(_e),".bs.swipe"),we="touchstart".concat(y),ke="touchmove".concat(y),Ee="touchend".concat(y),Oe="pointerdown".concat(y),Ae="pointerup".concat(y),Te={endCallback:null,leftCallback:null,rightCallback:null},Ce={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"},xe=function(){function i(e,t){var n;return ci(this,i),((n=ni(this,i))._element=e)&&i.isSupported()?(n._config=n._getConfig(t),n._deltaX=0,n._supportPointerEvents=Boolean(window.PointerEvent),n._initEvents(),n):ii(n)}return ai(i,p),li(i,[{key:"dispose",value:function(){m.off(this._element,y)}},{key:"_start",value:function(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}},{key:"_end",value:function(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),l(this._config.endCallback)}},{key:"_move",value:function(e){this._deltaX=e.touches&&1<e.touches.length?0:e.touches[0].clientX-this._deltaX}},{key:"_handleSwipe",value:function(){var e=Math.abs(this._deltaX);e<=40||(e=e/this._deltaX,this._deltaX=0,e&&l(0<e?this._config.rightCallback:this._config.leftCallback))}},{key:"_initEvents",value:function(){var t=this;this._supportPointerEvents?(m.on(this._element,Oe,function(e){return t._start(e)}),m.on(this._element,Ae,function(e){return t._end(e)}),this._element.classList.add("pointer-event")):(m.on(this._element,we,function(e){return t._start(e)}),m.on(this._element,ke,function(e){return t._move(e)}),m.on(this._element,Ee,function(e){return t._end(e)}))}},{key:"_eventIsPointerPenTouch",value:function(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}}],[{key:"Default",get:function(){return Te}},{key:"DefaultType",get:function(){return Ce}},{key:"NAME",get:function(){return"swipe"}},{key:"isSupported",value:function(){return"ontouchstart"in document.documentElement||0<navigator.maxTouchPoints}}]),i}(),g=".".concat("bs.carousel"),b=".data-api",_="next",w="prev",k="left",Se="right",Le="slide".concat(g),je="slid".concat(g),Ne="keydown".concat(g),De="mouseenter".concat(g),Me="mouseleave".concat(g),Pe="dragstart".concat(g),E="load".concat(g).concat(b),g="click".concat(g).concat(b),Ie="carousel",Ve="active",Fe=".active",He=".carousel-item",qe=pi(pi({},"ArrowLeft",Se),"ArrowRight",k),ze={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Re={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"},Be=function(){function n(e,t){return ci(this,n),(e=ni(this,n,[e,t]))._interval=null,e._activeElement=null,e._isSliding=!1,e.touchTimeout=null,e._swipeHelper=null,e._indicatorsElement=v.findOne(".carousel-indicators",e._element),e._addEventListeners(),e._config.ride===Ie&&e.cycle(),e}return ai(n,h),li(n,[{key:"next",value:function(){this._slide(_)}},{key:"nextWhenVisible",value:function(){!document.hidden&&r(this._element)&&this.next()}},{key:"prev",value:function(){this._slide(w)}},{key:"pause",value:function(){this._isSliding&&V(this._element),this._clearInterval()}},{key:"cycle",value:function(){var e=this;this._clearInterval(),this._updateInterval(),this._interval=setInterval(function(){return e.nextWhenVisible()},this._config.interval)}},{key:"_maybeEnableCycle",value:function(){var e=this;this._config.ride&&(this._isSliding?m.one(this._element,je,function(){return e.cycle()}):this.cycle())}},{key:"to",value:function(e){var t,n=this,i=this._getItems();e>i.length-1||e<0||(this._isSliding?m.one(this._element,je,function(){return n.to(e)}):(t=this._getItemIndex(this._getActive()))!==e&&this._slide(t<e?_:w,i[e]))}},{key:"dispose",value:function(){this._swipeHelper&&this._swipeHelper.dispose(),ti(oi(n.prototype),"dispose",this).call(this)}},{key:"_configAfterMerge",value:function(e){return e.defaultInterval=e.interval,e}},{key:"_addEventListeners",value:function(){var t=this;this._config.keyboard&&m.on(this._element,Ne,function(e){return t._keydown(e)}),"hover"===this._config.pause&&(m.on(this._element,De,function(){return t.pause()}),m.on(this._element,Me,function(){return t._maybeEnableCycle()})),this._config.touch&&xe.isSupported()&&this._addTouchEventListeners()}},{key:"_addTouchEventListeners",value:function(){var e,t=this,n=fi(v.find(".carousel-item img",this._element));try{for(n.s();!(e=n.n()).done;){var i=e.value;m.on(i,Pe,function(e){return e.preventDefault()})}}catch(e){n.e(e)}finally{n.f()}this._swipeHelper=new xe(this._element,{leftCallback:function(){return t._slide(t._directionToOrder(k))},rightCallback:function(){return t._slide(t._directionToOrder(Se))},endCallback:function(){"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout(function(){return t._maybeEnableCycle()},500+t._config.interval))}})}},{key:"_keydown",value:function(e){var t;/input|textarea/i.test(e.target.tagName)||(t=qe[e.key])&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}},{key:"_getItemIndex",value:function(e){return this._getItems().indexOf(e)}},{key:"_setActiveIndicatorElement",value:function(e){var t;this._indicatorsElement&&((t=v.findOne(Fe,this._indicatorsElement)).classList.remove(Ve),t.removeAttribute("aria-current"),t=v.findOne('[data-bs-slide-to="'.concat(e,'"]'),this._indicatorsElement))&&(t.classList.add(Ve),t.setAttribute("aria-current","true"))}},{key:"_updateInterval",value:function(){var e=this._activeElement||this._getActive();e&&(e=Number.parseInt(e.getAttribute("data-bs-interval"),10),this._config.interval=e||this._config.defaultInterval)}},{key:"_slide",value:function(t){var n,e,i,o,r,a,s,c=this,l=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;this._isSliding||(n=this._getActive(),e=t===_,(i=l||z(this._getItems(),n,e,this._config.wrap))===n)||(o=this._getItemIndex(i),(r=function(e){return m.trigger(c._element,e,{relatedTarget:i,direction:c._orderToDirection(t),from:c._getItemIndex(n),to:o})})(Le).defaultPrevented)||n&&i&&(l=Boolean(this._interval),this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i,a=e?"carousel-item-start":"carousel-item-end",s=e?"carousel-item-next":"carousel-item-prev",i.classList.add(s),u(i),n.classList.add(a),i.classList.add(a),this._queueCallback(function(){i.classList.remove(a,s),i.classList.add(Ve),n.classList.remove(Ve,s,a),c._isSliding=!1,r(je)},n,this._isAnimated()),l)&&this.cycle()}},{key:"_isAnimated",value:function(){return this._element.classList.contains("slide")}},{key:"_getActive",value:function(){return v.findOne(".active.carousel-item",this._element)}},{key:"_getItems",value:function(){return v.find(He,this._element)}},{key:"_clearInterval",value:function(){this._interval&&(clearInterval(this._interval),this._interval=null)}},{key:"_directionToOrder",value:function(e){return c()?e===k?w:_:e===k?_:w}},{key:"_orderToDirection",value:function(e){return c()?e===w?k:Se:e===w?Se:k}}],[{key:"Default",get:function(){return ze}},{key:"DefaultType",get:function(){return Re}},{key:"NAME",get:function(){return"carousel"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("number"==typeof t)e.to(t);else if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),n}(),b=(m.on(document,g,"[data-bs-slide], [data-bs-slide-to]",function(e){var t=v.getElementFromSelector(this);t&&t.classList.contains(Ie)&&(e.preventDefault(),e=Be.getOrCreateInstance(t),(t=this.getAttribute("data-bs-slide-to"))?e.to(t):"next"===ve(this,"slide")?e.next():e.prev(),e._maybeEnableCycle())}),m.on(window,E,function(){var e,t=fi(v.find('[data-bs-ride="carousel"]'));try{for(t.s();!(e=t.n()).done;){var n=e.value;Be.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}}),t(Be),".".concat("bs.collapse")),We="show".concat(b),Ue="shown".concat(b),Ye="hide".concat(b),Xe="hidden".concat(b),g="click".concat(b).concat(".data-api"),Qe="show",O="collapse",Ke="collapsing",$e=":scope .".concat(O," .").concat(O),Je='[data-bs-toggle="collapse"]',Ge={parent:null,toggle:!0},Ze={parent:"(null|element)",toggle:"boolean"},et=function(){function c(e,t){var n;ci(this,c),(n=ni(this,c,[e,t]))._isTransitioning=!1,n._triggerArray=[];var i,o=fi(v.find(Je));try{for(o.s();!(i=o.n()).done;){var r=i.value,a=v.getSelectorFromElement(r),s=v.find(a).filter(function(e){return e===n._element});null!==a&&s.length&&n._triggerArray.push(r)}}catch(e){o.e(e)}finally{o.f()}return n._initializeChildren(),n._config.parent||n._addAriaAndCollapsedClass(n._triggerArray,n._isShown()),n._config.toggle&&n.toggle(),n}return ai(c,h),li(c,[{key:"toggle",value:function(){this._isShown()?this.hide():this.show()}},{key:"show",value:function(){var t=this;if(!this._isTransitioning&&!this._isShown()){var e=[];if(!(e=this._config.parent?this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(function(e){return e!==t._element}).map(function(e){return c.getOrCreateInstance(e,{toggle:!1})}):e).length||!e[0]._isTransitioning){var n=m.trigger(this._element,We);if(!n.defaultPrevented){var i,o=fi(e);try{for(o.s();!(i=o.n()).done;)i.value.hide()}catch(e){o.e(e)}finally{o.f()}var r=this._getDimension(),n=(this._element.classList.remove(O),this._element.classList.add(Ke),this._element.style[r]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0,r[0].toUpperCase()+r.slice(1)),e="scroll".concat(n);this._queueCallback(function(){t._isTransitioning=!1,t._element.classList.remove(Ke),t._element.classList.add(O,Qe),t._element.style[r]="",m.trigger(t._element,Ue)},this._element,!0),this._element.style[r]="".concat(this._element[e],"px")}}}}},{key:"hide",value:function(){var e=this;if(!this._isTransitioning&&this._isShown()){var t=m.trigger(this._element,Ye);if(!t.defaultPrevented){var n,t=this._getDimension(),i=(this._element.style[t]="".concat(this._element.getBoundingClientRect()[t],"px"),u(this._element),this._element.classList.add(Ke),this._element.classList.remove(O,Qe),fi(this._triggerArray));try{for(i.s();!(n=i.n()).done;){var o=n.value,r=v.getElementFromSelector(o);r&&!this._isShown(r)&&this._addAriaAndCollapsedClass([o],!1)}}catch(e){i.e(e)}finally{i.f()}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback(function(){e._isTransitioning=!1,e._element.classList.remove(Ke),e._element.classList.add(O),m.trigger(e._element,Xe)},this._element,!0)}}}},{key:"_isShown",value:function(){return(0<arguments.length&&void 0!==arguments[0]?arguments[0]:this._element).classList.contains(Qe)}},{key:"_configAfterMerge",value:function(e){return e.toggle=Boolean(e.toggle),e.parent=o(e.parent),e}},{key:"_getDimension",value:function(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}},{key:"_initializeChildren",value:function(){if(this._config.parent){var e,t=fi(this._getFirstLevelChildren(Je));try{for(t.s();!(e=t.n()).done;){var n=e.value,i=v.getElementFromSelector(n);i&&this._addAriaAndCollapsedClass([n],this._isShown(i))}}catch(e){t.e(e)}finally{t.f()}}}},{key:"_getFirstLevelChildren",value:function(e){var t=v.find($e,this._config.parent);return v.find(e,this._config.parent).filter(function(e){return!t.includes(e)})}},{key:"_addAriaAndCollapsedClass",value:function(e,t){if(e.length){var n,i=fi(e);try{for(i.s();!(n=i.n()).done;){var o=n.value;o.classList.toggle("collapsed",!t),o.setAttribute("aria-expanded",t)}}catch(e){i.e(e)}finally{i.f()}}}}],[{key:"Default",get:function(){return Ge}},{key:"DefaultType",get:function(){return Ze}},{key:"NAME",get:function(){return"collapse"}},{key:"jQueryInterface",value:function(t){var n={};return"string"==typeof t&&/show|hide/.test(t)&&(n.toggle=!1),this.each(function(){var e=c.getOrCreateInstance(this,n);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),c}(),tt=(m.on(document,g,Je,function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();var t,n=fi(v.getMultipleElementsFromSelector(this));try{for(n.s();!(t=n.n()).done;){var i=t.value;et.getOrCreateInstance(i,{toggle:!1}).toggle()}}catch(e){n.e(e)}finally{n.f()}}),t(et),"dropdown"),E=".".concat("bs.dropdown"),b=".data-api",nt="ArrowDown",it="hide".concat(E),ot="hidden".concat(E),rt="show".concat(E),at="shown".concat(E),g="click".concat(E).concat(b),A="keydown".concat(E).concat(b),E="keyup".concat(E).concat(b),T="show",C='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',st="".concat(C,".").concat(T),ct=".dropdown-menu",lt=c()?"top-end":"top-start",ut=c()?"top-start":"top-end",ft=c()?"bottom-end":"bottom-start",dt=c()?"bottom-start":"bottom-end",pt=c()?"left-start":"right-start",ht=c()?"right-start":"left-start",mt={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},vt={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"},x=function(){function c(e,t){return ci(this,c),(e=ni(this,c,[e,t]))._popper=null,e._parent=e._element.parentNode,e._menu=v.next(e._element,ct)[0]||v.prev(e._element,ct)[0]||v.findOne(ct,e._parent),e._inNavbar=e._detectNavbar(),e}return ai(c,h),li(c,[{key:"toggle",value:function(){return this._isShown()?this.hide():this.show()}},{key:"show",value:function(){if(!a(this._element)&&!this._isShown()){var e={relatedTarget:this._element},t=m.trigger(this._element,rt,e);if(!t.defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav")){var n,i=fi((t=[]).concat.apply(t,di(document.body.children)));try{for(i.s();!(n=i.n()).done;){var o=n.value;m.on(o,"mouseover",s)}}catch(e){i.e(e)}finally{i.f()}}this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(T),this._element.classList.add(T),m.trigger(this._element,at,e)}}}},{key:"hide",value:function(){var e;!a(this._element)&&this._isShown()&&(e={relatedTarget:this._element},this._completeHide(e))}},{key:"dispose",value:function(){this._popper&&this._popper.destroy(),ti(oi(c.prototype),"dispose",this).call(this)}},{key:"update",value:function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}},{key:"_completeHide",value:function(e){var t=m.trigger(this._element,it,e);if(!t.defaultPrevented){if("ontouchstart"in document.documentElement){var n,i=fi((t=[]).concat.apply(t,di(document.body.children)));try{for(i.s();!(n=i.n()).done;){var o=n.value;m.off(o,"mouseover",s)}}catch(e){i.e(e)}finally{i.f()}}this._popper&&this._popper.destroy(),this._menu.classList.remove(T),this._element.classList.remove(T),this._element.setAttribute("aria-expanded","false"),he(this._menu,"popper"),m.trigger(this._element,ot,e),this._element.focus()}}},{key:"_getConfig",value:function(e){if("object"!==hi((e=ti(oi(c.prototype),"_getConfig",this).call(this,e)).reference)||d(e.reference)||"function"==typeof e.reference.getBoundingClientRect)return e;throw new TypeError("".concat(tt.toUpperCase(),': Option "reference" provided type "object" without a required "getBoundingClientRect" method.'))}},{key:"_createPopper",value:function(){if(void 0===i)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");var e=this._element,t=("parent"===this._config.reference?e=this._parent:d(this._config.reference)?e=o(this._config.reference):"object"===hi(this._config.reference)&&(e=this._config.reference),this._getPopperConfig());this._popper=i.createPopper(e,this._menu,t)}},{key:"_isShown",value:function(){return this._menu.classList.contains(T)}},{key:"_getPlacement",value:function(){var e,t=this._parent;return t.classList.contains("dropend")?pt:t.classList.contains("dropstart")?ht:t.classList.contains("dropup-center")?"top":t.classList.contains("dropdown-center")?"bottom":(e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim(),t.classList.contains("dropup")?e?ut:lt:e?dt:ft)}},{key:"_detectNavbar",value:function(){return null!==this._element.closest(".navbar")}},{key:"_getOffset",value:function(){var t=this,n=this._config.offset;return"string"==typeof n?n.split(",").map(function(e){return Number.parseInt(e,10)}):"function"==typeof n?function(e){return n(e,t._element)}:n}},{key:"_getPopperConfig",value:function(){var e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return!this._inNavbar&&"static"!==this._config.display||(pe(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),si(si({},e),l(this._config.popperConfig,[void 0,e]))}},{key:"_selectMenuItem",value:function(e){var t=e.key,e=e.target,n=v.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(r);n.length&&z(n,e,t===nt,!n.includes(e)).focus()}}],[{key:"Default",get:function(){return mt}},{key:"DefaultType",get:function(){return vt}},{key:"NAME",get:function(){return tt}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=c.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}},{key:"clearMenus",value:function(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key)){var t,n=fi(v.find(st));try{for(n.s();!(t=n.n()).done;){var i,o,r,a=t.value,s=c.getInstance(a);s&&!1!==s._config.autoClose&&(o=(i=e.composedPath()).includes(s._menu),i.includes(s._element)||"inside"===s._config.autoClose&&!o||"outside"===s._config.autoClose&&o||s._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName))||(r={relatedTarget:s._element},"click"===e.type&&(r.clickEvent=e),s._completeHide(r)))}}catch(e){n.e(e)}finally{n.f()}}}},{key:"dataApiKeydownHandler",value:function(e){var t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,i=["ArrowUp",nt].includes(e.key);!i&&!n||t&&!n||(e.preventDefault(),t=this.matches(C)?this:v.prev(this,C)[0]||v.next(this,C)[0]||v.findOne(C,e.delegateTarget.parentNode),n=c.getOrCreateInstance(t),i?(e.stopPropagation(),n.show(),n._selectMenuItem(e)):n._isShown()&&(e.stopPropagation(),n.hide(),t.focus()))}}]),c}(),gt=(m.on(document,A,C,x.dataApiKeydownHandler),m.on(document,A,ct,x.dataApiKeydownHandler),m.on(document,g,x.clearMenus),m.on(document,E,x.clearMenus),m.on(document,g,C,function(e){e.preventDefault(),x.getOrCreateInstance(this).toggle()}),t(x),"backdrop"),yt="mousedown.bs.".concat(gt),bt={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},_t={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"},wt=function(){function n(e){var t;return ci(this,n),(t=ni(this,n))._config=t._getConfig(e),t._isAppended=!1,t._element=null,t}return ai(n,p),li(n,[{key:"show",value:function(e){var t;this._config.isVisible?(this._append(),t=this._getElement(),this._config.isAnimated&&u(t),t.classList.add("show"),this._emulateAnimation(function(){l(e)})):l(e)}},{key:"hide",value:function(e){var t=this;this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation(function(){t.dispose(),l(e)})):l(e)}},{key:"dispose",value:function(){this._isAppended&&(m.off(this._element,yt),this._element.remove(),this._isAppended=!1)}},{key:"_getElement",value:function(){var e;return this._element||((e=document.createElement("div")).className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e),this._element}},{key:"_configAfterMerge",value:function(e){return e.rootElement=o(e.rootElement),e}},{key:"_append",value:function(){var e,t=this;this._isAppended||(e=this._getElement(),this._config.rootElement.append(e),m.on(e,yt,function(){l(t._config.clickCallback)}),this._isAppended=!0)}},{key:"_emulateAnimation",value:function(e){q(e,this._getElement(),this._config.isAnimated)}}],[{key:"Default",get:function(){return bt}},{key:"DefaultType",get:function(){return _t}},{key:"NAME",get:function(){return gt}}]),n}(),kt=".".concat("bs.focustrap"),Et="focusin".concat(kt),Ot="keydown.tab".concat(kt),At="backward",Tt={autofocus:!0,trapElement:null},Ct={autofocus:"boolean",trapElement:"element"},xt=function(){function n(e){var t;return ci(this,n),(t=ni(this,n))._config=t._getConfig(e),t._isActive=!1,t._lastTabNavDirection=null,t}return ai(n,p),li(n,[{key:"activate",value:function(){var t=this;this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),m.off(document,kt),m.on(document,Et,function(e){return t._handleFocusin(e)}),m.on(document,Ot,function(e){return t._handleKeydown(e)}),this._isActive=!0)}},{key:"deactivate",value:function(){this._isActive&&(this._isActive=!1,m.off(document,kt))}},{key:"_handleFocusin",value:function(e){var t=this._config.trapElement;e.target===document||e.target===t||t.contains(e.target)||(0===(e=v.focusableChildren(t)).length?t:this._lastTabNavDirection===At?e[e.length-1]:e[0]).focus()}},{key:"_handleKeydown",value:function(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?At:"forward")}}],[{key:"Default",get:function(){return Tt}},{key:"DefaultType",get:function(){return Ct}},{key:"NAME",get:function(){return"focustrap"}}]),n}(),St=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Lt=".sticky-top",jt="padding-right",Nt="margin-right",Dt=function(){function e(){ci(this,e),this._element=document.body}return li(e,[{key:"getWidth",value:function(){var e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}},{key:"hide",value:function(){var t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,jt,function(e){return e+t}),this._setElementAttributes(St,jt,function(e){return e+t}),this._setElementAttributes(Lt,Nt,function(e){return e-t})}},{key:"reset",value:function(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,jt),this._resetElementAttributes(St,jt),this._resetElementAttributes(Lt,Nt)}},{key:"isOverflowing",value:function(){return 0<this.getWidth()}},{key:"_disableOverFlow",value:function(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}},{key:"_setElementAttributes",value:function(e,n,i){var o=this,r=this.getWidth();this._applyManipulationCallback(e,function(e){var t;e!==o._element&&window.innerWidth>e.clientWidth+r||(o._saveInitialAttribute(e,n),t=window.getComputedStyle(e).getPropertyValue(n),e.style.setProperty(n,"".concat(i(Number.parseFloat(t)),"px")))})}},{key:"_saveInitialAttribute",value:function(e,t){var n=e.style.getPropertyValue(t);n&&pe(e,t,n)}},{key:"_resetElementAttributes",value:function(e,n){this._applyManipulationCallback(e,function(e){var t=ve(e,n);null===t?e.style.removeProperty(n):(he(e,n),e.style.setProperty(n,t))})}},{key:"_applyManipulationCallback",value:function(e,t){if(d(e))t(e);else{var n,i=fi(v.find(e,this._element));try{for(i.s();!(n=i.n()).done;)t(n.value)}catch(e){i.e(e)}finally{i.f()}}}}]),e}(),S=".".concat("bs.modal"),Mt="hide".concat(S),Pt="hidePrevented".concat(S),It="hidden".concat(S),Vt="show".concat(S),Ft="shown".concat(S),Ht="resize".concat(S),qt="click.dismiss".concat(S),zt="mousedown.dismiss".concat(S),Rt="keydown.dismiss".concat(S),b="click".concat(S).concat(".data-api"),Bt="modal-open",Wt="modal-static",Ut={backdrop:!0,focus:!0,keyboard:!0},Yt={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"},L=function(){function i(e,t){return ci(this,i),(e=ni(this,i,[e,t]))._dialog=v.findOne(".modal-dialog",e._element),e._backdrop=e._initializeBackDrop(),e._focustrap=e._initializeFocusTrap(),e._isShown=!1,e._isTransitioning=!1,e._scrollBar=new Dt,e._addEventListeners(),e}return ai(i,h),li(i,[{key:"toggle",value:function(e){return this._isShown?this.hide():this.show(e)}},{key:"show",value:function(e){var t=this;this._isShown||this._isTransitioning||m.trigger(this._element,Vt,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Bt),this._adjustDialog(),this._backdrop.show(function(){return t._showElement(e)}))}},{key:"hide",value:function(){var e=this;!this._isShown||this._isTransitioning||m.trigger(this._element,Mt).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove("show"),this._queueCallback(function(){return e._hideModal()},this._element,this._isAnimated()))}},{key:"dispose",value:function(){m.off(window,S),m.off(this._dialog,S),this._backdrop.dispose(),this._focustrap.deactivate(),ti(oi(i.prototype),"dispose",this).call(this)}},{key:"handleUpdate",value:function(){this._adjustDialog()}},{key:"_initializeBackDrop",value:function(){return new wt({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}},{key:"_initializeFocusTrap",value:function(){return new xt({trapElement:this._element})}},{key:"_showElement",value:function(e){var t=this,n=(document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,v.findOne(".modal-body",this._dialog));n&&(n.scrollTop=0),u(this._element),this._element.classList.add("show");this._queueCallback(function(){t._config.focus&&t._focustrap.activate(),t._isTransitioning=!1,m.trigger(t._element,Ft,{relatedTarget:e})},this._dialog,this._isAnimated())}},{key:"_addEventListeners",value:function(){var n=this;m.on(this._element,Rt,function(e){"Escape"===e.key&&(n._config.keyboard?n.hide():n._triggerBackdropTransition())}),m.on(window,Ht,function(){n._isShown&&!n._isTransitioning&&n._adjustDialog()}),m.on(this._element,zt,function(t){m.one(n._element,qt,function(e){n._element===t.target&&n._element===e.target&&("static"===n._config.backdrop?n._triggerBackdropTransition():n._config.backdrop&&n.hide())})})}},{key:"_hideModal",value:function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(function(){document.body.classList.remove(Bt),e._resetAdjustments(),e._scrollBar.reset(),m.trigger(e._element,It)})}},{key:"_isAnimated",value:function(){return this._element.classList.contains("fade")}},{key:"_triggerBackdropTransition",value:function(){var e,t,n=this;m.trigger(this._element,Pt).defaultPrevented||(e=this._element.scrollHeight>document.documentElement.clientHeight,"hidden"===(t=this._element.style.overflowY))||this._element.classList.contains(Wt)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Wt),this._queueCallback(function(){n._element.classList.remove(Wt),n._queueCallback(function(){n._element.style.overflowY=t},n._dialog)},this._dialog),this._element.focus())}},{key:"_adjustDialog",value:function(){var e,t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),i=0<n;i&&!t&&(e=c()?"paddingLeft":"paddingRight",this._element.style[e]="".concat(n,"px")),!i&&t&&(e=c()?"paddingRight":"paddingLeft",this._element.style[e]="".concat(n,"px"))}},{key:"_resetAdjustments",value:function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}}],[{key:"Default",get:function(){return Ut}},{key:"DefaultType",get:function(){return Yt}},{key:"NAME",get:function(){return"modal"}},{key:"jQueryInterface",value:function(t,n){return this.each(function(){var e=i.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t](n)}})}}]),i}(),A=(m.on(document,b,'[data-bs-toggle="modal"]',function(e){var t=this,n=v.getElementFromSelector(this),e=(["A","AREA"].includes(this.tagName)&&e.preventDefault(),m.one(n,Vt,function(e){e.defaultPrevented||m.one(n,It,function(){r(t)&&t.focus()})}),v.findOne(".modal.show"));e&&L.getInstance(e).hide(),L.getOrCreateInstance(n).toggle(this)}),n(L),t(L),".".concat("bs.offcanvas")),E=".data-api",g="load".concat(A).concat(E),Xt="showing",Qt=".offcanvas.show",Kt="show".concat(A),$t="shown".concat(A),Jt="hide".concat(A),Gt="hidePrevented".concat(A),Zt="hidden".concat(A),b="resize".concat(A),E="click".concat(A).concat(E),en="keydown.dismiss".concat(A),tn={backdrop:!0,keyboard:!0,scroll:!1},nn={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"},j=function(){function n(e,t){return ci(this,n),(e=ni(this,n,[e,t]))._isShown=!1,e._backdrop=e._initializeBackDrop(),e._focustrap=e._initializeFocusTrap(),e._addEventListeners(),e}return ai(n,h),li(n,[{key:"toggle",value:function(e){return this._isShown?this.hide():this.show(e)}},{key:"show",value:function(e){var t=this;this._isShown||m.trigger(this._element,Kt,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Dt).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Xt),this._queueCallback(function(){t._config.scroll&&!t._config.backdrop||t._focustrap.activate(),t._element.classList.add("show"),t._element.classList.remove(Xt),m.trigger(t._element,$t,{relatedTarget:e})},this._element,!0))}},{key:"hide",value:function(){var e=this;this._isShown&&!m.trigger(this._element,Jt).defaultPrevented&&(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add("hiding"),this._backdrop.hide(),this._queueCallback(function(){e._element.classList.remove("show","hiding"),e._element.removeAttribute("aria-modal"),e._element.removeAttribute("role"),e._config.scroll||(new Dt).reset(),m.trigger(e._element,Zt)},this._element,!0))}},{key:"dispose",value:function(){this._backdrop.dispose(),this._focustrap.deactivate(),ti(oi(n.prototype),"dispose",this).call(this)}},{key:"_initializeBackDrop",value:function(){var e=this,t=Boolean(this._config.backdrop);return new wt({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?function(){"static"===e._config.backdrop?m.trigger(e._element,Gt):e.hide()}:null})}},{key:"_initializeFocusTrap",value:function(){return new xt({trapElement:this._element})}},{key:"_addEventListeners",value:function(){var t=this;m.on(this._element,en,function(e){"Escape"===e.key&&(t._config.keyboard?t.hide():m.trigger(t._element,Gt))})}}],[{key:"Default",get:function(){return tn}},{key:"DefaultType",get:function(){return nn}},{key:"NAME",get:function(){return"offcanvas"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError('No method named "'.concat(t,'"'));e[t](this)}})}}]),n}(),A=(m.on(document,E,'[data-bs-toggle="offcanvas"]',function(e){var t=this,n=v.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),a(this)||(m.one(n,Zt,function(){r(t)&&t.focus()}),(e=v.findOne(Qt))&&e!==n&&j.getInstance(e).hide(),j.getOrCreateInstance(n).toggle(this))}),m.on(window,g,function(){var e,t=fi(v.find(Qt));try{for(t.s();!(e=t.n()).done;){var n=e.value;j.getOrCreateInstance(n).show()}}catch(e){t.e(e)}finally{t.f()}}),m.on(window,b,function(){var e,t=fi(v.find("[aria-modal][class*=show][class*=offcanvas-]"));try{for(t.s();!(e=t.n()).done;){var n=e.value;"fixed"!==getComputedStyle(n).position&&j.getOrCreateInstance(n).hide()}}catch(e){t.e(e)}finally{t.f()}}),n(j),t(j),{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]}),on=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),rn=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i;function an(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);var i,n=(new window.DOMParser).parseFromString(e,"text/html"),o=fi((e=[]).concat.apply(e,di(n.body.querySelectorAll("*"))));try{for(o.s();!(i=o.n()).done;){var r,a=i.value,s=a.nodeName.toLowerCase();if(Object.keys(t).includes(s)){var c,l=(r=[]).concat.apply(r,di(a.attributes)),u=[].concat(t["*"]||[],t[s]||[]),f=fi(l);try{for(f.s();!(c=f.n()).done;){var d=c.value;!function(e,t){var n=e.nodeName.toLowerCase();return t.includes(n)?!on.has(n)||Boolean(rn.test(e.nodeValue)):t.filter(function(e){return e instanceof RegExp}).some(function(e){return e.test(n)})}(d,u)&&a.removeAttribute(d.nodeName)}}catch(e){f.e(e)}finally{f.f()}}else a.remove()}}catch(e){o.e(e)}finally{o.f()}return n.body.innerHTML}var sn={allowList:A,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},cn={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},ln={entry:"(string|element|function|null)",selector:"(string|element)"},un=function(){function r(e){var t;return ci(this,r),(t=ni(this,r))._config=t._getConfig(e),t}return ai(r,p),li(r,[{key:"getContent",value:function(){var t=this;return Object.values(this._config.content).map(function(e){return t._resolvePossibleFunction(e)}).filter(Boolean)}},{key:"hasContent",value:function(){return 0<this.getContent().length}},{key:"changeContent",value:function(e){return this._checkContent(e),this._config.content=si(si({},this._config.content),e),this}},{key:"toHtml",value:function(){var e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(var t=0,n=Object.entries(this._config.content);t<n.length;t++){var i=ui(n[t],2),o=i[0],i=i[1];this._setContent(e,i,o)}var r,a=e.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&(r=a.classList).add.apply(r,di(s.split(" "))),a}},{key:"_typeCheckConfig",value:function(e){ti(oi(r.prototype),"_typeCheckConfig",this).call(this,e),this._checkContent(e.content)}},{key:"_checkContent",value:function(e){for(var t=0,n=Object.entries(e);t<n.length;t++){var i=ui(n[t],2),o=i[0],i=i[1];ti(oi(r.prototype),"_typeCheckConfig",this).call(this,{selector:o,entry:i},ln)}}},{key:"_setContent",value:function(e,t,n){n=v.findOne(n,e);n&&((t=this._resolvePossibleFunction(t))?d(t)?this._putElementInTemplate(o(t),n):this._config.html?n.innerHTML=this._maybeSanitize(t):n.textContent=t:n.remove())}},{key:"_maybeSanitize",value:function(e){return this._config.sanitize?an(e,this._config.allowList,this._config.sanitizeFn):e}},{key:"_resolvePossibleFunction",value:function(e){return l(e,[void 0,this])}},{key:"_putElementInTemplate",value:function(e,t){this._config.html?(t.innerHTML="",t.append(e)):t.textContent=e.textContent}}],[{key:"Default",get:function(){return sn}},{key:"DefaultType",get:function(){return cn}},{key:"NAME",get:function(){return"TemplateFactory"}}]),r}(),fn=new Set(["sanitize","allowList","sanitizeFn"]),dn="fade",pn="show",hn=".".concat("modal"),mn="hide.bs.modal",N="hover",vn="focus",gn={AUTO:"auto",TOP:"top",RIGHT:c()?"left":"right",BOTTOM:"bottom",LEFT:c()?"right":"left"},yn={allowList:A,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},bn={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"},D=function(){function n(e,t){if(ci(this,n),void 0===i)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");return(e=ni(this,n,[e,t]))._isEnabled=!0,e._timeout=0,e._isHovered=null,e._activeTrigger={},e._popper=null,e._templateFactory=null,e._newContent=null,e.tip=null,e._setListeners(),e._config.selector||e._fixTitle(),e}return ai(n,h),li(n,[{key:"enable",value:function(){this._isEnabled=!0}},{key:"disable",value:function(){this._isEnabled=!1}},{key:"toggleEnabled",value:function(){this._isEnabled=!this._isEnabled}},{key:"toggle",value:function(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}},{key:"dispose",value:function(){clearTimeout(this._timeout),m.off(this._element.closest(hn),mn,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),ti(oi(n.prototype),"dispose",this).call(this)}},{key:"show",value:function(){var e=this;if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this._isWithContent()&&this._isEnabled){var t=m.trigger(this._element,this.constructor.eventName("show")),n=(F(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(!t.defaultPrevented&&n){this._disposePopper();t=this._getTipElement(),n=(this._element.setAttribute("aria-describedby",t.getAttribute("id")),this._config.container);if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(t),m.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(t),t.classList.add(pn),"ontouchstart"in document.documentElement){var i,o=fi((n=[]).concat.apply(n,di(document.body.children)));try{for(o.s();!(i=o.n()).done;){var r=i.value;m.on(r,"mouseover",s)}}catch(e){o.e(e)}finally{o.f()}}this._queueCallback(function(){m.trigger(e._element,e.constructor.eventName("shown")),!1===e._isHovered&&e._leave(),e._isHovered=!1},this.tip,this._isAnimated())}}}},{key:"hide",value:function(){var e=this;if(this._isShown()){var t=m.trigger(this._element,this.constructor.eventName("hide"));if(!t.defaultPrevented){if(this._getTipElement().classList.remove(pn),"ontouchstart"in document.documentElement){var n,i=fi((t=[]).concat.apply(t,di(document.body.children)));try{for(i.s();!(n=i.n()).done;){var o=n.value;m.off(o,"mouseover",s)}}catch(e){i.e(e)}finally{i.f()}}this._activeTrigger.click=!1,this._activeTrigger[vn]=!1,this._activeTrigger[N]=!1,this._isHovered=null;this._queueCallback(function(){e._isWithActiveTrigger()||(e._isHovered||e._disposePopper(),e._element.removeAttribute("aria-describedby"),m.trigger(e._element,e.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}}},{key:"update",value:function(){this._popper&&this._popper.update()}},{key:"_isWithContent",value:function(){return Boolean(this._getTitle())}},{key:"_getTipElement",value:function(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}},{key:"_createTipElement",value:function(e){e=this._getTemplateFactory(e).toHtml();if(!e)return null;e.classList.remove(dn,pn),e.classList.add("bs-".concat(this.constructor.NAME,"-auto"));var t=function(e){for(;e+=Math.floor(1e6*Math.random()),document.getElementById(e););return e}(this.constructor.NAME).toString();return e.setAttribute("id",t),this._isAnimated()&&e.classList.add(dn),e}},{key:"setContent",value:function(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}},{key:"_getTemplateFactory",value:function(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new un(si(si({},this._config),{},{content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)})),this._templateFactory}},{key:"_getContentForTemplate",value:function(){return pi({},".tooltip-inner",this._getTitle())}},{key:"_getTitle",value:function(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}},{key:"_initializeOnDelegatedTarget",value:function(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}},{key:"_isAnimated",value:function(){return this._config.animation||this.tip&&this.tip.classList.contains(dn)}},{key:"_isShown",value:function(){return this.tip&&this.tip.classList.contains(pn)}},{key:"_createPopper",value:function(e){var t=l(this._config.placement,[this,e,this._element]),t=gn[t.toUpperCase()];return i.createPopper(this._element,e,this._getPopperConfig(t))}},{key:"_getOffset",value:function(){var t=this,n=this._config.offset;return"string"==typeof n?n.split(",").map(function(e){return Number.parseInt(e,10)}):"function"==typeof n?function(e){return n(e,t._element)}:n}},{key:"_resolvePossibleFunction",value:function(e){return l(e,[this._element,this._element])}},{key:"_getPopperConfig",value:function(e){var t=this,e={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:".".concat(this.constructor.NAME,"-arrow")}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:function(e){t._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return si(si({},e),l(this._config.popperConfig,[void 0,e]))}},{key:"_setListeners",value:function(){var e,n=this,t=fi(this._config.trigger.split(" "));try{for(t.s();!(e=t.n()).done;){var i,o,r=e.value;"click"===r?m.on(this._element,this.constructor.eventName("click"),this._config.selector,function(e){n._initializeOnDelegatedTarget(e).toggle()}):"manual"!==r&&(i=r===N?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),o=r===N?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout"),m.on(this._element,i,this._config.selector,function(e){var t=n._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?vn:N]=!0,t._enter()}),m.on(this._element,o,this._config.selector,function(e){var t=n._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?vn:N]=t._element.contains(e.relatedTarget),t._leave()}))}}catch(e){t.e(e)}finally{t.f()}this._hideModalHandler=function(){n._element&&n.hide()},m.on(this._element.closest(hn),mn,this._hideModalHandler)}},{key:"_fixTitle",value:function(){var e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}},{key:"_enter",value:function(){var e=this;this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(function(){e._isHovered&&e.show()},this._config.delay.show))}},{key:"_leave",value:function(){var e=this;this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(function(){e._isHovered||e.hide()},this._config.delay.hide))}},{key:"_setTimeout",value:function(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}},{key:"_isWithActiveTrigger",value:function(){return Object.values(this._activeTrigger).includes(!0)}},{key:"_getConfig",value:function(e){for(var t=me(this._element),n=0,i=Object.keys(t);n<i.length;n++){var o=i[n];fn.has(o)&&delete t[o]}return e=si(si({},t),"object"===hi(e)&&e?e:{}),e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}},{key:"_configAfterMerge",value:function(e){return e.container=!1===e.container?document.body:o(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}},{key:"_getDelegateConfig",value:function(){for(var e={},t=0,n=Object.entries(this._config);t<n.length;t++){var i=ui(n[t],2),o=i[0],i=i[1];this.constructor.Default[o]!==i&&(e[o]=i)}return e.selector=!1,e.trigger="manual",e}},{key:"_disposePopper",value:function(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}}],[{key:"Default",get:function(){return yn}},{key:"DefaultType",get:function(){return bn}},{key:"NAME",get:function(){return"tooltip"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),n}(),_n=(t(D),si(si({},D.Default),{},{content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"})),wn=si(si({},D.DefaultType),{},{content:"(null|string|element|function)"}),E=function(){function n(){return ci(this,n),ni(this,n,arguments)}return ai(n,D),li(n,[{key:"_isWithContent",value:function(){return this._getTitle()||this._getContent()}},{key:"_getContentForTemplate",value:function(){return pi(pi({},".popover-header",this._getTitle()),".popover-body",this._getContent())}},{key:"_getContent",value:function(){return this._resolvePossibleFunction(this._config.content)}}],[{key:"Default",get:function(){return _n}},{key:"DefaultType",get:function(){return wn}},{key:"NAME",get:function(){return"popover"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),n}(),g=(t(E),".".concat("bs.scrollspy")),kn="activate".concat(g),En="click".concat(g),b="load".concat(g).concat(".data-api"),M="active",On="[href]",A=".nav-link",An="".concat(A,", ").concat(".nav-item"," > ").concat(A,", ").concat(".list-group-item"),Tn={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Cn={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"},xn=function(){function n(e,t){return ci(this,n),(e=ni(this,n,[e,t]))._targetLinks=new Map,e._observableSections=new Map,e._rootElement="visible"===getComputedStyle(e._element).overflowY?null:e._element,e._activeTarget=null,e._observer=null,e._previousScrollData={visibleEntryTop:0,parentScrollTop:0},e.refresh(),e}return ai(n,h),li(n,[{key:"refresh",value:function(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();var e,t=fi(this._observableSections.values());try{for(t.s();!(e=t.n()).done;){var n=e.value;this._observer.observe(n)}}catch(e){t.e(e)}finally{t.f()}}},{key:"dispose",value:function(){this._observer.disconnect(),ti(oi(n.prototype),"dispose",this).call(this)}},{key:"_configAfterMerge",value:function(e){return e.target=o(e.target)||document.body,e.rootMargin=e.offset?"".concat(e.offset,"px 0px -30%"):e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(function(e){return Number.parseFloat(e)})),e}},{key:"_maybeEnableSmoothScroll",value:function(){var n=this;this._config.smoothScroll&&(m.off(this._config.target,En),m.on(this._config.target,En,On,function(e){var t=n._observableSections.get(e.target.hash);t&&(e.preventDefault(),e=n._rootElement||window,t=t.offsetTop-n._element.offsetTop,e.scrollTo?e.scrollTo({top:t,behavior:"smooth"}):e.scrollTop=t)}))}},{key:"_getNewObserver",value:function(){var t=this,e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(function(e){return t._observerCallback(e)},e)}},{key:"_observerCallback",value:function(e){function t(e){i._previousScrollData.visibleEntryTop=e.target.offsetTop,i._process(o(e))}var n,i=this,o=function(e){return i._targetLinks.get("#".concat(e.target.id))},r=(this._rootElement||document.documentElement).scrollTop,a=r>=this._previousScrollData.parentScrollTop,s=(this._previousScrollData.parentScrollTop=r,fi(e));try{for(s.s();!(n=s.n()).done;){var c=n.value;if(c.isIntersecting){var l=c.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(a&&l){if(t(c),r)continue;return}a||l||t(c)}else this._activeTarget=null,this._clearActiveClass(o(c))}}catch(e){s.e(e)}finally{s.f()}}},{key:"_initializeTargetsAndObservables",value:function(){this._targetLinks=new Map,this._observableSections=new Map;var e,t=fi(v.find(On,this._config.target));try{for(t.s();!(e=t.n()).done;){var n,i=e.value;i.hash&&!a(i)&&(n=v.findOne(decodeURI(i.hash),this._element),r(n))&&(this._targetLinks.set(decodeURI(i.hash),i),this._observableSections.set(i.hash,n))}}catch(e){t.e(e)}finally{t.f()}}},{key:"_process",value:function(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),(this._activeTarget=e).classList.add(M),this._activateParents(e),m.trigger(this._element,kn,{relatedTarget:e}))}},{key:"_activateParents",value:function(e){if(e.classList.contains("dropdown-item"))v.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(M);else{var t,n=fi(v.parents(e,".nav, .list-group"));try{for(n.s();!(t=n.n()).done;){var i,o=t.value,r=fi(v.prev(o,An));try{for(r.s();!(i=r.n()).done;)i.value.classList.add(M)}catch(e){r.e(e)}finally{r.f()}}}catch(e){n.e(e)}finally{n.f()}}}},{key:"_clearActiveClass",value:function(e){e.classList.remove(M);var t,n=fi(v.find("".concat(On,".").concat(M),e));try{for(n.s();!(t=n.n()).done;)t.value.classList.remove(M)}catch(e){n.e(e)}finally{n.f()}}}],[{key:"Default",get:function(){return Tn}},{key:"DefaultType",get:function(){return Cn}},{key:"NAME",get:function(){return"scrollspy"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),n}(),g=(m.on(window,b,function(){var e,t=fi(v.find('[data-bs-spy="scroll"]'));try{for(t.s();!(e=t.n()).done;){var n=e.value;xn.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}}),t(xn),".".concat("bs.tab")),Sn="hide".concat(g),Ln="hidden".concat(g),jn="show".concat(g),Nn="shown".concat(g),A="click".concat(g),Dn="keydown".concat(g),b="load".concat(g),Mn="ArrowRight",Pn="ArrowDown",In="Home",P="active",Vn="show",Fn=".dropdown-toggle",g=":not(".concat(Fn,")"),g=".nav-link".concat(g,", .list-group-item").concat(g,', [role="tab"]').concat(g),Hn='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',qn="".concat(g,", ").concat(Hn),zn=".".concat(P,'[data-bs-toggle="tab"], .').concat(P,'[data-bs-toggle="pill"], .').concat(P,'[data-bs-toggle="list"]'),Rn=function(){function i(e){var t;return ci(this,i),(t=ni(this,i,[e]))._parent=t._element.closest('.list-group, .nav, [role="tablist"]'),t._parent?(t._setInitialAttributes(t._parent,t._getChildren()),m.on(t._element,Dn,function(e){return t._keydown(e)}),t):ii(t)}return ai(i,h),li(i,[{key:"show",value:function(){var e,t,n=this._element;this._elemIsActive(n)||(t=(e=this._getActiveElem())?m.trigger(e,Sn,{relatedTarget:n}):null,m.trigger(n,jn,{relatedTarget:e}).defaultPrevented)||t&&t.defaultPrevented||(this._deactivate(e,n),this._activate(n,e))}},{key:"_activate",value:function(e,t){var n=this;e&&(e.classList.add(P),this._activate(v.getElementFromSelector(e)),this._queueCallback(function(){"tab"!==e.getAttribute("role")?e.classList.add(Vn):(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),n._toggleDropDown(e,!0),m.trigger(e,Nn,{relatedTarget:t}))},e,e.classList.contains("fade")))}},{key:"_deactivate",value:function(e,t){var n=this;e&&(e.classList.remove(P),e.blur(),this._deactivate(v.getElementFromSelector(e)),this._queueCallback(function(){"tab"!==e.getAttribute("role")?e.classList.remove(Vn):(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),n._toggleDropDown(e,!1),m.trigger(e,Ln,{relatedTarget:t}))},e,e.classList.contains("fade")))}},{key:"_keydown",value:function(e){var t,n;["ArrowLeft",Mn,"ArrowUp",Pn,In,"End"].includes(e.key)&&(e.stopPropagation(),e.preventDefault(),n=this._getChildren().filter(function(e){return!a(e)}),n=[In,"End"].includes(e.key)?n[e.key===In?0:n.length-1]:(t=[Mn,Pn].includes(e.key),z(n,e.target,t,!0)))&&(n.focus({preventScroll:!0}),i.getOrCreateInstance(n).show())}},{key:"_getChildren",value:function(){return v.find(qn,this._parent)}},{key:"_getActiveElem",value:function(){var t=this;return this._getChildren().find(function(e){return t._elemIsActive(e)})||null}},{key:"_setInitialAttributes",value:function(e,t){this._setAttributeIfNotExists(e,"role","tablist");var n,i=fi(t);try{for(i.s();!(n=i.n()).done;){var o=n.value;this._setInitialAttributesOnChild(o)}}catch(e){i.e(e)}finally{i.f()}}},{key:"_setInitialAttributesOnChild",value:function(e){e=this._getInnerElement(e);var t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}},{key:"_setInitialAttributesOnTargetPanel",value:function(e){var t=v.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id)&&this._setAttributeIfNotExists(t,"aria-labelledby","".concat(e.id))}},{key:"_toggleDropDown",value:function(e,n){var i=this._getOuterElement(e);i.classList.contains("dropdown")&&((e=function(e,t){e=v.findOne(e,i);e&&e.classList.toggle(t,n)})(Fn,P),e(".dropdown-menu",Vn),i.setAttribute("aria-expanded",n))}},{key:"_setAttributeIfNotExists",value:function(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}},{key:"_elemIsActive",value:function(e){return e.classList.contains(P)}},{key:"_getInnerElement",value:function(e){return e.matches(qn)?e:v.findOne(qn,e)}},{key:"_getOuterElement",value:function(e){return e.closest(".nav-item, .list-group-item")||e}}],[{key:"NAME",get:function(){return"tab"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=i.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError('No method named "'.concat(t,'"'));e[t]()}})}}]),i}(),g=(m.on(document,A,Hn,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),a(this)||Rn.getOrCreateInstance(this).show()}),m.on(window,b,function(){var e,t=fi(v.find(zn));try{for(t.s();!(e=t.n()).done;){var n=e.value;Rn.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}}),t(Rn),".".concat("bs.toast")),Bn="mouseover".concat(g),Wn="mouseout".concat(g),Un="focusin".concat(g),Yn="focusout".concat(g),Xn="hide".concat(g),Qn="hidden".concat(g),Kn="show".concat(g),$n="shown".concat(g),Jn="show",Gn="showing",Zn={animation:"boolean",autohide:"boolean",delay:"number"},ei={animation:!0,autohide:!0,delay:5e3},A=function(){function n(e,t){return ci(this,n),(e=ni(this,n,[e,t]))._timeout=null,e._hasMouseInteraction=!1,e._hasKeyboardInteraction=!1,e._setListeners(),e}return ai(n,h),li(n,[{key:"show",value:function(){var e=this;m.trigger(this._element,Kn).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),u(this._element),this._element.classList.add(Jn,Gn),this._queueCallback(function(){e._element.classList.remove(Gn),m.trigger(e._element,$n),e._maybeScheduleHide()},this._element,this._config.animation))}},{key:"hide",value:function(){var e=this;this.isShown()&&!m.trigger(this._element,Xn).defaultPrevented&&(this._element.classList.add(Gn),this._queueCallback(function(){e._element.classList.add("hide"),e._element.classList.remove(Gn,Jn),m.trigger(e._element,Qn)},this._element,this._config.animation))}},{key:"dispose",value:function(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Jn),ti(oi(n.prototype),"dispose",this).call(this)}},{key:"isShown",value:function(){return this._element.classList.contains(Jn)}},{key:"_maybeScheduleHide",value:function(){var e=this;!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(function(){e.hide()},this._config.delay))}},{key:"_onInteraction",value:function(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}t?this._clearTimeout():(e=e.relatedTarget,this._element===e||this._element.contains(e)||this._maybeScheduleHide())}},{key:"_setListeners",value:function(){var t=this;m.on(this._element,Bn,function(e){return t._onInteraction(e,!0)}),m.on(this._element,Wn,function(e){return t._onInteraction(e,!1)}),m.on(this._element,Un,function(e){return t._onInteraction(e,!0)}),m.on(this._element,Yn,function(e){return t._onInteraction(e,!1)})}},{key:"_clearTimeout",value:function(){clearTimeout(this._timeout),this._timeout=null}}],[{key:"Default",get:function(){return ei}},{key:"DefaultType",get:function(){return Zn}},{key:"NAME",get:function(){return"toast"}},{key:"jQueryInterface",value:function(t){return this.each(function(){var e=n.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'.concat(t,'"'));e[t](this)}})}}]),n}();return n(A),t(A),{Alert:e,Button:_e,Carousel:Be,Collapse:et,Dropdown:x,Modal:L,Offcanvas:j,Popover:E,ScrollSpy:xn,Tab:Rn,Toast:A,Tooltip:D}},"object"===((u=void 0)===n?"undefined":hi(n))&&void 0!==t?t.exports=f(e("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],f):(u="undefined"!=typeof globalThis?globalThis:self).bootstrap=f(u.Popper)},{"@popperjs/core":1}],5:[function(e,t,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o;o=function(e){var i=function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t(e,t,n){var c=this;this.endVal=t,this.options=n,this.version="2.8.0",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:"",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(e){c.startTime||(c.startTime=e);var e=e-c.startTime,t=(c.remaining=c.duration-e,c.useEasing?c.countDown?c.frameVal=c.startVal-c.easingFn(e,0,c.startVal-c.endVal,c.duration):c.frameVal=c.easingFn(e,c.startVal,c.endVal-c.startVal,c.duration):c.frameVal=c.startVal+(c.endVal-c.startVal)*(e/c.duration),c.countDown?c.frameVal<c.endVal:c.frameVal>c.endVal);c.frameVal=t?c.endVal:c.frameVal,c.frameVal=Number(c.frameVal.toFixed(c.options.decimalPlaces)),c.printValue(c.frameVal),e<c.duration?c.rAF=requestAnimationFrame(c.count):null!==c.finalEndVal?c.update(c.finalEndVal):c.options.onCompleteCallback&&c.options.onCompleteCallback()},this.formatNumber=function(e){var t=e<0?"-":"",e=Math.abs(e).toFixed(c.options.decimalPlaces),e=(e+="").split("."),n=e[0],e=1<e.length?c.options.decimal+e[1]:"";if(c.options.useGrouping){for(var i="",o=3,r=0,a=0,s=n.length;a<s;++a)c.options.useIndianSeparators&&4===a&&(o=2,r=1),0!==a&&r%o==0&&(i=c.options.separator+i),r++,i=n[s-a-1]+i;n=i}return c.options.numerals&&c.options.numerals.length&&(n=n.replace(/[0-9]/g,function(e){return c.options.numerals[+e]}),e=e.replace(/[0-9]/g,function(e){return c.options.numerals[+e]})),t+c.options.prefix+n+e+c.options.suffix},this.easeOutExpo=function(e,t,n,i){return n*(1-Math.pow(2,-10*e/i))*1024/1023+t},this.options=i(i({},this.defaults),n),this.formattingFn=this.options.formattingFn||this.formatNumber,this.easingFn=this.options.easingFn||this.easeOutExpo,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(t),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,""===this.options.separator&&(this.options.useGrouping=!1),this.el="string"==typeof e?document.getElementById(e):e,this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined","undefined"!=typeof window&&this.options.enableScrollSpy&&(this.error?console.error(this.error,e):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push(function(){return c.handleScroll(c)}),window.onscroll=function(){window.onScrollFns.forEach(function(e){return e()})},this.handleScroll(this)))}t.prototype.handleScroll=function(e){var t,n,i;e&&window&&!e.once&&(t=window.innerHeight+window.scrollY,n=(i=e.el.getBoundingClientRect()).top+window.pageYOffset,(i=i.top+i.height+window.pageYOffset)<t&&i>window.scrollY&&e.paused?(e.paused=!1,setTimeout(function(){return e.start()},e.options.scrollSpyDelay),e.options.scrollSpyOnce&&(e.once=!0)):(window.scrollY>i||t<n)&&!e.paused&&e.reset())},t.prototype.determineDirectionAndSmartEasing=function(){var e=this.finalEndVal||this.endVal,t=(this.countDown=this.startVal>e,e-this.startVal);Math.abs(t)>this.options.smartEasingThreshold&&this.options.useEasing?(this.finalEndVal=e,t=this.countDown?1:-1,this.endVal=e+t*this.options.smartEasingAmount,this.duration=this.duration/2):(this.endVal=e,this.finalEndVal=null),null!==this.finalEndVal?this.useEasing=!1:this.useEasing=this.options.useEasing},t.prototype.start=function(e){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),e&&(this.options.onCompleteCallback=e),0<this.duration?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},t.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},t.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},t.prototype.update=function(e){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(e),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,null==this.finalEndVal&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},t.prototype.printValue=function(e){var t;this.el&&(e=this.formattingFn(e),null!=(t=this.options.plugin)&&t.render?this.options.plugin.render(this.el,e):"INPUT"===this.el.tagName?this.el.value=e:"text"===this.el.tagName||"tspan"===this.el.tagName?this.el.textContent=e:this.el.innerHTML=e)},t.prototype.ensureNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.prototype.validateValue=function(e){var t=Number(e);return this.ensureNumber(t)?t:(this.error="[CountUp] invalid start or end value: ".concat(e),null)},t.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},e.CountUp=t,Object.defineProperty(e,"__esModule",{value:!0})},"object"==(void 0===n?"undefined":i(n))&&void 0!==t?o(n):"function"==typeof define&&define.amd?define(["exports"],o):o(("undefined"!=typeof globalThis?globalThis:self).countUp={})},{}],6:[function(e,t,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o;o=function(){function e(){return"undefined"!=typeof window}function d(e){return(r=e)&&r.document&&9===r.document.nodeType?(r=(n=e).document,i=r.body,o=r.documentElement,{scrollHeight:function(){return Math.max(i.scrollHeight,o.scrollHeight,i.offsetHeight,o.offsetHeight,i.clientHeight,o.clientHeight)},height:function(){return n.innerHeight||o.clientHeight||i.clientHeight},scrollY:function(){return void 0!==n.pageYOffset?n.pageYOffset:(o||i.parentNode||i).scrollTop}}):(t=e,{scrollHeight:function(){return Math.max(t.scrollHeight,t.offsetHeight,t.clientHeight)},height:function(){return Math.max(t.offsetHeight,t.clientHeight)},scrollY:function(){return t.scrollTop}});var t,n,i,o,r}function t(e,i,o){var t,n=function(){var t=!1;try{var e={get passive(){t=!0}};window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){t=!1}return t}(),r=!1,a=d(e),s=a.scrollY(),c={};function l(){var e=Math.round(a.scrollY()),t=a.height(),n=a.scrollHeight();c.scrollY=e,c.lastScrollY=s,c.direction=s<e?"down":"up",c.distance=Math.abs(e-s),c.isOutOfBounds=e<0||n<e+t,c.top=e<=i.offset[c.direction],c.bottom=n<=e+t,c.toleranceExceeded=c.distance>i.tolerance[c.direction],o(c),s=e,r=!1}function u(){r||(r=!0,t=requestAnimationFrame(l))}var f=!!n&&{passive:!0,capture:!1};return e.addEventListener("scroll",u,f),l(),{destroy:function(){cancelAnimationFrame(t),e.removeEventListener("scroll",u,f)}}}function n(e){return e===Object(e)?e:{down:e,up:e}}function i(e,t){t=t||{},Object.assign(this,i.options,t),this.classes=Object.assign({},i.options.classes,t.classes),this.elem=e,this.tolerance=n(this.tolerance),this.offset=n(this.offset),this.initialised=!1,this.frozen=!1}return i.prototype={constructor:i,init:function(){return i.cutsTheMustard&&!this.initialised&&(this.addClass("initial"),this.initialised=!0,setTimeout(function(e){e.scrollTracker=t(e.scroller,{offset:e.offset,tolerance:e.tolerance},e.update.bind(e))},100,this)),this},destroy:function(){this.initialised=!1,Object.keys(this.classes).forEach(this.removeClass,this),this.scrollTracker.destroy()},unpin:function(){!this.hasClass("pinned")&&this.hasClass("unpinned")||(this.addClass("unpinned"),this.removeClass("pinned"),this.onUnpin&&this.onUnpin.call(this))},pin:function(){this.hasClass("unpinned")&&(this.addClass("pinned"),this.removeClass("unpinned"),this.onPin)&&this.onPin.call(this)},freeze:function(){this.frozen=!0,this.addClass("frozen")},unfreeze:function(){this.frozen=!1,this.removeClass("frozen")},top:function(){this.hasClass("top")||(this.addClass("top"),this.removeClass("notTop"),this.onTop&&this.onTop.call(this))},notTop:function(){this.hasClass("notTop")||(this.addClass("notTop"),this.removeClass("top"),this.onNotTop&&this.onNotTop.call(this))},bottom:function(){this.hasClass("bottom")||(this.addClass("bottom"),this.removeClass("notBottom"),this.onBottom&&this.onBottom.call(this))},notBottom:function(){this.hasClass("notBottom")||(this.addClass("notBottom"),this.removeClass("bottom"),this.onNotBottom&&this.onNotBottom.call(this))},shouldUnpin:function(e){return"down"===e.direction&&!e.top&&e.toleranceExceeded},shouldPin:function(e){return"up"===e.direction&&e.toleranceExceeded||e.top},addClass:function(e){this.elem.classList.add.apply(this.elem.classList,this.classes[e].split(" "))},removeClass:function(e){this.elem.classList.remove.apply(this.elem.classList,this.classes[e].split(" "))},hasClass:function(e){return this.classes[e].split(" ").every(function(e){return this.classList.contains(e)},this.elem)},update:function(e){e.isOutOfBounds||!0!==this.frozen&&(e.top?this.top():this.notTop(),e.bottom?this.bottom():this.notBottom(),this.shouldUnpin(e)?this.unpin():this.shouldPin(e)&&this.pin())}},i.options={tolerance:{up:0,down:0},offset:0,scroller:e()?window:null,classes:{frozen:"headroom--frozen",pinned:"headroom--pinned",unpinned:"headroom--unpinned",top:"headroom--top",notTop:"headroom--not-top",bottom:"headroom--bottom",notBottom:"headroom--not-bottom",initial:"headroom"}},i.cutsTheMustard=!!(e()&&function(){}.bind&&"classList"in document.documentElement&&Object.assign&&Object.keys&&requestAnimationFrame),i},"object"===(void 0===n?"undefined":i(n))&&void 0!==t?t.exports=o():"function"==typeof define&&define.amd?define(o):self.Headroom=o()},{}],7:[function(e,t,n){"use strict";var i,o,t=t.exports={};function r(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}try{i="function"==typeof setTimeout?setTimeout:r}catch(e){i=r}try{o="function"==typeof clearTimeout?clearTimeout:a}catch(e){o=a}function s(t){if(i===setTimeout)return setTimeout(t,0);if((i===r||!i)&&setTimeout)return(i=setTimeout)(t,0);try{return i(t,0)}catch(e){try{return i.call(null,t,0)}catch(e){return i.call(this,t,0)}}}var c,l=[],u=!1,f=-1;function d(){u&&c&&(u=!1,c.length?l=c.concat(l):f=-1,l.length)&&p()}function p(){if(!u){for(var e=s(d),t=(u=!0,l.length);t;){for(c=l,l=[];++f<t;)c&&c[f].run();f=-1,t=l.length}c=null,u=!1,!function(t){if(o===clearTimeout)return clearTimeout(t);if((o===a||!o)&&clearTimeout)return(o=clearTimeout)(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}t.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new h(e,t)),1!==l.length||u||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=m,t.addListener=m,t.once=m,t.off=m,t.removeListener=m,t.removeAllListeners=m,t.emit=m,t.prependListener=m,t.prependOnceListener=m,t.listeners=function(e){return[]},t.binding=function(e){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(e){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},{}],8:[function(e,c,l){!function(s){!function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t,n=(this.document||this.ownerDocument).querySelectorAll(e),i=this;do{for(t=n.length;0<=--t&&n.item(t)!==i;);}while(t<0&&(i=i.parentElement));return i}),"function"!=typeof window.CustomEvent&&(a.prototype=window.Event.prototype,window.CustomEvent=a);for(var e,n,r=0,i=["ms","moz","webkit","o"],o=0;o<i.length&&!window.requestAnimationFrame;++o)window.requestAnimationFrame=window[i[o]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[i[o]+"CancelAnimationFrame"]||window[i[o]+"CancelRequestAnimationFrame"];function a(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}window.requestAnimationFrame||(window.requestAnimationFrame=function(e,t){var n=(new Date).getTime(),i=Math.max(0,16-(n-r)),o=window.setTimeout(function(){e(n+i)},i);return r=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)}),e=void 0!==s?s:"undefined"!=typeof window?window:void 0,n=function(_){function w(){var n={};return Array.prototype.forEach.call(arguments,function(e){for(var t in e){if(!e.hasOwnProperty(t))return;n[t]=e[t]}}),n}function a(e){"#"===e.charAt(0)&&(e=e.substr(1));for(var t,n=String(e),i=n.length,o=-1,r="",a=n.charCodeAt(0);++o<i;){if(0===(t=n.charCodeAt(o)))throw new InvalidCharacterError("Invalid character: the input contains U+0000.");r+=1<=t&&t<=31||127==t||0===o&&48<=t&&t<=57||1===o&&48<=t&&t<=57&&45===a?"\\"+t.toString(16)+" ":128<=t||45===t||95===t||48<=t&&t<=57||65<=t&&t<=90||97<=t&&t<=122?n.charAt(o):"\\"+n.charAt(o)}return"#"+r}function k(){return Math.max(document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight,document.body.clientHeight,document.documentElement.clientHeight)}function E(e,t,n){0===e&&document.body.focus(),n||(e.focus(),document.activeElement!==e&&(e.setAttribute("tabindex","-1"),e.focus(),e.style.outline="none"),_.scrollTo(0,t))}function O(e,t,n,i){t.emitEvents&&"function"==typeof _.CustomEvent&&(t=new CustomEvent(e,{bubbles:!0,detail:{anchor:n,toggle:i}}),document.dispatchEvent(t))}var A={ignore:"[data-scroll-ignore]",header:null,topOnEmptyHash:!0,speed:500,speedAsDuration:!1,durationMax:null,durationMin:null,clip:!0,offset:0,easing:"easeInOutCubic",customEasing:null,updateURL:!0,popstate:!0,emitEvents:!0};return function(o,e){function t(e){if(!e.defaultPrevented&&!(0!==e.button||e.metaKey||e.ctrlKey||e.shiftKey)&&"closest"in e.target&&(r=e.target.closest(o))&&"a"===r.tagName.toLowerCase()&&!e.target.closest(g.ignore)&&r.hostname===_.location.hostname&&r.pathname===_.location.pathname&&/#/.test(r.href)){var t;try{n=a(decodeURIComponent(r.hash))}catch(e){n=a(r.hash)}if("#"===n){if(!g.topOnEmptyHash)return;t=document.documentElement}else t=document.querySelector(n);(t=t||"#top"!==n?t:document.documentElement)&&(e.preventDefault(),n=g,history.replaceState&&n.updateURL&&!history.state&&(i=(i=_.location.hash)||"",history.replaceState({smoothScroll:JSON.stringify(n),anchor:i||_.pageYOffset},document.title,i||_.location.href)),b.animateScroll(t,r))}var n,i}function n(e){var t;null===history.state||!history.state.smoothScroll||history.state.smoothScroll!==JSON.stringify(g)||"string"==typeof(t=history.state.anchor)&&t&&!(t=document.querySelector(a(history.state.anchor)))||b.animateScroll(t,null,{updateURL:!1})}var g,r,i,y,b={cancelScroll:function(e){cancelAnimationFrame(y),y=null,e||O("scrollCancel",g)}};b.animateScroll=function(r,a,e){b.cancelScroll();var s,c,l,u,f,d,p,h,t,m=w(g||A,e||{}),v="[object Number]"===Object.prototype.toString.call(r),e=v||!r.tagName?null:r;(v||e)&&(s=_.pageYOffset,m.header&&!i&&(i=document.querySelector(m.header)),t=(t=i)?parseInt(_.getComputedStyle(t).height,10)+t.offsetTop:0,u=v?r:function(e,t,n,i){var o=0;if(e.offsetParent)for(;o+=e.offsetTop,e=e.offsetParent;);return o=Math.max(o-t-n,0),o=i?Math.min(o,k()-_.innerHeight):o}(e,t,parseInt("function"==typeof m.offset?m.offset(r,a):m.offset,10),m.clip),f=u-s,d=k(),p=0,t=(e=m).speedAsDuration?e.speed:Math.abs(f/1e3*e.speed),h=e.durationMax&&t>e.durationMax?e.durationMax:e.durationMin&&t<e.durationMin?e.durationMin:parseInt(t,10),e=function e(t){var n,i,o;p+=t-(c=c||t),l=s+f*(i=l=1<(l=0===h?0:p/h)?1:l,"easeInQuad"===m.easing&&(n=i*i),"easeOutQuad"===m.easing&&(n=i*(2-i)),"easeInOutQuad"===m.easing&&(n=i<.5?2*i*i:(4-2*i)*i-1),"easeInCubic"===m.easing&&(n=i*i*i),"easeOutCubic"===m.easing&&(n=--i*i*i+1),"easeInOutCubic"===m.easing&&(n=i<.5?4*i*i*i:(i-1)*(2*i-2)*(2*i-2)+1),"easeInQuart"===m.easing&&(n=i*i*i*i),"easeOutQuart"===m.easing&&(n=1- --i*i*i*i),"easeInOutQuart"===m.easing&&(n=i<.5?8*i*i*i*i:1-8*--i*i*i*i),"easeInQuint"===m.easing&&(n=i*i*i*i*i),"easeOutQuint"===m.easing&&(n=1+--i*i*i*i*i),"easeInOutQuint"===m.easing&&(n=i<.5?16*i*i*i*i*i:1+16*--i*i*i*i*i),(n=m.customEasing?m.customEasing(i):n)||i),_.scrollTo(0,Math.floor(l)),n=l,i=u,o=_.pageYOffset,(!(n==i||o==i||(s<i&&_.innerHeight+o)>=d)||(b.cancelScroll(!0),E(r,i,v),O("scrollStop",m,r,a),y=c=null))&&(y=_.requestAnimationFrame(e),c=t)},0===_.pageYOffset&&_.scrollTo(0,0),t=r,v||history.pushState&&m.updateURL&&history.pushState({smoothScroll:JSON.stringify(m),anchor:t.id},document.title,t===document.documentElement?"#top":"#"+t.id),"matchMedia"in _&&_.matchMedia("(prefers-reduced-motion)").matches?E(r,Math.floor(u),!1):(O("scrollStart",m,r,a),b.cancelScroll(!0),_.requestAnimationFrame(e)))};if(b.destroy=function(){g&&(document.removeEventListener("click",t,!1),_.removeEventListener("popstate",n,!1),b.cancelScroll(),y=i=r=g=null)},"querySelector"in document&&"addEventListener"in _&&"requestAnimationFrame"in _&&"closest"in _.Element.prototype)return b.destroy(),g=w(A,e||{}),i=g.header?document.querySelector(g.header):null,document.addEventListener("click",t,!1),g.updateURL&&g.popstate&&_.addEventListener("popstate",n,!1),b;throw"Smooth Scroll: This browser does not support the required JavaScript methods and browser APIs."}},"function"==typeof define&&define.amd?define([],function(){return n(e)}):"object"==(void 0===l?"undefined":t(l))?c.exports=n(e):e.SmoothScroll=n(e)}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(I,e,V){!function(Le){!function(){"use strict";Object.defineProperty(V,"__esModule",{value:!0});var ee=I("@popperjs/core"),i="tippy-content",r="tippy-backdrop",n="tippy-arrow",o="tippy-svg-arrow",te={passive:!0,capture:!0},ne=function(){return document.body};function ie(e,t,n){var i;return Array.isArray(e)?null==(i=e[t])?Array.isArray(n)?n[t]:n:i:e}function a(e,t){e={}.toString.call(e);return 0===e.indexOf("[object")&&-1<e.indexOf(t+"]")}function oe(e,t){return"function"==typeof e?e.apply(void 0,t):e}function re(t,n){var i;return 0===n?t:function(e){clearTimeout(i),i=setTimeout(function(){t(e)},n)}}function g(e,t){var n=Object.assign({},e);return t.forEach(function(e){delete n[e]}),n}function ae(e){return[].concat(e)}function se(e,t){-1===e.indexOf(t)&&e.push(t)}function ce(e){return e.split("-")[0]}function le(e){return[].slice.call(e)}function ue(n){return Object.keys(n).reduce(function(e,t){return void 0!==n[t]&&(e[t]=n[t]),e},{})}function fe(){return document.createElement("div")}function s(t){return["Element","Fragment"].some(function(e){return a(t,e)})}function de(e){return a(e,"MouseEvent")}function u(e){return e&&e._tippy&&e._tippy.reference===e}function f(e){return s(e)?[e]:a(e,"NodeList")?le(e):Array.isArray(e)?e:le(document.querySelectorAll(e))}function pe(e,t){e.forEach(function(e){e&&(e.style.transitionDuration=t+"ms")})}function he(e,t){e.forEach(function(e){e&&e.setAttribute("data-state",t)})}function Oe(e){var t,e=ae(e)[0];return null!=e&&null!=(t=e.ownerDocument)&&t.body?e.ownerDocument:document}function me(t,e,n){var i=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(e){t[i](e,n)})}function Ae(e,t){for(var n,i=t;i;){if(e.contains(i))return!0;i=null==i.getRootNode||null==(n=i.getRootNode())?void 0:n.host}return!1}var ve={isTouch:!1},t=0;function d(){ve.isTouch||(ve.isTouch=!0,window.performance&&document.addEventListener("mousemove",p))}function p(){var e=performance.now();e-t<20&&(ve.isTouch=!1,document.removeEventListener("mousemove",p)),t=e}function v(){var e,t=document.activeElement;u(t)&&(e=t._tippy,t.blur)&&!e.state.isVisible&&t.blur()}var c,Te="undefined"!=typeof window&&"undefined"!=typeof document&&!!window.msCrypto;function ge(e){return[e+"() was called on a"+("destroy"===e?"n already-":" ")+"destroyed instance. This is a no-op but","indicates a potential memory leak."].join(" ")}function b(e){return e.replace(/[ \t]{2,}/g," ").replace(/^[ \t]*/gm,"").trim()}function _(e){return[b("\n  %ctippy.js\n\n  %c"+b(e)+"\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  "),"color: #00C584; font-size: 1.3em; font-weight: bold;","line-height: 1.5","color: #a6a095;"]}function ye(e,t){e&&!c.has(t)&&(c.add(t),(e=console).warn.apply(e,_(t)))}function be(e,t){e&&!c.has(t)&&(c.add(t),(e=console).error.apply(e,_(t)))}"production"!==Le.env.NODE_ENV&&(c=new Set);var w={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},_e=Object.assign({appendTo:ne,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},w,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),k=Object.keys(_e);function Ce(i){var e=(i.plugins||[]).reduce(function(e,t){var n=t.name,t=t.defaultValue;return n&&(e[n]=void 0!==i[n]?i[n]:null!=(n=_e[n])?n:t),e},{});return Object.assign({},i,e)}function xe(e,t){var o,e=Object.assign({},t,{content:oe(t.content,[e])},t.ignoreAttributes?{}:(o=e,((e=t.plugins)?Object.keys(Ce(Object.assign({},_e,{plugins:e}))):k).reduce(function(t,n){var i=(o.getAttribute("data-tippy-"+n)||"").trim();if(i)if("content"===n)t[n]=i;else try{t[n]=JSON.parse(i)}catch(e){t[n]=i}return t},{})));return e.aria=Object.assign({},_e.aria,e.aria),e.aria={expanded:"auto"===e.aria.expanded?t.interactive:e.aria.expanded,content:"auto"===e.aria.content?t.interactive?null:"describedby":e.aria.content},e}function E(e,n){void 0===e&&(e={}),void 0===n&&(n=[]),Object.keys(e).forEach(function(t){var e=g(_e,Object.keys(w));ye(!{}.hasOwnProperty.call(e,t)&&0===n.filter(function(e){return e.name===t}).length,["`"+t+"`","is not a valid prop. You may have spelled it incorrectly, or if it's","a plugin, forgot to pass it in an array as props.plugins.","\n\n","All props: https://atomiks.github.io/tippyjs/v6/all-props/\n","Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "))})}function O(){return"innerHTML"}function l(e,t){e[O()]=t}function A(e){var t=fe();return!0===e?t.className=n:(t.className=o,s(e)?t.appendChild(e):l(t,e)),t}function T(e,t){s(t.content)?(l(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?l(e,t.content):e.textContent=t.content)}function we(e){var e=e.firstElementChild,t=le(e.children);return{box:e,content:t.find(function(e){return e.classList.contains(i)}),arrow:t.find(function(e){return e.classList.contains(n)||e.classList.contains(o)}),backdrop:t.find(function(e){return e.classList.contains(r)})}}function e(r){var a=fe(),e=fe(),t=(e.className="tippy-box",e.setAttribute("data-state","hidden"),e.setAttribute("tabindex","-1"),fe());function n(e,t){var n=we(a),i=n.box,o=n.content,n=n.arrow;t.theme?i.setAttribute("data-theme",t.theme):i.removeAttribute("data-theme"),"string"==typeof t.animation?i.setAttribute("data-animation",t.animation):i.removeAttribute("data-animation"),t.inertia?i.setAttribute("data-inertia",""):i.removeAttribute("data-inertia"),i.style.maxWidth="number"==typeof t.maxWidth?t.maxWidth+"px":t.maxWidth,t.role?i.setAttribute("role",t.role):i.removeAttribute("role"),e.content===t.content&&e.allowHTML===t.allowHTML||T(o,r.props),t.arrow?n?e.arrow!==t.arrow&&(i.removeChild(n),i.appendChild(A(t.arrow))):i.appendChild(A(t.arrow)):n&&i.removeChild(n)}return t.className=i,t.setAttribute("data-state","hidden"),T(t,r.props),a.appendChild(e),e.appendChild(t),n(r.props,r.props),{popper:a,onUpdate:n}}e.$$tippy=!0;var Se=1,ke=[],Ee=[];function C(a,e){var i,t,n,o,r,s,c,l,u,f,d,p,h=xe(a,Object.assign({},_e,Ce(ue(e)))),m=!1,v=!1,g=!1,y=!1,b=[],_=re(X,h.interactiveDebounce),e=Se++,w=(l=h.plugins).filter(function(e,t){return l.indexOf(e)===t}),k={id:e,reference:a,popper:fe(),popperInstance:null,props:h,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:w,clearDelayTimeouts:function(){clearTimeout(i),clearTimeout(t),cancelAnimationFrame(n)},setProps:function(e){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("setProps"));var t,n;k.state.isDestroyed||(x("onBeforeUpdate",[k,e]),U(),t=k.props,n=xe(a,Object.assign({},t,ue(e),{ignoreAttributes:!0})),k.props=n,W(),t.interactiveDebounce!==n.interactiveDebounce&&(L(),_=re(X,n.interactiveDebounce)),t.triggerTarget&&!n.triggerTarget?ae(t.triggerTarget).forEach(function(e){e.removeAttribute("aria-expanded")}):n.triggerTarget&&a.removeAttribute("aria-expanded"),S(),C(),f&&f(t,n),k.popperInstance&&(J(),M().forEach(function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)})),x("onAfterUpdate",[k,e]))},setContent:function(e){k.setProps({content:e})},show:function(){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("show"));var e=k.state.isVisible,t=k.state.isDestroyed,n=!k.state.isEnabled,i=ve.isTouch&&!k.props.touch,o=ie(k.props.duration,0,_e.duration);e||t||n||i||O().hasAttribute("disabled")||(x("onShow",[k],!1),!1!==k.props.onShow(k)&&(k.state.isVisible=!0,E()&&(u.style.visibility="visible"),C(),R(),k.state.isMounted||(u.style.transition="none"),E()&&(e=T(),t=e.box,n=e.content,pe([t,n],0)),s=function(){var e,t;k.state.isVisible&&!y&&(y=!0,u.offsetHeight,u.style.transition=k.props.moveTransition,E()&&k.props.animation&&(pe([e=(t=T()).box,t=t.content],o),he([e,t],"visible")),H(),S(),se(Ee,k),null!=(e=k.popperInstance)&&e.forceUpdate(),x("onMount",[k]),k.props.animation)&&E()&&B(o,function(){k.state.isShown=!0,x("onShown",[k])})},i=k.props.appendTo,e=O(),(t=k.props.interactive&&i===ne||"parent"===i?e.parentNode:oe(i,[e])).contains(u)||t.appendChild(u),k.state.isMounted=!0,J(),"production"!==Le.env.NODE_ENV)&&ye(k.props.interactive&&i===_e.appendTo&&e.nextElementSibling!==u,["Interactive tippy element may not be accessible via keyboard","navigation because it is not directly after the reference element","in the DOM source order.","\n\n","Using a wrapper <div> or <span> tag around the reference element","solves this by creating a new parentNode context.","\n\n","Specifying `appendTo: document.body` silences this warning, but it","assumes you are using a focus management solution to handle","keyboard navigation.","\n\n","See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" ")))},hide:function(){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("hide"));var e,t=!k.state.isVisible,n=k.state.isDestroyed,i=!k.state.isEnabled,o=ie(k.props.duration,1,_e.duration);t||n||i||(x("onHide",[k],!1),!1!==k.props.onHide(k)&&(k.state.isVisible=!1,k.state.isShown=!1,m=y=!1,E()&&(u.style.visibility="hidden"),L(),N(),C(!0),E()&&(t=T(),n=t.box,i=t.content,k.props.animation)&&(pe([n,i],o),he([n,i],"hidden")),H(),S(),k.props.animation?E()&&(e=k.unmount,B(o,function(){!k.state.isVisible&&u.parentNode&&u.parentNode.contains(u)&&e()})):k.unmount()))},hideWithInteractivity:function(e){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("hideWithInteractivity"));A().addEventListener("mousemove",_),se(ke,_),_(e)},enable:function(){k.state.isEnabled=!0},disable:function(){k.hide(),k.state.isEnabled=!1},unmount:function(){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("unmount"));k.state.isVisible&&k.hide();k.state.isMounted&&(G(),M().forEach(function(e){e._tippy.unmount()}),u.parentNode&&u.parentNode.removeChild(u),Ee=Ee.filter(function(e){return e!==k}),k.state.isMounted=!1,x("onHidden",[k]))},destroy:function(){"production"!==Le.env.NODE_ENV&&ye(k.state.isDestroyed,ge("destroy"));k.state.isDestroyed||(k.clearDelayTimeouts(),k.unmount(),U(),delete a._tippy,k.state.isDestroyed=!0,x("onDestroy",[k]))}};return h.render?(e=h.render(k),u=e.popper,f=e.onUpdate,d=(u.setAttribute("data-tippy-root",""),u.id="tippy-"+k.id,k.popper=u,a._tippy=k,u._tippy=k,w.map(function(e){return e.fn(k)})),p=a.hasAttribute("aria-expanded"),W(),S(),C(),x("onCreate",[k]),h.showOnCreate&&Z(),u.addEventListener("mouseenter",function(){k.props.interactive&&k.state.isVisible&&k.clearDelayTimeouts()}),u.addEventListener("mouseleave",function(){k.props.interactive&&0<=k.props.trigger.indexOf("mouseenter")&&A().addEventListener("mousemove",_)})):"production"!==Le.env.NODE_ENV&&be(!0,"render() function has not been supplied."),k;function I(){var e=k.props.touch;return Array.isArray(e)?e:[e,0]}function V(){return"hold"===I()[0]}function E(){var e;return null!=(e=k.props.render)&&e.$$tippy}function O(){return c||a}function A(){var e=O().parentNode;return e?Oe(e):document}function T(){return we(u)}function F(e){return k.state.isMounted&&!k.state.isVisible||ve.isTouch||o&&"focus"===o.type?0:ie(k.props.delay,e?0:1,_e.delay)}function C(e){void 0===e&&(e=!1),u.style.pointerEvents=k.props.interactive&&!e?"":"none",u.style.zIndex=""+k.props.zIndex}function x(t,n,e){void 0===e&&(e=!0),d.forEach(function(e){e[t]&&e[t].apply(e,n)}),e&&(e=k.props)[t].apply(e,n)}function H(){var n,i,e=k.props.aria;e.content&&(n="aria-"+e.content,i=u.id,ae(k.props.triggerTarget||a).forEach(function(e){var t=e.getAttribute(n);k.state.isVisible?e.setAttribute(n,t?t+" "+i:i):(t=t&&t.replace(i,"").trim())?e.setAttribute(n,t):e.removeAttribute(n)}))}function S(){!p&&k.props.aria.expanded&&ae(k.props.triggerTarget||a).forEach(function(e){k.props.interactive?e.setAttribute("aria-expanded",k.state.isVisible&&e===O()?"true":"false"):e.removeAttribute("aria-expanded")})}function L(){A().removeEventListener("mousemove",_),ke=ke.filter(function(e){return e!==_})}function j(e){if(!ve.isTouch||!g&&"mousedown"!==e.type){var t=e.composedPath&&e.composedPath()[0]||e.target;if(!k.props.interactive||!Ae(u,t)){if(ae(k.props.triggerTarget||a).some(function(e){return Ae(e,t)})){if(ve.isTouch)return;if(k.state.isVisible&&0<=k.props.trigger.indexOf("click"))return}else x("onClickOutside",[k,e]);!0===k.props.hideOnClick&&(k.clearDelayTimeouts(),k.hide(),v=!0,setTimeout(function(){v=!1}),k.state.isMounted||N())}}}function q(){g=!0}function z(){g=!1}function R(){var e=A();e.addEventListener("mousedown",j,!0),e.addEventListener("touchend",j,te),e.addEventListener("touchstart",z,te),e.addEventListener("touchmove",q,te)}function N(){var e=A();e.removeEventListener("mousedown",j,!0),e.removeEventListener("touchend",j,te),e.removeEventListener("touchstart",z,te),e.removeEventListener("touchmove",q,te)}function B(e,t){var n=T().box;function i(e){e.target===n&&(me(n,"remove",i),t())}if(0===e)return t();me(n,"remove",r),me(n,"add",i),r=i}function D(t,n,i){void 0===i&&(i=!1),ae(k.props.triggerTarget||a).forEach(function(e){e.addEventListener(t,n,i),b.push({node:e,eventType:t,handler:n,options:i})})}function W(){V()&&(D("touchstart",Y,{passive:!0}),D("touchend",Q,{passive:!0})),k.props.trigger.split(/\s+/).filter(Boolean).forEach(function(e){if("manual"!==e)switch(D(e,Y),e){case"mouseenter":D("mouseleave",Q);break;case"focus":D(Te?"focusout":"blur",K);break;case"focusin":D("focusout",K)}})}function U(){b.forEach(function(e){var t=e.node,n=e.eventType,i=e.handler,e=e.options;t.removeEventListener(n,i,e)}),b=[]}function Y(t){var e,n=!1;!k.state.isEnabled||$(t)||v||(e="focus"===(null==o?void 0:o.type),c=(o=t).currentTarget,S(),!k.state.isVisible&&de(t)&&ke.forEach(function(e){return e(t)}),"click"===t.type&&(k.props.trigger.indexOf("mouseenter")<0||m)&&!1!==k.props.hideOnClick&&k.state.isVisible?n=!0:Z(t),"click"===t.type&&(m=!n),n&&!e&&P(t))}function X(e){var s,c,t=e.target,t=O().contains(t)||u.contains(t);"mousemove"===e.type&&t||(t=M().concat(u).map(function(e){var t=null==(t=e._tippy.popperInstance)?void 0:t.state;return t?{popperRect:e.getBoundingClientRect(),popperState:t,props:h}:null}).filter(Boolean),s=e.clientX,c=e.clientY,t.every(function(e){var t,n,i,o=e.popperRect,r=e.popperState,e=e.props.interactiveBorder,a=ce(r.placement),r=r.modifiersData.offset;return!r||(t="bottom"===a?r.top.y:0,n="top"===a?r.bottom.y:0,i="right"===a?r.left.x:0,a="left"===a?r.right.x:0,r=o.top-c+t>e,t=c-o.bottom-n>e,n=o.left-s+i>e,i=s-o.right-a>e,r)||t||n||i})&&(L(),P(e)))}function Q(e){$(e)||0<=k.props.trigger.indexOf("click")&&m||(k.props.interactive?k.hideWithInteractivity(e):P(e))}function K(e){k.props.trigger.indexOf("focusin")<0&&e.target!==O()||k.props.interactive&&e.relatedTarget&&u.contains(e.relatedTarget)||P(e)}function $(e){return!!ve.isTouch&&V()!==0<=e.type.indexOf("touch")}function J(){G();var e=k.props,t=e.popperOptions,n=e.placement,i=e.offset,o=e.getReferenceClientRect,e=e.moveTransition,r=E()?we(u).arrow:null,o=o?{getBoundingClientRect:o,contextElement:o.contextElement||O()}:a,i=[{name:"offset",options:{offset:i}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!e}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t,n=e.state;E()&&(t=T().box,["placement","reference-hidden","escaped"].forEach(function(e){"placement"===e?t.setAttribute("data-placement",n.placement):n.attributes.popper["data-popper-"+e]?t.setAttribute("data-"+e,""):t.removeAttribute("data-"+e)}),n.attributes.popper={})}}];E()&&r&&i.push({name:"arrow",options:{element:r,padding:3}}),i.push.apply(i,(null==t?void 0:t.modifiers)||[]),k.popperInstance=ee.createPopper(o,u,Object.assign({},t,{placement:n,onFirstUpdate:s,modifiers:i}))}function G(){k.popperInstance&&(k.popperInstance.destroy(),k.popperInstance=null)}function M(){return le(u.querySelectorAll("[data-tippy-root]"))}function Z(e){k.clearDelayTimeouts(),e&&x("onTrigger",[k,e]),R();var e=F(!0),t=I(),n=t[0],t=t[1];(e=ve.isTouch&&"hold"===n&&t?t:e)?i=setTimeout(function(){k.show()},e):k.show()}function P(e){k.clearDelayTimeouts(),x("onUntrigger",[k,e]),k.state.isVisible?0<=k.props.trigger.indexOf("mouseenter")&&0<=k.props.trigger.indexOf("click")&&0<=["mouseleave","mousemove"].indexOf(e.type)&&m||((e=F(!1))?t=setTimeout(function(){k.state.isVisible&&k.hide()},e):n=requestAnimationFrame(function(){k.hide()})):N()}}function y(e,t){var n,i,o=_e.plugins.concat((t=void 0===t?{}:t).plugins||[]),r=("production"!==Le.env.NODE_ENV&&(a=!(n=e),i="[object Object]"===Object.prototype.toString.call(n)&&!n.addEventListener,be(a,["tippy() was passed","`"+String(n)+"`","as its targets (first) argument. Valid types are: String, Element,","Element[], or NodeList."].join(" ")),be(i,["tippy() was passed a plain object which is not supported as an argument","for virtual positioning. Use props.getReferenceClientRect instead."].join(" ")),E(t,o)),document.addEventListener("touchstart",d,te),window.addEventListener("blur",v),Object.assign({},t,{plugins:o})),a=f(e),t=("production"!==Le.env.NODE_ENV&&(n=s(r.content),i=1<a.length,ye(n&&i,["tippy() was passed an Element as the `content` prop, but more than","one tippy instance was created by this invocation. This means the","content element will only be appended to the last tippy instance.","\n\n","Instead, pass the .innerHTML of the element, or use a function that","returns a cloned version of the element instead.","\n\n","1) content: element.innerHTML\n","2) content: () => element.cloneNode(true)"].join(" "))),a.reduce(function(e,t){t=t&&C(t,r);return t&&e.push(t),e},[]));return s(e)?t[0]:t}y.defaultProps=_e,y.setDefaultProps=function(t){"production"!==Le.env.NODE_ENV&&E(t,[]),Object.keys(t).forEach(function(e){_e[e]=t[e]})},y.currentInput=ve;var x=Object.assign({},ee.applyStyles,{effect:function(e){var e=e.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow)}}),S={mouseover:"mouseenter",focusin:"focus",click:"click"};var L={name:"animateFill",defaultValue:!1,fn:function(e){var n,i,o,t;return null!=(t=e.props.render)&&t.$$tippy?(t=we(e.popper),n=t.box,i=t.content,o=e.props.animateFill?((t=fe()).className=r,he([t],"hidden"),t):null,{onCreate:function(){o&&(n.insertBefore(o,n.firstElementChild),n.setAttribute("data-animatefill",""),n.style.overflow="hidden",e.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){var e,t;o&&(e=n.style.transitionDuration,t=Number(e.replace("ms","")),i.style.transitionDelay=Math.round(t/10)+"ms",o.style.transitionDuration=e,he([o],"visible"))},onShow:function(){o&&(o.style.transitionDuration="0ms")},onHide:function(){o&&he([o],"hidden")}}):("production"!==Le.env.NODE_ENV&&be(e.props.animateFill,"The `animateFill` plugin requires the default render function."),{})}};var h={clientX:0,clientY:0},m=[];function j(e){var t=e.clientX,e=e.clientY;h={clientX:t,clientY:e}}var N={name:"followCursor",defaultValue:!1,fn:function(n){var u=n.reference,t=Oe(n.props.triggerTarget||u),i=!1,o=!1,e=!0,r=n.props;function a(){return"initial"===n.props.followCursor&&n.state.isVisible}function s(){t.addEventListener("mousemove",f)}function c(){t.removeEventListener("mousemove",f)}function l(){i=!0,n.setProps({getReferenceClientRect:null}),i=!1}function f(e){var t=!e.target||u.contains(e.target),r=n.props.followCursor,a=e.clientX,s=e.clientY,e=u.getBoundingClientRect(),c=a-e.left,l=s-e.top;!t&&n.props.interactive||n.setProps({getReferenceClientRect:function(){var e=u.getBoundingClientRect(),t=a,n=s,i=("initial"===r&&(t=e.left+c,n=e.top+l),"horizontal"===r?e.top:n),o="vertical"===r?e.right:t,n="horizontal"===r?e.bottom:n,e="vertical"===r?e.left:t;return{width:o-e,height:n-i,top:i,right:o,bottom:n,left:e}}})}function d(){n.props.followCursor&&(m.push({instance:n,doc:t}),t.addEventListener("mousemove",j))}function p(){0===(m=m.filter(function(e){return e.instance!==n})).filter(function(e){return e.doc===t}).length&&t.removeEventListener("mousemove",j)}return{onCreate:d,onDestroy:p,onBeforeUpdate:function(){r=n.props},onAfterUpdate:function(e,t){t=t.followCursor;i||void 0!==t&&r.followCursor!==t&&(p(),t?(d(),!n.state.isMounted||o||a()||s()):(c(),l()))},onMount:function(){n.props.followCursor&&!o&&(e&&(f(h),e=!1),a()||s())},onTrigger:function(e,t){de(t)&&(h={clientX:t.clientX,clientY:t.clientY}),o="focus"===t.type},onHidden:function(){n.props.followCursor&&(l(),c(),e=!0)}}}};var D={name:"inlinePositioning",defaultValue:!1,fn:function(o){var t,p=o.reference;var h=-1,n=!1,i=[],r={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(e){var d=e.state;o.props.inlinePositioning&&(-1!==i.indexOf(d.placement)&&(i=[]),t!==d.placement&&-1===i.indexOf(d.placement)&&(i.push(d.placement),o.setProps({getReferenceClientRect:function(){var t=ce(d.placement),e=p.getBoundingClientRect(),n=le(p.getClientRects()),i=h;if(n.length<2||null===t)return e;if(2===n.length&&0<=i&&n[0].left>n[1].right)return n[i]||e;switch(t){case"top":case"bottom":var o=n[0],r=n[n.length-1],a="top"===t,s=o.top,c=r.bottom,l=(a?o:r).left,a=(a?o:r).right;return{top:s,bottom:c,left:l,right:a,width:a-l,height:c-s};case"left":case"right":var u=Math.min.apply(Math,n.map(function(e){return e.left})),f=Math.max.apply(Math,n.map(function(e){return e.right})),o=n.filter(function(e){return"left"===t?e.left===u:e.right===f}),r=o[0].top,a=o[o.length-1].bottom;return{top:r,bottom:a,left:u,right:f,width:f-u,height:a-r};default:return e}}})),t=d.placement)}};function e(){var e,t;n||(e=o.props,t=r,e={popperOptions:Object.assign({},e.popperOptions,{modifiers:[].concat(((null==(e=e.popperOptions)?void 0:e.modifiers)||[]).filter(function(e){return e.name!==t.name}),[t])})},n=!0,o.setProps(e),n=!1)}return{onCreate:e,onAfterUpdate:e,onTrigger:function(e,t){var n,i;de(t)&&(n=(i=le(o.reference.getClientRects())).find(function(e){return e.left-2<=t.clientX&&e.right+2>=t.clientX&&e.top-2<=t.clientY&&e.bottom+2>=t.clientY}),i=i.indexOf(n),h=-1<i?i:h)},onHidden:function(){h=-1}}}};var M={name:"sticky",defaultValue:!1,fn:function(n){var i=n.reference,o=n.popper;function r(e){return!0===n.props.sticky||n.props.sticky===e}var a=null,s=null;function c(){var e=r("reference")?(n.popperInstance?n.popperInstance.state.elements.reference:i).getBoundingClientRect():null,t=r("popper")?o.getBoundingClientRect():null;(e&&P(a,e)||t&&P(s,t))&&n.popperInstance&&n.popperInstance.update(),a=e,s=t,n.state.isMounted&&requestAnimationFrame(c)}return{onMount:function(){n.props.sticky&&c()}}}};function P(e,t){return!e||!t||e.top!==t.top||e.right!==t.right||e.bottom!==t.bottom||e.left!==t.left}y.setDefaultProps({render:e}),V.animateFill=L,V.createSingleton=function(e,t){void 0===t&&(t={}),"production"!==Le.env.NODE_ENV&&be(!Array.isArray(e),["The first argument passed to createSingleton() must be an array of","tippy instances. The passed value was",String(e)].join(" "));var o,r=e,i=[],a=[],s=t.overrides,n=[],c=!1;function l(){a=r.map(function(e){return ae(e.props.triggerTarget||e.reference)}).reduce(function(e,t){return e.concat(t)},[])}function u(){i=r.map(function(e){return e.reference})}function f(t){r.forEach(function(e){t?e.enable():e.disable()})}function d(i){return r.map(function(t){var n=t.setProps;return t.setProps=function(e){n(e),t.reference===o&&i.setProps(e)},function(){t.setProps=n}})}function p(e,t){var n=a.indexOf(t);t!==o&&(o=t,t=(s||[]).concat("content").reduce(function(e,t){return e[t]=r[n].props[t],e},{}),e.setProps(Object.assign({},t,{getReferenceClientRect:"function"==typeof t.getReferenceClientRect?t.getReferenceClientRect:function(){var e;return null==(e=i[n])?void 0:e.getBoundingClientRect()}})))}f(!1),u(),l();var e={fn:function(){return{onDestroy:function(){f(!0)},onHidden:function(){o=null},onClickOutside:function(e){e.props.showOnCreate&&!c&&(c=!0,o=null)},onShow:function(e){e.props.showOnCreate&&!c&&(c=!0,p(e,i[0]))},onTrigger:function(e,t){p(e,t.currentTarget)}}}},h=y(fe(),Object.assign({},g(t,["overrides"]),{plugins:[e].concat(t.plugins||[]),triggerTarget:a,popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat((null==(e=t.popperOptions)?void 0:e.modifiers)||[],[x])})})),m=h.show,v=(h.show=function(e){var t;return m(),o||null!=e?o&&null==e?void 0:"number"==typeof e?i[e]&&p(h,i[e]):0<=r.indexOf(e)?(t=e.reference,p(h,t)):0<=i.indexOf(e)?p(h,e):void 0:p(h,i[0])},h.showNext=function(){var e=i[0];if(!o)return h.show(0);var t=i.indexOf(o);h.show(i[t+1]||e)},h.showPrevious=function(){var e=i[i.length-1];if(!o)return h.show(e);var t=i.indexOf(o),t=i[t-1]||e;h.show(t)},h.setProps);return h.setProps=function(e){s=e.overrides||s,v(e)},h.setInstances=function(e){f(!0),n.forEach(function(e){return e()}),r=e,f(!1),u(),l(),n=d(h),h.setProps({triggerTarget:a})},n=d(h),h},V.default=y,V.delegate=function(e,i){"production"!==Le.env.NODE_ENV&&be(!(i&&i.target),["You must specity a `target` prop indicating a CSS selector string matching","the target elements that should receive a tippy."].join(" "));var o=[],r=[],a=!1,s=i.target,t=g(i,["target"]),n=Object.assign({},t,{trigger:"manual",touch:!1}),c=Object.assign({touch:_e.touch},t,{showOnCreate:!0});function l(e){var t,n;e.target&&!a&&(t=e.target.closest(s))&&(n=t.getAttribute("data-tippy-trigger")||i.trigger||_e.trigger,t._tippy||"touchstart"===e.type&&"boolean"==typeof c.touch||"touchstart"!==e.type&&n.indexOf(S[e.type])<0||(n=y(t,c))&&(r=r.concat(n)))}function u(e,t,n,i){e.addEventListener(t,n,i=void 0===i?!1:i),o.push({node:e,eventType:t,handler:n,options:i})}return ae(t=y(e,n)).forEach(function(e){var t=e.destroy,n=e.enable,i=e.disable;e.destroy=function(e){(e=void 0===e?!0:e)&&r.forEach(function(e){e.destroy()}),r=[],o.forEach(function(e){var t=e.node,n=e.eventType,i=e.handler,e=e.options;t.removeEventListener(n,i,e)}),o=[],t()},e.enable=function(){n(),r.forEach(function(e){return e.enable()}),a=!1},e.disable=function(){i(),r.forEach(function(e){return e.disable()}),a=!0},u(e=(e=e).reference,"touchstart",l,te),u(e,"mouseover",l),u(e,"focusin",l),u(e,"click",l)}),t},V.followCursor=N,V.hideAll=function(e){var e=void 0===e?{}:e,n=e.exclude,i=e.duration;Ee.forEach(function(e){var t=!1;(t=n?u(n)?e.reference===n:e.popper===n.popper:t)||(t=e.props.duration,e.setProps({duration:i}),e.hide(),e.state.isDestroyed)||e.setProps({duration:t})})},V.inlinePositioning=D,V.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',V.sticky=M}.call(this)}.call(this,I("_process"))},{"@popperjs/core":1,_process:7}],10:[function(e,t,n){"use strict";var r=e("countup.js");function i(e){var t=e.dataset.to?+e.dataset.to:null,n=e.dataset.countup?JSON.parse(e.dataset.countup):{};new r.CountUp(e,t,n).start()}e("aos").init({startEvent:"load",duration:750,delay:50,once:!0}),document.querySelectorAll("[data-countup]").forEach(function(e){"countup:in"!==e.getAttribute("data-aos-id")&&i(e)}),document.addEventListener("aos:in:countup:in",function(e){(e.detail instanceof Element?[e.detail]:document.querySelectorAll('.aos-animate[data-aos-id="countup:in"]')).forEach(function(e){i(e)})}),document.querySelectorAll('[data-as-toggle="price"]').forEach(function(e){e.addEventListener("change",function(e){var e=e.target,o=e.checked,e=e.dataset.asTarget;document.querySelectorAll(e).forEach(function(e){var t=e.dataset.asAnnual,n=e.dataset.asMonthly,i=e.dataset.options?JSON.parse(e.dataset.options):{};i.startVal=o?t:n,i.duration=i.duration||1,(o?new r.CountUp(e,n,i):new r.CountUp(e,t,i)).start()})})}),document.querySelectorAll(".progress-bar").forEach(function(e){e.getAttribute("data-aos-id")}),document.addEventListener("aos:in:progress:in",function(e){(e.detail instanceof Element?[e.detail]:document.querySelectorAll('.aos-animate[data-aos-id="progress:in"]')).forEach(function(e){e.style.width=e.getAttribute(["aria-valuenow"])+"%",e.style.transitionDelay=".4s",e.style.transitionDuration=".8s"})})},{aos:2,"countup.js":5}],11:[function(e,t,n){"use strict";var i=document.querySelector(".toTop");i&&window.addEventListener("scroll",function(){var e=window.pageYOffset,t=document.documentElement.clientHeight;t<e&&i.classList.add("show"),e<t&&i.classList.remove("show")})},{}],12:[function(e,t,n){"use strict";window.bootstrap=e("bootstrap/dist/js/bootstrap.js");[].slice.call(document.querySelectorAll(".toast")).map(function(e){return new bootstrap.Toast(e)});document.querySelectorAll(".modal").forEach(function(e){e.addEventListener("shown.bs.modal",function(e){e.preventDefault(),document.querySelector("[autofocus]").focus()})})},{"bootstrap/dist/js/bootstrap.js":4}],13:[function(e,t,n){"use strict";function i(){return a||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")}function o(e){"auto"===e&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.setAttribute("data-bs-theme","dark"):document.documentElement.setAttribute("data-bs-theme",e)}function r(e){var t=document.querySelector(".theme-icon-active .material-icons"),n=(e=document.querySelector('[data-bs-theme-value="'.concat(e,'"]'))).querySelector(".theme-icon .material-icons").getAttribute("href");document.querySelectorAll("[data-bs-theme-value]").forEach(function(e){e.classList.remove("active")}),e.classList.add("active"),t.setAttribute("href",n)}var a=localStorage.getItem("theme");o(i()),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",function(){"light"===a&&"dark"===a||o(i())}),window.addEventListener("DOMContentLoaded",function(){r(i()),document.querySelectorAll("[data-bs-theme-value]").forEach(function(t){t.addEventListener("click",function(){var e=t.getAttribute("data-bs-theme-value");localStorage.setItem("theme",e),o(e),r(e)})})})},{}],14:[function(e,t,n){"use strict";var i;i=document.querySelectorAll(".needs-validation"),Array.prototype.slice.call(i).forEach(function(t){t.addEventListener("submit",function(e){t.checkValidity()||(e.preventDefault(),e.stopPropagation()),t.classList.add("was-validated")},!1)})},{}],15:[function(e,t,n){"use strict";var i=(e=e("headroom.js"))&&e.__esModule?e:{default:e};document.querySelectorAll(".navbar-sticky").forEach(function(e){new i.default(e).init()})},{"headroom.js":6}],16:[function(e,t,n){"use strict";new((e=e("smooth-scroll"))&&e.__esModule?e:{default:e}).default('a[href*="#"]',{speed:700,speedAsDuration:!0,updateURL:!1})},{"smooth-scroll":8}],17:[function(e,t,n){"use strict";(0,((e=e("autosize"))&&e.__esModule?e:{default:e}).default)(document.querySelectorAll("textarea"))},{autosize:3}],18:[function(e,t,n){"use strict";e("@popperjs/core"),(0,((e=e("tippy.js"))&&e.__esModule?e:{default:e}).default)("[data-tippy-content]",{allowHTML:!0,animation:"shift-toward"})},{"@popperjs/core":1,"tippy.js":9}],19:[function(e,t,n){"use strict";e("./custom/bootstrap"),e("./custom/dark-mode"),e("./custom/tippy"),e("./custom/header"),e("./custom/aos"),e("./custom/form-validation"),e("./custom/textarea"),e("./custom/smooth-scroll"),e("./custom/back-to-top")},{"./custom/aos":10,"./custom/back-to-top":11,"./custom/bootstrap":12,"./custom/dark-mode":13,"./custom/form-validation":14,"./custom/header":15,"./custom/smooth-scroll":16,"./custom/textarea":17,"./custom/tippy":18}]},{},[19]);
//# sourceMappingURL=theme.bundle.min.js.map
