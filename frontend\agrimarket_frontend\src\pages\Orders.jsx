import React, { useState, useEffect } from 'react';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';
import '../App.css';

/**
 * Orders component for buyers to view and manage their orders on AgriMarket.
 * Allows buyers to select logistics partners for pending orders and submit reviews after delivery.
 */

const Orders = () => {
  const { user } = useAuth();
  // State for order data and UI
  const [orders, setOrders] = useState([]); // List of buyer's orders
  const [loading, setLoading] = useState(true); // Loading state for API calls
  const [error, setError] = useState(null); // Error state for API failures
  // State for logistics selection modal
  const [selectedOrder, setSelectedOrder] = useState(null); // Currently selected order for logistics
  const [logisticsPartners, setLogisticsPartners] = useState([]); // Available logistics partners
  const [selectedPartner, setSelectedPartner] = useState(null); // Chosen logistics partner
  const [modalOpen, setModalOpen] = useState(false); // Controls logistics modal visibility
  // State for review submission modal
  const [reviews, setReviews] = useState([]); // Reviews for a logistics partner
  const [reviewModalOpen, setReviewModalOpen] = useState(false); // Controls review modal visibility
  const [rating, setRating] = useState(3); // Rating value for review (1-5)
  const [reviewText, setReviewText] = useState(''); // Optional review comment

  useEffect(() => {
    const fetchOrders = async () => {
      if (!user) return;
      try {
        setLoading(true);
        const response = await axiosInstance.get('/api/orders/');
        setOrders(response.data);
      } catch (err) {
        setError('Failed to load orders. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user]);

  const openLogisticsModal = async (order) => {
    setSelectedOrder(order);
    setModalOpen(true);
    try {
      // Fetch logistics partners, filter by region if order has a region
      const regionParam = order.region ? `?region_id=${order.region.id}` : '';
      const response = await axiosInstance.get(`/api/logistics-partners/${regionParam}`);
      setLogisticsPartners(response.data);
    } catch (err) {
      console.error('Failed to fetch logistics partners:', err);
      setLogisticsPartners([]);
    }
  };

  const closeModal = () => {
    setModalOpen(false);
    setSelectedOrder(null);
    setSelectedPartner(null);
    setLogisticsPartners([]);
  };

  const handlePartnerSelect = (partner) => {
    setSelectedPartner(partner);
  };

  const confirmLogisticsSelection = async () => {
    if (!selectedOrder || !selectedPartner) return;
    try {
      const response = await axiosInstance.post(`/api/orders/${selectedOrder.id}/select_logistics/`, {
        logistics_partner_id: selectedPartner.id
      });
      // Update the order in the list
      const updatedOrders = orders.map(o => 
        o.id === selectedOrder.id ? { ...o, status: 'logistics_assigned', logistics: response.data } : o
      );
      setOrders(updatedOrders);
      closeModal();
    } catch (err) {
      console.error('Failed to select logistics partner:', err);
      alert('Failed to assign logistics partner. Please try again.');
    }
  };

  const openReviewModal = (order) => {
    setSelectedOrder(order);
    setReviewModalOpen(true);
  };

  const closeReviewModal = () => {
    setReviewModalOpen(false);
    setSelectedOrder(null);
    setRating(3);
    setReviewText('');
  };

  const viewPartnerReviews = async (partner) => {
    setSelectedPartner(partner);
    try {
      const response = await axiosInstance.get(`/api/logistics-partners/${partner.id}/reviews/`);
      setReviews(response.data);
      // Open a separate modal or section for reviews (for simplicity, we'll alert here)
      if (response.data.length > 0) {
        alert(`Reviews for ${partner.name}:\n${response.data.map(r => `${r.rating}/5 - ${r.review_text || 'No comment'} by ${r.buyer.username}`).join('\n')}`);
      } else {
        alert(`No reviews yet for ${partner.name}.`);
      }
    } catch (err) {
      console.error('Failed to fetch reviews:', err);
      alert('Could not load reviews at this time.');
    }
  };

  const submitReview = async () => {
    if (!selectedOrder || !rating) return;
    try {
      await axiosInstance.post(`/api/logistics-reviews/`, {
        logistics_partner: selectedOrder.logistics.logistics_partner.id,
        rating: rating,
        review_text: reviewText
      });
      // Refresh orders to prevent duplicate reviews
      const response = await axiosInstance.get('/api/orders/');
      setOrders(response.data);
      closeReviewModal();
    } catch (err) {
      console.error('Failed to submit review:', err);
      alert('Failed to submit review. Please try again.');
    }
  };

  if (loading) {
    return <div className="text-center mt-5">Loading orders...</div>;
  }

  if (error) {
    return <div className="alert alert-danger mt-5">{error}</div>;
  }

  if (!user || !user.is_buyer) {
    return <div className="alert alert-warning mt-5">This page is only accessible to buyers.</div>;
  }

  return (
    <div className="container" style={{ paddingTop: '20px' }}>
      <h2 className="mb-4">My Orders</h2>
      {orders.length === 0 ? (
        <p>You have no orders yet.</p>
      ) : (
        <div className="table-responsive">
          <table className="table table-striped">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Produce</th>
                <th>Farmer</th>
                <th>Quantity</th>
                <th>Total Price</th>
                <th>Order Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {orders.map(order => (
                <tr key={order.id}>
                  <td>{order.id}</td>
                  <td>{order.produce.name}</td>
                  <td>{order.farmer.username}</td>
                  <td>{order.quantity}</td>
                  <td>${order.total_price}</td>
                  <td>{new Date(order.order_date).toLocaleDateString()}</td>
                  <td>{order.status.replace('_', ' ').toUpperCase()}</td>
                  <td>
                    {order.status === 'logistics_pending' && (
                      <button className="btn btn-primary btn-sm" onClick={() => openLogisticsModal(order)}>
                        Select Logistics
                      </button>
                    )}
                    {order.status === 'delivered' && !order.logistics_reviewed && (
                      <button className="btn btn-secondary btn-sm" onClick={() => openReviewModal(order)}>
                        Review Logistics
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Logistics Selection Modal */}
      {modalOpen && selectedOrder && (
        <div className="modal fade show d-block" tabIndex="-1" role="dialog" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Select Logistics Partner for Order #{selectedOrder.id}</h5>
                <button type="button" className="btn-close" onClick={closeModal} aria-label="Close"></button>
              </div>
              <div className="modal-body">
                {logisticsPartners.length === 0 ? (
                  <p>No logistics partners available for your region.</p>
                ) : (
                  <div className="row">
                    {logisticsPartners.map(partner => (
                      <div key={partner.id} className="col-md-6 mb-3">
                        <div className={`card ${selectedPartner && selectedPartner.id === partner.id ? 'border-primary' : ''}`} 
                             onClick={() => handlePartnerSelect(partner)} style={{ cursor: 'pointer' }}>
                          <div className="card-body">
                            <h5 className="card-title">{partner.name}</h5>
                            <p className="card-text">
                              Base Rate: ${partner.base_rate}<br />
                              Rating: {partner.average_rating.toFixed(1)}/5<br />
                              {partner.description.slice(0, 100)}...
                            </p>
                            <button className="btn btn-link btn-sm" onClick={(e) => { e.stopPropagation(); viewPartnerReviews(partner); }}>
                              View Reviews
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={closeModal}>Close</button>
                {selectedPartner && (
                  <button type="button" className="btn btn-primary" onClick={confirmLogisticsSelection}>
                    Confirm Selection: {selectedPartner.name}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Review Submission Modal */}
      {reviewModalOpen && selectedOrder && selectedOrder.logistics && selectedOrder.logistics.logistics_partner && (
        <div className="modal fade show d-block" tabIndex="-1" role="dialog" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Review {selectedOrder.logistics.logistics_partner.name}</h5>
                <button type="button" className="btn-close" onClick={closeReviewModal} aria-label="Close"></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label htmlFor="rating" className="form-label">Rating (1-5)</label>
                  <select className="form-select" id="rating" value={rating} onChange={(e) => setRating(Number(e.target.value))}>
                    {[5, 4, 3, 2, 1].map(num => (
                      <option key={num} value={num}>{num} Star{num !== 1 ? 's' : ''}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-3">
                  <label htmlFor="reviewText" className="form-label">Comments (Optional)</label>
                  <textarea className="form-control" id="reviewText" rows="3" value={reviewText} onChange={(e) => setReviewText(e.target.value)}></textarea>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={closeReviewModal}>Close</button>
                <button type="button" className="btn btn-primary" onClick={submitReview}>Submit Review</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;
