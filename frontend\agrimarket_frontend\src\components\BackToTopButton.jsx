import React from "react";

function BackToTopButton() {
  const handleClick = (e) => {
    e.preventDefault();
    window.scrollTo({ top: 0, behavior: "smooth" });
  };
  return (
    <a
      href="#top"
      onClick={handleClick}
      className="toTop btn text-body bg-success-subtle border-0 p-0 size-3x shadow d-flex align-items-center justify-content-center position-fixed"
      style={{ right: 24, bottom: 24, zIndex: 1000 }}
      aria-label="Scroll to Top"
    >
      <span className="material-icons align-middle">arrow_upward</span>
    </a>
  );
}

export default BackToTopButton;
