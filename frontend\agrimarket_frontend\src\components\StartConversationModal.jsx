import React, { useState, useEffect } from 'react';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';

const StartConversationModal = ({ show, onHide, onConversationStarted }) => {
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [conversationType, setConversationType] = useState('buyer_farmer');
  const [title, setTitle] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');
  const { user, hasRole } = useAuth();

  useEffect(() => {
    if (show) {
      fetchUsers();
    }
  }, [show]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // Fetch farmers, buyers, and logistics partners based on current user role
      const endpoints = [];
      
      if (hasRole('buyer')) {
        endpoints.push('profile/?role=farmer');
        endpoints.push('logistics-partners/');
      }
      
      if (hasRole('farmer')) {
        endpoints.push('profile/?role=buyer');
        endpoints.push('logistics-partners/');
      }
      
      if (hasRole('admin')) {
        endpoints.push('profile/');
      }

      const responses = await Promise.all(
        endpoints.map(endpoint => axiosInstance.get(endpoint).catch(() => ({ data: [] })))
      );

      let allUsers = [];
      responses.forEach(response => {
        const data = response.data.results || response.data;
        if (Array.isArray(data)) {
          allUsers = [...allUsers, ...data];
        }
      });

      // Remove current user and duplicates
      const uniqueUsers = allUsers
        .filter(u => u.id !== user.id)
        .filter((u, index, self) => index === self.findIndex(user => user.id === u.id));

      setUsers(uniqueUsers);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelect = (selectedUser) => {
    setSelectedUsers(prev => {
      const isSelected = prev.find(u => u.id === selectedUser.id);
      if (isSelected) {
        return prev.filter(u => u.id !== selectedUser.id);
      } else {
        return [...prev, selectedUser];
      }
    });
  };

  const startConversation = async () => {
    if (selectedUsers.length === 0) {
      setError('Please select at least one user');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axiosInstance.post('conversations/start_conversation/', {
        participants: selectedUsers.map(u => u.id),
        type: conversationType,
        title: title.trim() || undefined
      });

      onConversationStarted(response.data);
      handleClose();
    } catch (err) {
      console.error('Error starting conversation:', err);
      setError(err.response?.data?.error || 'Failed to start conversation');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedUsers([]);
    setTitle('');
    setSearchTerm('');
    setError('');
    onHide();
  };

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.name?.toLowerCase().includes(searchTerm.toLowerCase()) // For logistics partners
  );

  const getUserDisplayName = (user) => {
    if (user.name) return user.name; // Logistics partner
    return `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username;
  };

  const getUserRole = (user) => {
    if (user.name) return 'Logistics Partner';
    if (user.is_farmer) return 'Farmer';
    if (user.is_buyer) return 'Buyer';
    return 'User';
  };

  if (!show) return null;

  return (
    <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Start New Conversation</h5>
            <button type="button" className="btn-close" onClick={handleClose}></button>
          </div>
          
          <div className="modal-body">
            {error && (
              <div className="alert alert-danger">
                {error}
              </div>
            )}

            {/* Search Users */}
            <div className="mb-3">
              <label className="form-label">Search Users</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by name or username..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Conversation Type */}
            <div className="mb-3">
              <label className="form-label">Conversation Type</label>
              <select
                className="form-select"
                value={conversationType}
                onChange={(e) => setConversationType(e.target.value)}
              >
                <option value="buyer_farmer">Buyer-Farmer</option>
                <option value="buyer_logistics">Buyer-Logistics</option>
                <option value="farmer_logistics">Farmer-Logistics</option>
                <option value="group">Group Chat</option>
              </select>
            </div>

            {/* Optional Title */}
            <div className="mb-3">
              <label className="form-label">Conversation Title (Optional)</label>
              <input
                type="text"
                className="form-control"
                placeholder="Enter a title for this conversation..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div className="mb-3">
                <label className="form-label">Selected Users ({selectedUsers.length})</label>
                <div className="d-flex flex-wrap gap-2">
                  {selectedUsers.map(user => (
                    <span key={user.id} className="badge bg-primary d-flex align-items-center">
                      {getUserDisplayName(user)}
                      <button
                        type="button"
                        className="btn-close btn-close-white ms-2"
                        style={{ fontSize: '0.7rem' }}
                        onClick={() => handleUserSelect(user)}
                      ></button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Users List */}
            <div className="mb-3">
              <label className="form-label">Available Users</label>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }} className="border rounded p-2">
                {loading ? (
                  <div className="text-center p-3">
                    <div className="spinner-border spinner-border-sm" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <span className="ms-2">Loading users...</span>
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center text-muted p-3">
                    No users found
                  </div>
                ) : (
                  filteredUsers.map(user => (
                    <div
                      key={user.id}
                      className={`d-flex align-items-center p-2 rounded cursor-pointer ${
                        selectedUsers.find(u => u.id === user.id) ? 'bg-light' : ''
                      }`}
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleUserSelect(user)}
                    >
                      <input
                        type="checkbox"
                        className="form-check-input me-3"
                        checked={!!selectedUsers.find(u => u.id === user.id)}
                        onChange={() => handleUserSelect(user)}
                      />
                      <div className="flex-grow-1">
                        <div className="fw-bold">{getUserDisplayName(user)}</div>
                        <small className="text-muted">
                          @{user.username || user.email} • {getUserRole(user)}
                        </small>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={handleClose}>
              Cancel
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={startConversation}
              disabled={loading || selectedUsers.length === 0}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </span>
                  Starting...
                </>
              ) : (
                'Start Conversation'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartConversationModal;
