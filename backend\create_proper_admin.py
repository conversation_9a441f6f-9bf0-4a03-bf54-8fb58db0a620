#!/usr/bin/env python3
"""
Create a proper admin user with correct privileges
"""

import os
import django

def main():
    """Create proper admin user"""
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()
    
    from core.models import User
    
    print("🔧 Creating Proper Admin User")
    print("=" * 40)
    
    # Delete existing admin if exists
    try:
        existing_admin = User.objects.get(username='admin')
        print(f"Found existing admin user: {existing_admin.username}")
        print(f"Current is_staff: {existing_admin.is_staff}")
        print(f"Current is_superuser: {existing_admin.is_superuser}")
        
        # Update existing admin
        existing_admin.is_staff = True
        existing_admin.is_superuser = True
        existing_admin.is_active = True
        existing_admin.is_farmer = False
        existing_admin.is_buyer = False
        existing_admin.save()
        
        print("✅ Updated existing admin user")
        admin_user = existing_admin
        
    except User.DoesNotExist:
        print("No existing admin found, creating new one...")
        
        # Create new admin user
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        
        # Set admin privileges
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.is_active = True
        admin_user.is_farmer = False
        admin_user.is_buyer = False
        admin_user.save()
        
        print("✅ Created new admin user")
    
    # Verify admin user
    admin_user.refresh_from_db()
    print(f"\n📋 Final admin user status:")
    print(f"   Username: {admin_user.username}")
    print(f"   Email: {admin_user.email}")
    print(f"   is_staff: {admin_user.is_staff}")
    print(f"   is_superuser: {admin_user.is_superuser}")
    print(f"   is_active: {admin_user.is_active}")
    print(f"   is_farmer: {admin_user.is_farmer}")
    print(f"   is_buyer: {admin_user.is_buyer}")
    
    # Test hasRole logic
    is_admin = bool(admin_user.is_staff)
    print(f"\n🧪 hasRole('admin') test: {is_admin}")
    
    if is_admin:
        print("✅ SUCCESS! Admin user should now see dashboard link")
        print("\n📋 Next steps:")
        print("1. Logout from frontend")
        print("2. Login again with: admin / admin123")
        print("3. Check navbar - should show: Debug: admin | Staff: Yes | hasRole(admin): Yes")
        print("4. Look for purple 'Admin Dashboard' button")
    else:
        print("❌ FAILED! Admin user still won't see dashboard link")
    
    return is_admin

if __name__ == "__main__":
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        print("   cd backend")
        print("   python create_proper_admin.py")
    else:
        main()
