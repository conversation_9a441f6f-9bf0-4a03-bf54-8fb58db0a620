# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
# For SQLite (default)
DATABASE_URL=sqlite:///db.sqlite3

# For PostgreSQL (production)
# DATABASE_URL=postgresql://username:password@localhost:5432/agrimarket

# CORS Settings
CORS_ALLOW_ALL_ORIGINS=True

# Media Files
MEDIA_URL=/media/
MEDIA_ROOT=media/

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# SMS Configuration (optional)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-provider.com/send

# Frontend Configuration
REACT_APP_API_BASE_URL=http://127.0.0.1:8000/api/
REACT_APP_MEDIA_BASE_URL=http://127.0.0.1:8000/media/