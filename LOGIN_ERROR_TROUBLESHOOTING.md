# 🚨 Login Error Troubleshooting Guide

## 🔍 **Error Analysis**

You're seeing these errors:
1. **Browser Extension Error**: `A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received`
2. **API Connection Error**: `API Error: Object` and `AxiosError`

## 🎯 **Root Cause**

The main issue is likely that **the Django backend server is not running** or not accessible at `http://127.0.0.1:8000`.

## 🛠️ **Step-by-Step Fix**

### **Step 1: Start Django Server**

**First, make sure Django server is running:**

```bash
# Open terminal and navigate to backend
cd backend

# Start Django server
python manage.py runserver 127.0.0.1:8000
```

**You should see output like:**
```
Watching for file changes with StatReloader
Performing system checks...

System check identified no issues (0 silenced).
December 21, 2024 - 10:30:00
Django version 4.2.x, using settings 'agrimarket_backend.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CTRL-BREAK.
```

### **Step 2: Verify Server is Running**

**Test the server in a new terminal:**
```bash
# Test if server responds
curl http://127.0.0.1:8000/api/

# Or open in browser
# Go to: http://127.0.0.1:8000/api/
```

**Expected response**: JSON data or Django API root page

### **Step 3: Start Frontend Server**

**In a new terminal, start the React frontend:**
```bash
# Navigate to frontend
cd frontend/agrimarket_frontend

# Start frontend server
npm run dev
```

**You should see:**
```
  VITE v4.x.x  ready in xxx ms

  ➜  Local:   http://127.0.0.1:5173/
  ➜  Network: use --host to expose
```

### **Step 4: Test Login Again**

1. **Go to**: `http://127.0.0.1:5173/login`
2. **Open browser DevTools** (F12)
3. **Go to Console tab**
4. **Try logging in**
5. **Check for new error messages**

## 🔧 **Common Issues & Solutions**

### **Issue 1: Django Server Not Running**

**Symptoms:**
- `API Error: Network Error`
- `ERR_CONNECTION_REFUSED`
- No response from `http://127.0.0.1:8000`

**Solution:**
```bash
cd backend
python manage.py runserver 127.0.0.1:8000
```

### **Issue 2: Port Already in Use**

**Symptoms:**
- `Error: That port is already in use`

**Solution:**
```bash
# Kill process on port 8000
netstat -ano | findstr :8000
taskkill /PID <PID_NUMBER> /F

# Or use different port
python manage.py runserver 127.0.0.1:8001
```

### **Issue 3: Database Not Migrated**

**Symptoms:**
- Django starts but shows database errors
- `no such table` errors

**Solution:**
```bash
cd backend
python manage.py makemigrations
python manage.py migrate
```

### **Issue 4: CORS Issues**

**Symptoms:**
- `CORS policy` errors in browser console

**Solution:**
Check `backend/agrimarket_backend/settings.py`:
```python
CORS_ALLOWED_ORIGINS = [
    "http://127.0.0.1:5173",
    "http://localhost:5173",
]
```

### **Issue 5: Browser Extension Interference**

**Symptoms:**
- `message channel closed` error

**Solution:**
- **Disable browser extensions** temporarily
- **Try incognito/private mode**
- **Clear browser cache**

## 🧪 **Testing Checklist**

### **✅ Backend Tests**
- [ ] Django server starts without errors
- [ ] `http://127.0.0.1:8000/api/` returns response
- [ ] `http://127.0.0.1:8000/admin/` loads Django admin
- [ ] Database migrations are applied

### **✅ Frontend Tests**
- [ ] React server starts on port 5173
- [ ] `http://127.0.0.1:5173` loads homepage
- [ ] Browser console shows no errors
- [ ] Network tab shows API calls being made

### **✅ Connection Tests**
- [ ] Frontend can reach backend API
- [ ] Login form submits without network errors
- [ ] API responses are received

## 🔍 **Debugging Commands**

### **Check Django Server Status**
```bash
# Test API endpoint
curl -X GET http://127.0.0.1:8000/api/

# Test login endpoint
curl -X POST http://127.0.0.1:8000/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### **Check Frontend Network**
1. **Open DevTools** (F12)
2. **Go to Network tab**
3. **Try login**
4. **Look for POST request to `/api/login/`**
5. **Check request/response details**

## 🚀 **Quick Fix Steps**

### **1. Restart Everything**
```bash
# Terminal 1: Backend
cd backend
python manage.py runserver 127.0.0.1:8000

# Terminal 2: Frontend
cd frontend/agrimarket_frontend
npm run dev
```

### **2. Test Basic Connection**
- **Backend**: `http://127.0.0.1:8000/api/`
- **Frontend**: `http://127.0.0.1:5173`

### **3. Try Login**
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Admin

### **4. Check Console**
- **Look for detailed error messages**
- **Check Network tab for failed requests**

## 📊 **Expected vs Actual**

### **Expected Behavior:**
1. ✅ Django server running on port 8000
2. ✅ React server running on port 5173
3. ✅ API calls successful
4. ✅ Login redirects to home page

### **Current Issues:**
1. ❌ Django server not responding
2. ❌ API calls failing
3. ❌ Network connection errors

## 🎯 **Most Likely Solution**

**The Django server is not running.** Start it with:

```bash
cd backend
python manage.py runserver 127.0.0.1:8000
```

Then test login again.

## 📋 **Summary**

1. **Start Django server** on port 8000
2. **Start React server** on port 5173
3. **Test API connection** in browser
4. **Try login** with admin credentials
5. **Check browser console** for errors

**Once both servers are running, the login should work correctly!** 🎉

## 🆘 **If Still Not Working**

Share these details:
1. **Django server output** when starting
2. **Browser console errors** (full messages)
3. **Network tab** request/response details
4. **Any error messages** from terminal

This will help identify the specific issue! 🔍
