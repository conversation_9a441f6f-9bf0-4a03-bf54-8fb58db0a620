import React, { useState, useEffect } from 'react';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';
import Navbar from '../components/Navbar';

const LogisticsDashboard = () => {
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0
  });
  const [orders, setOrders] = useState([]);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch logistics partner profile
      const profileResponse = await axiosInstance.get('logistics-partners/me/');
      setProfile(profileResponse.data);
      
      // Fetch assigned orders
      const ordersResponse = await axiosInstance.get('order-logistics/my-orders/');
      setOrders(ordersResponse.data.results || ordersResponse.data);
      
      // Calculate stats
      const totalOrders = ordersResponse.data.length || 0;
      const pendingOrders = ordersResponse.data.filter(order => 
        order.status === 'pending' || order.status === 'in_transit'
      ).length || 0;
      const completedOrders = ordersResponse.data.filter(order => 
        order.status === 'delivered'
      ).length || 0;
      const totalRevenue = ordersResponse.data.reduce((sum, order) => 
        sum + (parseFloat(order.cost) || 0), 0
      );
      
      setStats({
        totalOrders,
        pendingOrders,
        completedOrders,
        totalRevenue
      });
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      await axiosInstance.patch(`order-logistics/${orderId}/`, {
        status: newStatus
      });
      
      // Refresh orders
      fetchDashboardData();
    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status');
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'confirmed': return 'bg-info';
      case 'in_transit': return 'bg-primary';
      case 'delivered': return 'bg-success';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading dashboard...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container-fluid dashboard-content">
        {error && (
          <div className="alert alert-danger alert-dismissible fade show">
            {error}
            <button type="button" className="btn-close" onClick={() => setError(null)}></button>
          </div>
        )}

        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <h2 className="mb-0">Logistics Dashboard</h2>
            <p className="text-muted">Welcome back, {profile?.name || user?.username}!</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="row mb-4">
          <div className="col-md-3 mb-3">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">{stats.totalOrders}</h4>
                    <p className="mb-0">Total Orders</p>
                  </div>
                  <span className="material-icons" style={{ fontSize: '2rem' }}>
                    local_shipping
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-md-3 mb-3">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">{stats.pendingOrders}</h4>
                    <p className="mb-0">Pending Orders</p>
                  </div>
                  <span className="material-icons" style={{ fontSize: '2rem' }}>
                    pending
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-md-3 mb-3">
            <div className="card bg-success text-white">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">{stats.completedOrders}</h4>
                    <p className="mb-0">Completed</p>
                  </div>
                  <span className="material-icons" style={{ fontSize: '2rem' }}>
                    check_circle
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-md-3 mb-3">
            <div className="card bg-info text-white">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">${stats.totalRevenue.toFixed(2)}</h4>
                    <p className="mb-0">Total Revenue</p>
                  </div>
                  <span className="material-icons" style={{ fontSize: '2rem' }}>
                    attach_money
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Recent Orders</h5>
                <button className="btn btn-sm btn-primary">
                  <span className="material-icons me-1">refresh</span>
                  Refresh
                </button>
              </div>
              <div className="card-body">
                {orders.length === 0 ? (
                  <div className="text-center py-5">
                    <span className="material-icons text-muted" style={{ fontSize: '4rem' }}>
                      inbox
                    </span>
                    <h4 className="mt-3 text-muted">No Orders Yet</h4>
                    <p className="text-muted">Orders assigned to you will appear here.</p>
                  </div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Order ID</th>
                          <th>Customer</th>
                          <th>Pickup Location</th>
                          <th>Delivery Location</th>
                          <th>Status</th>
                          <th>Cost</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {orders.map(order => (
                          <tr key={order.id}>
                            <td>#{order.order?.id || order.id}</td>
                            <td>{order.order?.buyer?.username || 'N/A'}</td>
                            <td>{order.order?.farmer?.location || 'N/A'}</td>
                            <td>{order.order?.delivery_address || 'N/A'}</td>
                            <td>
                              <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                                {order.status?.replace('_', ' ').toUpperCase()}
                              </span>
                            </td>
                            <td>${order.cost || '0.00'}</td>
                            <td>
                              <div className="btn-group btn-group-sm">
                                {order.status === 'pending' && (
                                  <button
                                    className="btn btn-success"
                                    onClick={() => updateOrderStatus(order.id, 'confirmed')}
                                  >
                                    Accept
                                  </button>
                                )}
                                {order.status === 'confirmed' && (
                                  <button
                                    className="btn btn-primary"
                                    onClick={() => updateOrderStatus(order.id, 'in_transit')}
                                  >
                                    Start Delivery
                                  </button>
                                )}
                                {order.status === 'in_transit' && (
                                  <button
                                    className="btn btn-success"
                                    onClick={() => updateOrderStatus(order.id, 'delivered')}
                                  >
                                    Mark Delivered
                                  </button>
                                )}
                                <button className="btn btn-outline-primary">
                                  <span className="material-icons">visibility</span>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Profile Summary */}
        {profile && (
          <div className="row mt-4">
            <div className="col-md-6">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Profile Information</h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-sm-6">
                      <strong>Business Name:</strong>
                      <p>{profile.name}</p>
                    </div>
                    <div className="col-sm-6">
                      <strong>Contact Phone:</strong>
                      <p>{profile.contact_phone || 'Not provided'}</p>
                    </div>
                    <div className="col-sm-6">
                      <strong>Base Rate:</strong>
                      <p>${profile.base_rate}/km</p>
                    </div>
                    <div className="col-sm-6">
                      <strong>Verification Status:</strong>
                      <p>
                        <span className={`badge ${profile.is_verified ? 'bg-success' : 'bg-warning'}`}>
                          {profile.is_verified ? 'Verified' : 'Pending Verification'}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div className="mt-3">
                    <button className="btn btn-primary">
                      <span className="material-icons me-1">edit</span>
                      Edit Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Service Areas</h5>
                </div>
                <div className="card-body">
                  {profile.service_regions?.length > 0 ? (
                    <div className="d-flex flex-wrap gap-2">
                      {profile.service_regions.map(region => (
                        <span key={region.id} className="badge bg-secondary">
                          {region.name}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted">No service areas specified</p>
                  )}
                  <div className="mt-3">
                    <button className="btn btn-outline-primary">
                      <span className="material-icons me-1">add_location</span>
                      Manage Service Areas
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default LogisticsDashboard;
