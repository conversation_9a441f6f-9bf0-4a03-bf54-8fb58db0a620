import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';
import Navbar from '../components/Navbar';

const LogisticsLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axiosInstance.post('login/', {
        username: formData.username,
        password: formData.password,
        role: 'logistics' // Specify logistics role
      });

      // Login successful
      login(response.data.user, response.data.token);
      navigate('/logistics-dashboard');
    } catch (err) {
      console.error('Login error:', err);
      if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError('Login failed. Please check your credentials.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <div className="container d-flex justify-content-center align-items-center" style={{ minHeight: '80vh' }}>
        <div className="card shadow-lg" style={{ maxWidth: '400px', width: '100%', borderRadius: '15px' }}>
          <div className="card-body p-5">
            <div className="text-center mb-4">
              <span className="material-icons text-primary" style={{ fontSize: '3rem' }}>
                local_shipping
              </span>
              <h2 className="card-title mt-2" style={{ color: '#29ab4e', fontWeight: 'bold' }}>
                Logistics Partner Login
              </h2>
              <p className="text-muted">Access your logistics dashboard</p>
            </div>

            {error && (
              <div className="alert alert-danger" role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-3">
                <label htmlFor="username" className="form-label">
                  <span className="material-icons me-2" style={{ fontSize: '1.2rem', verticalAlign: 'middle' }}>
                    person
                  </span>
                  Username
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  required
                  placeholder="Enter your username"
                  style={{ borderRadius: '8px', padding: '12px' }}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="password" className="form-label">
                  <span className="material-icons me-2" style={{ fontSize: '1.2rem', verticalAlign: 'middle' }}>
                    lock
                  </span>
                  Password
                </label>
                <input
                  type="password"
                  className="form-control"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  placeholder="Enter your password"
                  style={{ borderRadius: '8px', padding: '12px' }}
                />
              </div>

              <button
                type="submit"
                className="btn btn-primary w-100 mb-3"
                disabled={loading}
                style={{
                  borderRadius: '8px',
                  padding: '12px',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none'
                }}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </span>
                    Logging in...
                  </>
                ) : (
                  <>
                    <span className="material-icons me-2" style={{ fontSize: '1.2rem', verticalAlign: 'middle' }}>
                      login
                    </span>
                    Login to Dashboard
                  </>
                )}
              </button>
            </form>

            <div className="text-center">
              <p className="mb-2">
                <small className="text-muted">
                  Don't have an account?{' '}
                  <Link to="/logistics-register" className="text-primary text-decoration-none">
                    Register as Logistics Partner
                  </Link>
                </small>
              </p>
              <p className="mb-0">
                <small className="text-muted">
                  Not a logistics partner?{' '}
                  <Link to="/login" className="text-primary text-decoration-none">
                    Regular Login
                  </Link>
                </small>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Info Section */}
      <div className="container mt-5 mb-5">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="card bg-light">
              <div className="card-body text-center">
                <h5 className="card-title text-primary">Logistics Partner Access</h5>
                <p className="card-text">
                  Access your logistics dashboard to manage deliveries, track orders, and communicate with buyers and farmers.
                </p>
                <div className="row mt-4">
                  <div className="col-md-4">
                    <span className="material-icons text-primary" style={{ fontSize: '2rem' }}>
                      dashboard
                    </span>
                    <h6 className="mt-2">Dashboard</h6>
                    <small className="text-muted">View orders and analytics</small>
                  </div>
                  <div className="col-md-4">
                    <span className="material-icons text-primary" style={{ fontSize: '2rem' }}>
                      local_shipping
                    </span>
                    <h6 className="mt-2">Deliveries</h6>
                    <small className="text-muted">Manage delivery assignments</small>
                  </div>
                  <div className="col-md-4">
                    <span className="material-icons text-primary" style={{ fontSize: '2rem' }}>
                      message
                    </span>
                    <h6 className="mt-2">Communication</h6>
                    <small className="text-muted">Chat with buyers and farmers</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LogisticsLogin;
