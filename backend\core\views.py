from rest_framework import viewsets, status, permissions, generics, filters, serializers
from rest_framework.decorators import action, api_view, permission_classes, throttle_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from django.contrib.auth import authenticate
from django.utils import timezone
from django.db.models import Avg, Count, Q
from django.contrib.auth import get_user_model
from .models import Produce, ProduceCategory, MarketPrice, SMSAlert, ContactRequest, PriceSource, Collection, Favorite, Watchlist, UserProfile, Region, Order, LogisticsPartner, OrderLogistics, LogisticsPartnerReview, Conversation, Message, MessageRead, MessageNotification
from .serializers import (
    ProduceSerializer, ProduceCategorySerializer, MarketPriceSerializer, SMSAlertSerializer,
    ContactRequestSerializer, PriceSourceSerializer, CollectionSerializer, FavoriteSerializer,
    WatchlistSerializer, UserProfileSerializer, RegionSerializer, OrderSerializer,
    LogisticsPartnerSerializer, LogisticsPartnerRegistrationSerializer, OrderLogisticsSerializer, LogisticsPartnerReviewSerializer,
    UserSerializer, UserRegistrationSerializer, UserLoginSerializer,
    ConversationSerializer, MessageSerializer, MessageNotificationSerializer
)

User = get_user_model()

# Custom Throttle Classes
class LoginRateThrottle(AnonRateThrottle):
    scope = 'login'

class RegisterRateThrottle(AnonRateThrottle):
    scope = 'register'

# Permission Classes
class IsFarmer(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_farmer

class IsBuyer(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_buyer

class IsAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_staff

# ViewSets for existing models
class ProduceViewSet(viewsets.ModelViewSet):
    queryset = Produce.objects.all()
    serializer_class = ProduceSerializer

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsFarmer()]
        elif self.action in ['approve', 'reject']:
            return [IsAdmin()]
        return [permissions.AllowAny()]

    def get_queryset(self):
        queryset = Produce.objects.all()
        if not self.request.user.is_staff:
            queryset = queryset.filter(approved=True)
        return queryset

    def perform_create(self, serializer):
        serializer.save(farmer=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsAdmin])
    def approve(self, request, pk=None):
        """Approve a produce listing"""
        produce = self.get_object()
        produce.approved = True
        produce.save()
        return Response({'message': 'Produce approved successfully'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], permission_classes=[IsAdmin])
    def reject(self, request, pk=None):
        """Reject a produce listing"""
        produce = self.get_object()
        produce.approved = False
        produce.save()
        return Response({'message': 'Produce rejected'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'], permission_classes=[IsAdmin])
    def pending(self, request):
        """Get all pending produce listings"""
        pending_produce = Produce.objects.filter(approved=False)
        page = self.paginate_queryset(pending_produce)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(pending_produce, many=True)
        return Response(serializer.data)

class ProduceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ProduceCategory.objects.all()
    serializer_class = ProduceCategorySerializer
    permission_classes = [permissions.AllowAny]

class MarketPriceViewSet(viewsets.ModelViewSet):
    queryset = MarketPrice.objects.all()
    serializer_class = MarketPriceSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = ['crop_name']

    def get_permissions(self):
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdmin()]
        return [permissions.AllowAny()]

    def get_queryset(self):
        return MarketPrice.objects.filter(approved=True) if not self.request.user.is_staff else MarketPrice.objects.all()

class SMSAlertViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = SMSAlertSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return SMSAlert.objects.filter(user=self.request.user)

class ContactRequestViewSet(viewsets.ModelViewSet):
    serializer_class = ContactRequestSerializer
    permission_classes = [IsBuyer]

    def get_queryset(self):
        return ContactRequest.objects.filter(buyer=self.request.user)

    def perform_create(self, serializer):
        # Get the produce and its farmer
        produce_id = self.request.data.get('produce')
        if not produce_id:
            raise serializers.ValidationError("Produce ID is required")

        try:
            produce = Produce.objects.get(id=produce_id)
            serializer.save(buyer=self.request.user, farmer=produce.farmer, produce=produce)
        except Produce.DoesNotExist:
            raise serializers.ValidationError("Produce not found")

class PriceSourceViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = PriceSource.objects.all()
    serializer_class = PriceSourceSerializer
    permission_classes = [permissions.AllowAny]

class CollectionViewSet(viewsets.ModelViewSet):
    serializer_class = CollectionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Collection.objects.filter(owner=self.request.user)

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

class FavoriteViewSet(viewsets.ModelViewSet):
    serializer_class = FavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Favorite.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class WatchlistViewSet(viewsets.ModelViewSet):
    serializer_class = WatchlistSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Watchlist.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class UserProfileViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return UserProfile.objects.filter(user=self.request.user)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user's profile"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            # Create profile if it doesn't exist
            profile = UserProfile.objects.create(user=request.user)
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)

# ViewSets for new logistics models
class RegionViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = [permissions.AllowAny]

class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_buyer:
            return Order.objects.filter(buyer=user)
        elif user.is_farmer:
            return Order.objects.filter(farmer=user)
        elif user.is_staff:
            return Order.objects.all()
        return Order.objects.none()

    def perform_create(self, serializer):
        # Ensure only buyers can create orders
        if not self.request.user.is_buyer:
            raise permissions.PermissionDenied("Only buyers can create orders.")
        serializer.save(buyer=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsBuyer])
    def select_logistics(self, request, pk=None):
        order = self.get_object()
        if order.status != 'logistics_pending':
            return Response({'error': 'Logistics selection is not available for this order status.'}, status=status.HTTP_400_BAD_REQUEST)

        logistics_partner_id = request.data.get('logistics_partner_id')
        if not logistics_partner_id:
            return Response({'error': 'Logistics partner ID is required.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            logistics_partner = LogisticsPartner.objects.get(id=logistics_partner_id, is_verified=True)
        except LogisticsPartner.DoesNotExist:
            return Response({'error': 'Invalid or unverified logistics partner.'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if the logistics partner serves the order's region
        if order.region and order.region not in logistics_partner.service_regions.all():
            return Response({'error': 'Selected logistics partner does not serve the order region.'}, status=status.HTTP_400_BAD_REQUEST)

        # Create or update OrderLogistics
        order_logistics, created = OrderLogistics.objects.get_or_create(
            order=order,
            defaults={'logistics_partner': logistics_partner}
        )
        if not created:
            order_logistics.logistics_partner = logistics_partner
            order_logistics.save()

        # Update order status
        order.status = 'logistics_assigned'
        order.save()

        return Response(OrderLogisticsSerializer(order_logistics).data, status=status.HTTP_200_OK)

class LogisticsPartnerViewSet(viewsets.ModelViewSet):
    serializer_class = LogisticsPartnerSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        queryset = LogisticsPartner.objects.filter(is_verified=True)
        region_id = self.request.query_params.get('region_id')
        if region_id:
            queryset = queryset.filter(service_regions__id=region_id)
        return queryset

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def me(self, request):
        """Get current logistics partner's profile"""
        try:
            logistics_partner = LogisticsPartner.objects.get(user=request.user)
            serializer = LogisticsPartnerSerializer(logistics_partner)
            return Response(serializer.data)
        except LogisticsPartner.DoesNotExist:
            return Response({'error': 'Logistics partner profile not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def register(self, request):
        """Register a new logistics partner with user account"""
        data = request.data
        serializer = LogisticsPartnerRegistrationSerializer(data=data)
        if serializer.is_valid():
            logistics_partner = serializer.save()  # Creates user and logistics partner
            return Response({
                'message': 'Registration successful. Your account has been created and is pending admin verification.',
                'logistics_partner_id': logistics_partner.id,
                'username': logistics_partner.user.username,
                'name': logistics_partner.name
            }, status=status.HTTP_201_CREATED)
        else:
            # Return detailed error information
            return Response({
                'errors': serializer.errors,
                'message': 'Registration failed. Please check the provided data.'
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], permission_classes=[permissions.AllowAny])
    def reviews(self, request, pk=None):
        logistics_partner = self.get_object()
        reviews = logistics_partner.reviews.all()
        page = self.paginate_queryset(reviews)
        if page is not None:
            serializer = LogisticsPartnerReviewSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = LogisticsPartnerReviewSerializer(reviews, many=True)
        return Response(serializer.data)

class OrderLogisticsViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OrderLogisticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return OrderLogistics.objects.all()
        return OrderLogistics.objects.filter(order__buyer=user) | OrderLogistics.objects.filter(order__farmer=user)

class LogisticsPartnerReviewViewSet(viewsets.ModelViewSet):
    serializer_class = LogisticsPartnerReviewSerializer
    permission_classes = [IsBuyer]

    def get_queryset(self):
        return LogisticsPartnerReview.objects.filter(buyer=self.request.user)

    def perform_create(self, serializer):
        logistics_partner_id = self.request.data.get('logistics_partner')
        # Ensure the buyer has completed an order with this logistics partner
        if not OrderLogistics.objects.filter(
            order__buyer=self.request.user,
            logistics_partner_id=logistics_partner_id,
            order__status='delivered'
        ).exists():
            raise permissions.PermissionDenied("You can only review logistics partners for completed orders.")
        serializer.save(buyer=self.request.user)


# Authentication Views
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@throttle_classes([RegisterRateThrottle])
def register_user(request):
    """User registration endpoint"""
    serializer = UserRegistrationSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({'error': serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Create user
        user = serializer.save()

        # Create user profile
        UserProfile.objects.create(user=user)

        # Create token
        token, _ = Token.objects.get_or_create(user=user)

        return Response({
            'message': 'Registration successful',
            'user': UserSerializer(user).data,
            'token': token.key
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@throttle_classes([LoginRateThrottle])
def login_user(request):
    """User login endpoint"""
    serializer = UserLoginSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({'error': 'Username and password are required'}, status=status.HTTP_400_BAD_REQUEST)

    username = serializer.validated_data['username']
    password = serializer.validated_data['password']
    role = serializer.validated_data.get('role')

    # Authenticate user
    user = authenticate(username=username, password=password)

    if not user:
        return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

    # Optional: Validate role if provided
    if role:
        if role == 'farmer' and not user.is_farmer:
            return Response({'error': 'User is not registered as a farmer'}, status=status.HTTP_403_FORBIDDEN)
        elif role == 'buyer' and not user.is_buyer:
            return Response({'error': 'User is not registered as a buyer'}, status=status.HTTP_403_FORBIDDEN)
        elif role == 'logistics' and not user.is_logistics_partner:
            return Response({'error': 'User is not registered as a logistics partner'}, status=status.HTTP_403_FORBIDDEN)
        elif role == 'admin' and not user.is_staff:
            return Response({'error': 'User is not an admin'}, status=status.HTTP_403_FORBIDDEN)

    # Get or create token
    token, _ = Token.objects.get_or_create(user=user)

    return Response({
        'message': 'Login successful',
        'user': UserSerializer(user).data,
        'token': token.key
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_user(request):
    """User logout endpoint"""
    try:
        # Delete the user's token
        request.user.auth_token.delete()
        return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)
    except:
        return Response({'error': 'Logout failed'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def health_check(request):
    """Simple health check endpoint"""
    return Response({
        'status': 'healthy',
        'message': 'AgriMarket API is running',
        'timestamp': timezone.now().isoformat()
    }, status=status.HTTP_200_OK)

# Admin Dashboard Views
@api_view(['GET'])
@permission_classes([IsAdmin])
def admin_dashboard_stats(request):
    """Get dashboard statistics for admin"""
    try:
        stats = {
            'users': {
                'total': User.objects.count(),
                'farmers': User.objects.filter(is_farmer=True).count(),
                'buyers': User.objects.filter(is_buyer=True).count(),
                'admins': User.objects.filter(is_staff=True).count(),
                'new_this_month': User.objects.filter(
                    created_at__gte=timezone.now().replace(day=1)
                ).count()
            },
            'produce': {
                'total': Produce.objects.count(),
                'approved': Produce.objects.filter(approved=True).count(),
                'pending': Produce.objects.filter(approved=False).count(),
                'organic': Produce.objects.filter(is_organic=True).count()
            },
            'logistics': {
                'total': LogisticsPartner.objects.count(),
                'verified': LogisticsPartner.objects.filter(is_verified=True).count(),
                'pending': LogisticsPartner.objects.filter(is_verified=False).count()
            },
            'market_prices': {
                'total': MarketPrice.objects.count(),
                'approved': MarketPrice.objects.filter(approved=True).count(),
                'pending': MarketPrice.objects.filter(approved=False).count()
            },
            'orders': {
                'total': Order.objects.count(),
                'pending': Order.objects.filter(status='pending').count(),
                'completed': Order.objects.filter(status='delivered').count()
            }
        }
        return Response(stats, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAdmin])
def admin_pending_approvals(request):
    """Get all items pending admin approval"""
    try:
        pending_produce = Produce.objects.filter(approved=False).select_related('farmer', 'category')
        pending_logistics = LogisticsPartner.objects.filter(is_verified=False)
        pending_prices = MarketPrice.objects.filter(approved=False)

        data = {
            'produce': ProduceSerializer(pending_produce, many=True).data,
            'logistics_partners': LogisticsPartnerSerializer(pending_logistics, many=True).data,
            'market_prices': MarketPriceSerializer(pending_prices, many=True).data
        }
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAdmin])
def admin_approve_produce(request, produce_id):
    """Approve or reject produce listing"""
    try:
        produce = Produce.objects.get(id=produce_id)
        action = request.data.get('action')  # 'approve' or 'reject'

        if action == 'approve':
            produce.approved = True
            produce.save()
            return Response({'message': f'Produce "{produce.name}" approved successfully'})
        elif action == 'reject':
            produce.delete()
            return Response({'message': 'Produce rejected and removed'})
        else:
            return Response({'error': 'Invalid action'}, status=status.HTTP_400_BAD_REQUEST)
    except Produce.DoesNotExist:
        return Response({'error': 'Produce not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAdmin])
def admin_approve_logistics(request, logistics_id):
    """Approve or reject logistics partner"""
    try:
        logistics = LogisticsPartner.objects.get(id=logistics_id)
        action = request.data.get('action')

        if action == 'approve':
            logistics.is_verified = True
            logistics.save()
            return Response({'message': f'Logistics partner "{logistics.name}" approved successfully'})
        elif action == 'reject':
            logistics.delete()
            return Response({'message': 'Logistics partner rejected and removed'})
        else:
            return Response({'error': 'Invalid action'}, status=status.HTTP_400_BAD_REQUEST)
    except LogisticsPartner.DoesNotExist:
        return Response({'error': 'Logistics partner not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAdmin])
def admin_all_users(request):
    """Get all users for admin management"""
    try:
        users = User.objects.all().order_by('-created_at')
        data = []
        for user in users:
            data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_farmer': user.is_farmer,
                'is_buyer': user.is_buyer,
                'is_staff': user.is_staff,
                'is_active': user.is_active,
                'created_at': user.created_at,
                'last_login': user.last_login
            })
        return Response(data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Messaging Views
class ConversationViewSet(viewsets.ModelViewSet):
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(participants=self.request.user, is_active=True)

    @action(detail=False, methods=['post'])
    def start_conversation(self, request):
        """Start a new conversation with specified users"""
        participant_ids = request.data.get('participants', [])
        conversation_type = request.data.get('type', 'buyer_farmer')
        title = request.data.get('title', '')
        related_order_id = request.data.get('related_order')
        related_produce_id = request.data.get('related_produce')

        if not participant_ids:
            return Response({'error': 'Participants are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Add current user to participants
        participant_ids.append(request.user.id)
        participants = User.objects.filter(id__in=participant_ids)

        if participants.count() < 2:
            return Response({'error': 'At least 2 participants required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if conversation already exists between these users
        existing_conversation = None
        if participants.count() == 2:
            for conv in Conversation.objects.filter(participants__in=participants):
                if conv.participants.count() == 2 and set(conv.participants.all()) == set(participants):
                    existing_conversation = conv
                    break

        if existing_conversation:
            serializer = self.get_serializer(existing_conversation)
            return Response(serializer.data)

        # Create new conversation
        conversation = Conversation.objects.create(
            type=conversation_type,
            title=title
        )

        if related_order_id:
            try:
                conversation.related_order = Order.objects.get(id=related_order_id)
            except Order.DoesNotExist:
                pass

        if related_produce_id:
            try:
                conversation.related_produce = Produce.objects.get(id=related_produce_id)
            except Produce.DoesNotExist:
                pass

        conversation.save()
        conversation.participants.set(participants)

        serializer = self.get_serializer(conversation)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark all messages in conversation as read"""
        conversation = self.get_object()
        unread_messages = conversation.messages.exclude(sender=request.user)

        for message in unread_messages:
            MessageRead.objects.get_or_create(message=message, user=request.user)

        return Response({'message': 'Messages marked as read'})

class MessageViewSet(viewsets.ModelViewSet):
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        conversation_id = self.request.query_params.get('conversation')
        if conversation_id:
            # Ensure user is participant in the conversation
            conversation = Conversation.objects.filter(
                id=conversation_id,
                participants=self.request.user
            ).first()
            if conversation:
                return Message.objects.filter(conversation=conversation)
        return Message.objects.none()

    def perform_create(self, serializer):
        conversation_id = self.request.data.get('conversation')
        try:
            conversation = Conversation.objects.get(
                id=conversation_id,
                participants=self.request.user
            )
            message = serializer.save(sender=self.request.user, conversation=conversation)

            # Update conversation timestamp
            conversation.updated_at = timezone.now()
            conversation.save()

            # Create notifications for other participants
            other_participants = conversation.participants.exclude(id=self.request.user.id)
            for participant in other_participants:
                MessageNotification.objects.create(
                    recipient=participant,
                    message=message
                )

        except Conversation.DoesNotExist:
            raise serializers.ValidationError("Conversation not found or access denied")

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark specific message as read"""
        message = self.get_object()
        MessageRead.objects.get_or_create(message=message, user=request.user)
        return Response({'message': 'Message marked as read'})

class MessageNotificationViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = MessageNotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return MessageNotification.objects.filter(recipient=self.request.user)

    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read"""
        MessageNotification.objects.filter(recipient=request.user, is_read=False).update(is_read=True)
        return Response({'message': 'All notifications marked as read'})
