#!/usr/bin/env python3
"""
AgriMarket Startup Script
This script helps you start both backend and frontend services properly.
"""

import os
import sys
import subprocess
import time
import platform
from pathlib import Path

def run_command(command, cwd=None, shell=True):
    """Run a command and return the process."""
    try:
        if platform.system() == "Windows":
            # On Windows, use shell=True for better compatibility
            return subprocess.Popen(command, shell=shell, cwd=cwd)
        else:
            # On Unix-like systems
            return subprocess.Popen(command.split(), cwd=cwd)
    except Exception as e:
        print(f"Error running command: {command}")
        print(f"Error: {e}")
        return None

def check_python_version():
    """Check if Python version is adequate."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def setup_backend():
    """Set up and start the backend."""
    print("\n🔧 Setting up Backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return None
    
    # Check if virtual environment exists
    venv_dir = backend_dir / "myenv"
    if venv_dir.exists():
        print("✅ Virtual environment found")
        if platform.system() == "Windows":
            python_exe = venv_dir / "Scripts" / "python.exe"
            pip_exe = venv_dir / "Scripts" / "pip.exe"
        else:
            python_exe = venv_dir / "bin" / "python"
            pip_exe = venv_dir / "bin" / "pip"
    else:
        print("⚠️  No virtual environment found, using system Python")
        python_exe = "python"
        pip_exe = "pip"
    
    # Install requirements
    print("📦 Installing requirements...")
    req_process = subprocess.run([str(pip_exe), "install", "-r", "requirements.txt"], 
                                cwd=backend_dir, capture_output=True, text=True)
    if req_process.returncode != 0:
        print("⚠️  Requirements installation had issues, continuing anyway...")
    
    # Run migrations
    print("🗄️  Running migrations...")
    migrate_process = subprocess.run([str(python_exe), "manage.py", "migrate"], 
                                   cwd=backend_dir, capture_output=True, text=True)
    if migrate_process.returncode != 0:
        print("⚠️  Migration had issues:")
        print(migrate_process.stderr)
    
    # Start the server
    print("🚀 Starting Django server...")
    server_command = f"{python_exe} manage.py runserver 127.0.0.1:8000"
    return run_command(server_command, cwd=backend_dir)

def setup_frontend():
    """Set up and start the frontend."""
    print("\n🔧 Setting up Frontend...")
    
    frontend_dir = Path("frontend/agrimarket_frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return None
    
    # Check if node_modules exists
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 Installing npm packages...")
        npm_install = subprocess.run(["npm", "install"], cwd=frontend_dir, 
                                   capture_output=True, text=True)
        if npm_install.returncode != 0:
            print("❌ npm install failed:")
            print(npm_install.stderr)
            return None
    
    # Start the development server
    print("🚀 Starting React development server...")
    return run_command("npm run dev", cwd=frontend_dir)

def main():
    """Main function to start both services."""
    print("🌾 AgriMarket Startup Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Start backend
    backend_process = setup_backend()
    if not backend_process:
        print("❌ Failed to start backend")
        sys.exit(1)
    
    # Wait a bit for backend to start
    print("⏳ Waiting for backend to start...")
    time.sleep(5)
    
    # Start frontend
    frontend_process = setup_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend")
        backend_process.terminate()
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("🎉 Both services are starting!")
    print("📱 Backend: http://127.0.0.1:8000")
    print("🌐 Frontend: http://127.0.0.1:5173")
    print("📚 API Docs: http://127.0.0.1:8000/swagger/")
    print("\n⚠️  Keep this terminal open. Press Ctrl+C to stop both services.")
    
    try:
        # Wait for both processes
        while True:
            time.sleep(1)
            # Check if processes are still running
            if backend_process.poll() is not None:
                print("❌ Backend process stopped")
                break
            if frontend_process.poll() is not None:
                print("❌ Frontend process stopped")
                break
    except KeyboardInterrupt:
        print("\n🛑 Stopping services...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Services stopped")

if __name__ == "__main__":
    main()
