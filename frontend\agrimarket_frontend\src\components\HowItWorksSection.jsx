import React from "react";
import { FaUserPlus, FaSeedling, FaHandshake } from "react-icons/fa";

const steps = [
  {
    icon: <FaUserPlus size={36} className="text-success" />,
    title: "Register",
    desc: "Sign up as a farmer or buyer to access the AgriMarket platform.",
  },
  {
    icon: <FaSeedling size={36} className="text-success" />,
    title: "Post Your Produce",
    desc: "List your fresh produce or browse available crops and market prices.",
  },
  {
    icon: <FaHandshake size={36} className="text-success" />,
    title: "Connect & Sell",
    desc: "Connect with buyers or farmers, negotiate, and complete your transaction securely.",
  },
];

function HowItWorksSection() {
  return (
    <section className="container py-5">
      <h2 className="text-center mb-5 text-primary">How AgriMarket Works</h2>
      <div className="row pt-4">
        {steps.map((step, idx) => (
          <div className="col-lg-4 mb-4" key={idx}>
            <div className="card hover-lift pt-6 p-4 text-center position-relative">              <div className="position-absolute top-0 start-50 translate-middle rounded-circle bg-success bg-opacity-75 shadow d-flex align-items-center justify-content-center step-circle">
                {step.icon}
              </div>
              <h5 className="mb-4 mt-5 position-relative">{step.title}</h5>
              <p className="mb-0">{step.desc}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

export default HowItWorksSection;
