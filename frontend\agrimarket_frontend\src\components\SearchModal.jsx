import React, { useRef } from "react";

function SearchModal() {
  // Focus input when modal opens (<PERSON><PERSON><PERSON> handles autofocus, but for React SSR safety, useRef is good)
  const inputRef = useRef(null);

  // Example AgriMarket tags
  const tags = [
    "Maize", "Tomatoes", "Groundnuts", "Cattle", "Wheat", "Organic", "Vegetables", "Fruits"
  ];

  return (
    <div className="modal fade" id="modal_search" tabIndex="-1" aria-labelledby="modal_searchLabel" aria-hidden="true">
      <div className="modal-dialog modal-dialog-scrollable">
        <div className="modal-content overflow-hidden">
          <div className="modal-body p-3">
            <form>
              <div className="search-input-wrapper">
                <span className="material-icons search-icon">search</span>
                <input
                  type="text"
                  className="form-control search-input"
                  ref={inputRef}
                  autoFocus
                  placeholder="Search AgriMarket..."
                />
                <div className="position-absolute end-0 top-0 h-100 me-2 d-flex align-items-center">
                  <button type="button" 
                    className="search-close-btn" 
                    data-bs-dismiss="modal" 
                    aria-label="Close"
                  >
                    <span className="material-icons">close</span>
                  </button>
                </div>
              </div>
            </form>
            <div className="pt-4 px-2">
              <h6 className="mb-3">
                <span className="material-icons align-middle me-2 opacity-50">sell</span>
                Tags
              </h6>
              <div className="d-flex flex-wrap gap-2">
                {tags.map(tag => (
                  <a key={tag} href="#" className="search-tag hover-lift-sm">{tag}</a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SearchModal;
