import React from "react";

function Help() {
  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <h1 className="mb-4 text-primary">Help Center</h1>
        <div className="accordion" id="helpAccordion">
          <div className="accordion-item">
            <h2 className="accordion-header" id="faq1">
              <button
                className="accordion-button"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapse1"
                aria-expanded="true"
                aria-controls="collapse1"
              >
                How do I post my produce?
              </button>
            </h2>
            <div
              id="collapse1"
              className="accordion-collapse collapse show"
              aria-labelledby="faq1"
              data-bs-parent="#helpAccordion"
            >
              <div className="accordion-body">
                Go to the <b>Post Produce</b> page, fill in your produce details,
                and submit your listing.
              </div>
            </div>
          </div>
          <div className="accordion-item">
            <h2 className="accordion-header" id="faq2">
              <button
                className="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapse2"
                aria-expanded="false"
                aria-controls="collapse2"
              >
                How do I contact a farmer or buyer?
              </button>
            </h2>
            <div
              id="collapse2"
              className="accordion-collapse collapse"
              aria-labelledby="faq2"
              data-bs-parent="#helpAccordion"
            >
              <div className="accordion-body">
                Use the contact details provided on each listing or use the in-app
                messaging feature (coming soon).
              </div>
            </div>
          </div>
        </div>
        <div className="alert alert-info mt-4">
          More help topics coming soon!
        </div>
      </div>
    </>
  );
}

export default Help;
