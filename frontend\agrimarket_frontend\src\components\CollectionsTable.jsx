import React, { useEffect, useState } from "react";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../api/AuthContext";

function CollectionsTable({ view }) {
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [newName, setNewName] = useState("");
  const [newDescription, setNewDescription] = useState("");
  const [creating, setCreating] = useState(false);
  const { user, hasRole } = useAuth();

  // Fetch collections
  const fetchCollections = () => {
    setLoading(true);
    setError("");
    axiosInstance
      .get("collections/")
      .then((res) => {
        setCollections(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError("Failed to load collections. Please try again later.");
        setLoading(false);
        if (err.response) {
          console.error(
            "Collections API error:",
            err.response.status,
            err.response.data
          );
        } else {
          console.error("Collections API error:", err);
        }
      });
  };

  useEffect(() => {
    fetchCollections();
  }, []);

  // Create collection
  const handleCreate = async (e) => {
    e.preventDefault();
    setCreating(true);
    setError("");
    try {
      const res = await axiosInstance.post("collections/", {
        name: newName,
        description: newDescription,
      });
      setCollections([res.data, ...collections]);
      setShowForm(false);
      setNewName("");
      setNewDescription("");
    } catch (err) {
      setError(
        err.response?.data?.error || "Failed to create collection. Please try again."
      );
    } finally {
      setCreating(false);
    }
  };

  // Delete collection
  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this collection?"))
      return;
    try {
      await axiosInstance.delete(`collections/${id}/`);
      setCollections(collections.filter((col) => col.id !== id));
    } catch {
      setError("Failed to delete collection");
    }
  };

  if (loading)
    return (
      <div className="container py-5 text-center">
        <div className="spinner-border text-success" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  if (error)
    return (
      <div className="container py-5 text-center">
        <div className="alert alert-danger mb-3">{error}</div>
        <button className="btn btn-outline-success" onClick={fetchCollections}>
          Retry
        </button>
      </div>
    );
  if (!collections.length)
    return (
      <div className="container py-5 text-center">
        <div className="alert alert-info mb-4">
          No collections found.<br />
          {user && (
            <>
              <button
                className="btn btn-success mt-3"
                onClick={() => setShowForm((v) => !v)}
              >
                {showForm ? "Cancel" : "Create Your First Collection"}
              </button>
            </>
          )}
        </div>
        {showForm && user && (
          <form
            className="card p-4 mx-auto mb-4"
            style={{ maxWidth: 420 }}
            onSubmit={handleCreate}
          >
            <h5 className="mb-3 text-success">Create Collection</h5>
            <div className="mb-3 text-start">
              <label className="form-label">Name</label>
              <input
                className="form-control"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                required
                maxLength={100}
              />
            </div>
            <div className="mb-3 text-start">
              <label className="form-label">Description</label>
              <textarea
                className="form-control"
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
                rows={3}
                maxLength={300}
              />
            </div>
            <button
              className="btn btn-success w-100"
              type="submit"
              disabled={creating}
            >
              {creating ? "Creating..." : "Create"}
            </button>
          </form>
        )}
      </div>
    );

  return (
    <div className="table-responsive mb-5">
      {user && (
        <div className="mb-4 text-end">
          <button
            className="btn btn-success"
            onClick={() => setShowForm((v) => !v)}
          >
            {showForm ? "Cancel" : "Create New Collection"}
          </button>
        </div>
      )}
      {showForm && user && (
        <form
          className="card p-4 mx-auto mb-4"
          style={{ maxWidth: 420 }}
          onSubmit={handleCreate}
        >
          <h5 className="mb-3 text-success">Create Collection</h5>
          <div className="mb-3 text-start">
            <label className="form-label">Name</label>
            <input
              className="form-control"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              required
              maxLength={100}
            />
          </div>
          <div className="mb-3 text-start">
            <label className="form-label">Description</label>
            <textarea
              className="form-control"
              value={newDescription}
              onChange={(e) => setNewDescription(e.target.value)}
              rows={3}
              maxLength={300}
            />
          </div>
          <button
            className="btn btn-success w-100"
            type="submit"
            disabled={creating}
          >
            {creating ? "Creating..." : "Create"}
          </button>
        </form>
      )}
      <table className="table table-nowrap table-bordered align-middle mb-0">
        <thead>
          <tr>
            <th className="width-4x text-nowrap">#</th>
            <th className="text-nowrap">Collection</th>
            <th className="text-end text-nowrap">Owner</th>
            <th className="text-end text-nowrap">Verified</th>
            <th className="text-end text-nowrap">Created</th>
            <th className="text-end text-nowrap">Actions</th>
          </tr>
        </thead>
        <tbody>
          {collections.map((col, idx) => (
            <tr key={col.id}>
              <td>{String(idx + 1).padStart(2, "0")}</td>
              <td>{col.name}</td>
              <td className="text-end">{col.owner && col.owner.username}</td>
              <td className="text-end">{col.verified ? "Yes" : "No"}</td>
              <td className="text-end">
                {col.created_at ? col.created_at.split("T")[0] : ""}
              </td>
              <td className="text-end">
                {(hasRole("admin") ||
                  (user && col.owner && user.username === col.owner.username)) && (
                  <button
                    className="btn btn-sm btn-danger"
                    onClick={() => handleDelete(col.id)}
                  >
                    Delete
                  </button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default CollectionsTable;
