import React, { useState } from "react";
import axiosInstance from "../api/axiosInstance";
import Navbar from "../components/Navbar";
import { useNavigate } from "react-router-dom";

const Register = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess(false);
    if (!role) {
      setError("Please select a role.");
      return;
    }
    
    // Security: Only allow farmer or buyer registration
    if (role !== "farmer" && role !== "buyer") {
      setError("Invalid role selection. Please choose either Farmer or Buyer.");
      return;
    }
    
    try {
      await axiosInstance.post("/register/", {
        username,
        password,
        email,
        is_farmer: role === "farmer",
        is_buyer: role === "buyer",
        is_staff: false, // Explicitly set to false for security
      });
      setSuccess(true);
      setTimeout(() => navigate("/login"), 1500);
    } catch (err) {
      if (err.response && err.response.data && err.response.data.error) {
        setError(err.response.data.error);
      } else if (err.response && err.response.data) {
        setError(Object.values(err.response.data).join(" "));
      } else {
        setError("Registration failed. Please check your input and try again.");
      }
    }
  };

  return (
    <>
      <Navbar />
      <div className="container page-content" style={{ maxWidth: 500 }}>
        <div className="card shadow p-4 w-100">
          <h2 className="mb-4 text-center text-primary">Join AgriMarket</h2>
          <p className="text-center text-muted mb-4">
            Connect farmers with buyers in Zimbabwe's agricultural marketplace
          </p>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Username</label>
              <input 
                type="text" 
                className="form-control" 
                value={username} 
                onChange={e => setUsername(e.target.value)} 
                required 
                placeholder="Enter your username"
              />
            </div>
            
            <div className="mb-3">
              <label className="form-label">Email</label>
              <input 
                type="email" 
                className="form-control" 
                value={email} 
                onChange={e => setEmail(e.target.value)} 
                required 
                placeholder="Enter your email address"
              />
            </div>
            
            <div className="mb-3">
              <label className="form-label">Password</label>
              <input 
                type="password" 
                className="form-control" 
                value={password} 
                onChange={e => setPassword(e.target.value)} 
                required 
                placeholder="Create a secure password"
              />
            </div>
            
            <div className="mb-3">
              <label className="form-label">I want to register as</label>
              <select 
                className="form-select" 
                value={role} 
                onChange={e => setRole(e.target.value)} 
                required
              >
                <option value="">Choose your role...</option>
                <option value="farmer">🌾 Farmer - Sell your produce</option>
                <option value="buyer">🛒 Buyer - Purchase fresh produce</option>
              </select>
              <div className="form-text">
                <small className="text-muted">
                  <strong>Farmer:</strong> Post and manage your produce listings, connect with buyers<br/>
                  <strong>Buyer:</strong> Browse and purchase fresh produce directly from farmers
                </small>
              </div>
            </div>
            
            {error && (
              <div className="alert alert-danger d-flex align-items-center">
                <span className="material-icons me-2">error</span>
                {error}
              </div>
            )}
            
            {success && (
              <div className="alert alert-success d-flex align-items-center">
                <span className="material-icons me-2">check_circle</span>
                Registration successful! Redirecting to login...
              </div>
            )}
            
            <button type="submit" className="btn btn-primary w-100 py-2">
              Create Account
            </button>
          </form>
          
          <div className="text-center mt-3">
            <p className="text-muted">
              Already have an account? 
              <a href="/login" className="text-decoration-none ms-1">Sign in here</a>
            </p>
          </div>
          
          <div className="mt-4 p-3 bg-light rounded">
            <h6 className="text-primary mb-2">Why Join AgriMarket?</h6>
            <ul className="list-unstyled small text-muted mb-0">
              <li>✓ Direct connection between farmers and buyers</li>
              <li>✓ Fair pricing and transparent marketplace</li>
              <li>✓ Support local agriculture in Zimbabwe</li>
              <li>✓ Fresh produce from farm to table</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default Register;