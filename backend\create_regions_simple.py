#!/usr/bin/env python3
"""
Simple script to create regions data for AgriMarket
Run this from the backend directory: python create_regions_simple.py
"""

import os
import sys

def main():
    """Main function"""
    print("🌍 AgriMarket Regions Setup")
    print("=" * 40)
    
    # Check if we're in the backend directory
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        sys.exit(1)
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    
    try:
        import django
        django.setup()
        print("✅ Django setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        sys.exit(1)
    
    # Import models after Django setup
    try:
        from core.models import Region
        print("✅ Models imported successfully")
    except Exception as e:
        print(f"❌ Failed to import models: {e}")
        sys.exit(1)
    
    # Create regions
    print("\n🏗️  Creating Zimbabwe regions...")
    
    # Zimbabwe provinces and major cities
    zimbabwe_data = {
        "Harare": ["<PERSON>re", "Chitungwiza", "<PERSON><PERSON><PERSON>", "<PERSON>uwa"],
        "Bulawayo": ["Bulawayo"],
        "Manicaland": ["Mutare", "Rusape", "Chipinge", "Nyanga"],
        "Mashonaland Central": ["Bindura", "Shamva", "Mount Darwin"],
        "Mashonaland East": ["Marondera", "Macheke", "Wedza"],
        "Mashonaland West": ["Chinhoyi", "Kariba", "Makonde"],
        "Masvingo": ["Masvingo", "Chiredzi", "Zaka"],
        "Matabeleland North": ["Victoria Falls", "Hwange", "Binga"],
        "Matabeleland South": ["Gwanda", "Beitbridge", "Plumtree"],
        "Midlands": ["Gweru", "Kwekwe", "Redcliff"]
    }
    
    created_provinces = 0
    created_cities = 0
    
    for province_name, cities in zimbabwe_data.items():
        # Create province
        try:
            province, created = Region.objects.get_or_create(
                name=province_name,
                type='region',
                defaults={'parent_region': None}
            )
            
            if created:
                print(f"✅ Created province: {province_name}")
                created_provinces += 1
            else:
                print(f"📋 Province exists: {province_name}")
        except Exception as e:
            print(f"❌ Failed to create province {province_name}: {e}")
            continue
        
        # Create cities
        for city_name in cities:
            try:
                city, created = Region.objects.get_or_create(
                    name=city_name,
                    type='city',
                    defaults={'parent_region': province}
                )
                
                if created:
                    created_cities += 1
            except Exception as e:
                print(f"❌ Failed to create city {city_name}: {e}")
    
    # Summary
    total_regions = Region.objects.count()
    print(f"\n📊 Summary:")
    print(f"   New provinces: {created_provinces}")
    print(f"   New cities: {created_cities}")
    print(f"   Total regions: {total_regions}")
    
    if total_regions > 0:
        print("\n✅ Regions setup successful!")
        print("\n🧪 Test the API:")
        print("   http://127.0.0.1:8000/api/regions/")
        print("\n📋 Sample regions:")
        
        # Show sample regions
        sample_regions = Region.objects.all()[:5]
        for region in sample_regions:
            parent = f" (in {region.parent_region.name})" if region.parent_region else ""
            print(f"   - {region.name} ({region.type}){parent}")
        
        print("\n🎉 Success! Now restart your Django server:")
        print("   python manage.py runserver 127.0.0.1:8000")
    else:
        print("❌ No regions were created!")
        sys.exit(1)

if __name__ == "__main__":
    main()
