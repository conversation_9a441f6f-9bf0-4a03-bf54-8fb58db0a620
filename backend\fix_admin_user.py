#!/usr/bin/env python3
"""
Fix admin user to ensure proper admin dashboard access
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()

def fix_admin_user():
    """Fix admin user to have proper admin privileges"""
    from core.models import User
    
    print("🔧 Fixing Admin User for Dashboard Access")
    print("=" * 50)
    
    # Check if admin user exists
    try:
        admin_user = User.objects.get(username='admin')
        print(f"✅ Found admin user: {admin_user.username}")
    except User.DoesNotExist:
        print("❌ Admin user not found. Creating new admin user...")
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("✅ Created new admin user")
    
    # Check current admin status
    print(f"\n📋 Current admin user status:")
    print(f"   Username: {admin_user.username}")
    print(f"   Email: {admin_user.email}")
    print(f"   is_staff: {admin_user.is_staff}")
    print(f"   is_superuser: {admin_user.is_superuser}")
    print(f"   is_active: {admin_user.is_active}")
    print(f"   is_farmer: {admin_user.is_farmer}")
    print(f"   is_buyer: {admin_user.is_buyer}")
    
    # Fix admin privileges
    needs_update = False
    
    if not admin_user.is_staff:
        admin_user.is_staff = True
        needs_update = True
        print("🔧 Setting is_staff = True")
    
    if not admin_user.is_superuser:
        admin_user.is_superuser = True
        needs_update = True
        print("🔧 Setting is_superuser = True")
    
    if not admin_user.is_active:
        admin_user.is_active = True
        needs_update = True
        print("🔧 Setting is_active = True")
    
    # Make sure admin is not farmer or buyer
    if admin_user.is_farmer:
        admin_user.is_farmer = False
        needs_update = True
        print("🔧 Setting is_farmer = False")
    
    if admin_user.is_buyer:
        admin_user.is_buyer = False
        needs_update = True
        print("🔧 Setting is_buyer = False")
    
    if needs_update:
        admin_user.save()
        print("✅ Admin user updated successfully!")
    else:
        print("✅ Admin user already has correct privileges")
    
    # Final status check
    print(f"\n📋 Updated admin user status:")
    print(f"   Username: {admin_user.username}")
    print(f"   Email: {admin_user.email}")
    print(f"   is_staff: {admin_user.is_staff}")
    print(f"   is_superuser: {admin_user.is_superuser}")
    print(f"   is_active: {admin_user.is_active}")
    print(f"   is_farmer: {admin_user.is_farmer}")
    print(f"   is_buyer: {admin_user.is_buyer}")
    
    return admin_user

def test_admin_access():
    """Test if admin user can access admin functions"""
    from core.models import User
    
    print(f"\n🧪 Testing Admin Access")
    print("=" * 30)
    
    try:
        admin_user = User.objects.get(username='admin')
        
        # Test hasRole logic
        is_staff_check = bool(admin_user.is_staff)
        print(f"hasRole('admin') check: {is_staff_check}")
        
        if is_staff_check:
            print("✅ Admin user should see dashboard link")
        else:
            print("❌ Admin user will NOT see dashboard link")
            return False
        
        # Test API access
        print(f"API access checks:")
        print(f"   is_staff (for admin APIs): {admin_user.is_staff}")
        print(f"   is_superuser (for superuser APIs): {admin_user.is_superuser}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing admin access: {e}")
        return False

def main():
    """Main function"""
    print("🔧 AgriMarket Admin User Fix")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        print("   cd backend")
        print("   python fix_admin_user.py")
        sys.exit(1)
    
    # Setup Django
    setup_django()
    
    # Fix admin user
    admin_user = fix_admin_user()
    
    # Test admin access
    if test_admin_access():
        print("\n🎉 Admin user fix complete!")
        print("\n📋 Next steps:")
        print("1. Restart your Django server:")
        print("   python manage.py runserver 127.0.0.1:8000")
        print("\n2. In frontend, logout and login again:")
        print("   - Go to: http://127.0.0.1:5173/login")
        print("   - Login with: admin / admin123")
        print("   - Look for 'Admin Dashboard' in navbar")
        print("\n3. Check browser console for user data:")
        print("   - Open browser dev tools")
        print("   - Check if user.is_staff is true")
        
        print(f"\n✅ Admin credentials:")
        print(f"   Username: admin")
        print(f"   Password: admin123")
        print(f"   Dashboard URL: http://127.0.0.1:5173/comprehensive-admin")
    else:
        print("\n❌ Admin user fix failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
