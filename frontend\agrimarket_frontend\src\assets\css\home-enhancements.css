/* ===== HOME PAGE ENHANCEMENTS ===== */

/* Enhanced Hero Section */
.hero-section {
  position: relative;
  overflow: hidden;
  background: url('/src/assets/img/heroSectionBg.jpg') center/cover no-repeat !important;
  min-height: 75vh !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(46, 125, 50, 0.85) 0%,
    rgba(67, 160, 71, 0.5) 40%,
    rgba(102, 187, 106, 0.15) 75%,
    rgba(255,255,255,0) 100%
  );
  z-index: 1;
  pointer-events: none;
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.hero-section h1 {
  font-size: clamp(2.5rem, 5vw, 4rem) !important;
  font-weight: 800 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
  margin-bottom: 1.5rem !important;
  line-height: 1.2 !important;
}

.hero-section .lead {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem) !important;
  font-weight: 500 !important;
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 16px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
}

/* Hero Feature Cards */
.hero-section .bg-dark {
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20px !important;
  padding: 1.5rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-width: 200px;
  text-align: center;
}

.hero-section .bg-dark:hover {
  transform: translateY(-8px) scale(1.05);
  background: rgba(255, 255, 255, 0.25) !important;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.3) !important;
}

.hero-section .material-icons {
  display: block !important;
  margin: 0 auto 0.5rem auto !important;
  font-size: 3rem !important;
  color: #66BB6A !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Enhanced Buttons */
.hero-section .btn {
  border-radius: 50px !important;
  padding: 1rem 2rem !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 2px solid transparent !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

.hero-section .btn:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

.hero-section .btn-warning {
  color: #43A047 !important; /* AgriMarket green */
  background: linear-gradient(135deg, #F9A825, #FFB74D) !important;
  border-color: #F9A825 !important;
}

.hero-section .btn-warning:hover,
.hero-section .btn-warning:focus {
  color: #fff !important;
}

.hero-section .btn-outline-light {
  background: transparent !important;
  color: #fff !important;
  border: 2px solid #fff !important;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(0,0,0,0.18) !important;
}

.hero-section .btn-outline-light .material-icons {
  color: #fff !important;
}

.hero-section .btn-outline-light:hover,
.hero-section .btn-outline-light:focus {
  background: rgba(255,255,255,0.12) !important;
  color: #fff !important;
  border: 2px solid #fff !important;
}

.hero-section .btn-success {
  background: linear-gradient(135deg, #43A047, #66BB6A) !important;
  border-color: #43A047 !important;
}

/* Make Browse Market button's icon bright for visibility */
.btn-success.glassy-btn .material-icons {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.12));
  opacity: 0.97;
}

.hero-section .btn-success.text-white.btn-lg .material-icons {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.12));
  opacity: 1 !important;
}

/* Stats Section Enhancement */
.stats-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 24px;
  padding: 3rem 2rem;
  margin: 2rem auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.stats-section .col-md-3 {
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-section .col-md-3:hover {
  transform: translateY(-8px);
  background: rgba(67, 160, 71, 0.1);
  box-shadow: 0 12px 24px rgba(67, 160, 71, 0.15);
}

.stats-section .display-4 {
  font-weight: 800 !important;
  background: linear-gradient(135deg, #2E7D32, #43a048b6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
}

/* Feature Highlights Section */
.feature-section {
  padding: 4rem 0;
}

.feature-section img {
  border-radius: 24px !important;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.feature-section img:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 24px 50px rgba(0, 0, 0, 0.2) !important;
}

.feature-section h2 {
  font-size: clamp(2rem, 4vw, 2.5rem) !important;
  font-weight: 700 !important;
  margin-bottom: 2rem !important;
  background: linear-gradient(135deg, #2E7D32, #43A047);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.feature-section .list-unstyled li {
  padding: 0.75rem 0 !important;
  font-size: 1.1rem !important;
  font-weight: 500 !important;
  border-bottom: 1px solid rgba(67, 160, 71, 0.1);
  transition: all 0.3s ease;
}

.feature-section .list-unstyled li:hover {
  padding-left: 1rem;
  background: rgba(67, 160, 71, 0.05);
  border-radius: 8px;
}

.feature-section .material-icons {
  background: linear-gradient(135deg, #43A047, #66BB6A);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 1.5rem !important;
  margin-right: 1rem !important;
}

/* Section Spacing and Layout */
main section {
  margin-bottom: 4rem;
}

main section:last-child {
  margin-bottom: 0;
}

/* Enhanced Collection and Produce Sections */
.collection-item,
.produce-card .card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.6) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden;
}

.collection-item:hover,
.produce-card .card:hover {
  transform: translateY(-12px) scale(1.02) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(67, 160, 71, 0.3) !important;
}

/* Enhanced Images */
.collection-item img,
.produce-card img {
  border-radius: 16px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.collection-item:hover img,
.produce-card:hover img {
  transform: scale(1.05);
}

/* Enhanced Typography */
.display-6 {
  font-weight: 700 !important;
  background: linear-gradient(135deg, #2E7D32, #43A047);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Section Background Enhancements */
.popular-produce-section {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 32px;
  margin: 2rem 0;
  padding: 2rem 0;
}

.top-collections-section {
  background: linear-gradient(135deg, rgba(241, 245, 249, 0.6) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 32px;
  margin: 2rem 0;
  padding: 2rem 0;
}

/* Enhanced Buttons Global */
.btn {
  border-radius: 12px !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.btn-success {
  background: linear-gradient(135deg, #43A047, #66BB6A) !important;
  border: none !important;
}

.btn-outline-success {
  border: 2px solid #43A047 !important;
  color: #43A047 !important;
  background: transparent !important;
}

.btn-outline-success:hover {
  background: linear-gradient(135deg, #43A047, #66BB6A) !important;
  border-color: #43A047 !important;
  color: white !important;
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 60vh !important;
    padding: 2rem 0 !important;
  }
  
  .hero-section .bg-dark {
    min-width: 150px;
    padding: 1rem !important;
    margin: 0.5rem;
  }
  
  .hero-section .material-icons {
    font-size: 2rem !important;
  }
  
  .stats-section {
    padding: 2rem 1rem;
    margin: 1rem auto;
  }
  
  .feature-section {
    padding: 2rem 0;
  }
  
  .popular-produce-section,
  .top-collections-section {
    margin: 1rem 0;
    padding: 1.5rem 0;
    border-radius: 20px;
  }
}

@media (max-width: 576px) {
  .hero-section h1 {
    font-size: 2rem !important;
  }
  
  .hero-section .lead {
    font-size: 1rem !important;
    padding: 1.5rem !important;
  }
  
  .hero-section .btn {
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    margin: 0.25rem;
  }
  
  .hero-section .bg-dark {
    min-width: 120px;
    padding: 0.75rem !important;
  }
  
  .popular-produce-section,
  .top-collections-section {
    border-radius: 16px;
  }
}

/* Loading and Transition States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
  box-shadow: 0 0 0 4px rgba(67, 160, 71, 0.25) !important;
  border-color: #43A047 !important;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .hero-section .bg-dark {
    border: 2px solid white !important;
  }
  
  .collection-item,
  .produce-card .card {
    border: 2px solid #43A047 !important;
  }
}

/* Navbar Link Hover Effect */
.navbar .nav-link:hover, .navbar .nav-link:focus {
  color: #F9A825 !important;
  transition: color 0.2s;
}
.navbar .nav-link:hover .material-icons, .navbar .nav-link:focus .material-icons {
  color: #F9A825 !important;
  transition: color 0.2s;
}