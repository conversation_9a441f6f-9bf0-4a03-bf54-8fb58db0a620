import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../api/AuthContext";

function SuperuserAdminManagement() {
  const [users, setUsers] = useState([]);
  const [adminUsers, setAdminUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const { user, hasRole } = useAuth();

  // Only show this component to superusers
  if (!user || !user.is_superuser) {
    return (
      <div className="alert alert-warning">
        <h5>Access Denied</h5>
        <p>Only superusers can manage admin roles.</p>
      </div>
    );
  }

  useEffect(() => {
    fetchUsers();
    fetchAdminUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await axiosInstance.get("/users/");
      // Filter out superusers from the list
      const regularUsers = response.data.filter(u => !u.is_superuser);
      setUsers(regularUsers);
    } catch (err) {
      setError("Failed to fetch users");
    }
  };

  const fetchAdminUsers = async () => {
    try {
      const response = await axiosInstance.get("/users/admin_users/");
      setAdminUsers(response.data.admin_users || []);
      setLoading(false);
    } catch (err) {
      setError("Failed to fetch admin users");
      setLoading(false);
    }
  };

  const assignAdminRole = async (userId, username) => {
    try {
      setError("");
      setSuccess("");
      const response = await axiosInstance.post(`/users/${userId}/assign_admin_role/`);
      setSuccess(response.data.message);
      fetchUsers();
      fetchAdminUsers();
    } catch (err) {
      setError(err.response?.data?.error || "Failed to assign admin role");
    }
  };

  const revokeAdminRole = async (userId, username) => {
    try {
      setError("");
      setSuccess("");
      const response = await axiosInstance.post(`/users/${userId}/revoke_admin_role/`);
      setSuccess(response.data.message);
      fetchUsers();
      fetchAdminUsers();
    } catch (err) {
      setError(err.response?.data?.error || "Failed to revoke admin role");
    }
  };

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="row">
        <div className="col-12">
          <h2 className="mb-4">
            <span className="material-icons me-2">admin_panel_settings</span>
            Admin Role Management
          </h2>
          <p className="text-muted mb-4">
            As a superuser, you can assign or revoke admin roles for users. 
            Admin users can access the admin dashboard and moderate content.
          </p>

          {error && (
            <div className="alert alert-danger d-flex align-items-center">
              <span className="material-icons me-2">error</span>
              {error}
            </div>
          )}

          {success && (
            <div className="alert alert-success d-flex align-items-center">
              <span className="material-icons me-2">check_circle</span>
              {success}
            </div>
          )}

          {/* Current Admin Users */}
          <div className="card mb-4">
            <div className="card-header bg-primary text-white">
              <h5 className="mb-0">
                <span className="material-icons me-2">verified_user</span>
                Current Admin Users ({adminUsers.length})
              </h5>
            </div>
            <div className="card-body">
              {adminUsers.length === 0 ? (
                <p className="text-muted">No admin users assigned yet.</p>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead>
                      <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {adminUsers.map(adminUser => (
                        <tr key={adminUser.id}>
                          <td>
                            <strong>{adminUser.username}</strong>
                          </td>
                          <td>{adminUser.email}</td>
                          <td>
                            <span className="badge bg-primary">Admin</span>
                            {adminUser.is_farmer && <span className="badge bg-success ms-1">Farmer</span>}
                            {adminUser.is_buyer && <span className="badge bg-info ms-1">Buyer</span>}
                          </td>
                          <td>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => revokeAdminRole(adminUser.id, adminUser.username)}
                            >
                              <span className="material-icons me-1">remove_circle</span>
                              Revoke Admin
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>

          {/* All Users */}
          <div className="card">
            <div className="card-header bg-secondary text-white">
              <h5 className="mb-0">
                <span className="material-icons me-2">people</span>
                All Users
              </h5>
            </div>
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead>
                    <tr>
                      <th>Username</th>
                      <th>Email</th>
                      <th>Current Role</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map(regularUser => (
                      <tr key={regularUser.id}>
                        <td>
                          <strong>{regularUser.username}</strong>
                        </td>
                        <td>{regularUser.email}</td>
                        <td>
                          {regularUser.is_staff && <span className="badge bg-primary me-1">Admin</span>}
                          {regularUser.is_farmer && <span className="badge bg-success me-1">Farmer</span>}
                          {regularUser.is_buyer && <span className="badge bg-info me-1">Buyer</span>}
                          {!regularUser.is_staff && !regularUser.is_farmer && !regularUser.is_buyer && (
                            <span className="badge bg-secondary">No Role</span>
                          )}
                        </td>
                        <td>
                          {regularUser.is_staff ? (
                            <button
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => revokeAdminRole(regularUser.id, regularUser.username)}
                            >
                              <span className="material-icons me-1">remove_circle</span>
                              Revoke Admin
                            </button>
                          ) : (
                            <button
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => assignAdminRole(regularUser.id, regularUser.username)}
                            >
                              <span className="material-icons me-1">add_circle</span>
                              Make Admin
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-light rounded">
            <h6 className="text-primary mb-2">
              <span className="material-icons me-2">info</span>
              Important Notes
            </h6>
            <ul className="list-unstyled small text-muted mb-0">
              <li>✓ Only superusers can assign or revoke admin roles</li>
              <li>✓ Admin users can access the admin dashboard and moderate content</li>
              <li>✓ Superuser roles cannot be modified through this interface</li>
              <li>✓ Users can have multiple roles (e.g., Admin + Farmer)</li>
              <li>✓ Changes take effect immediately</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SuperuserAdminManagement;