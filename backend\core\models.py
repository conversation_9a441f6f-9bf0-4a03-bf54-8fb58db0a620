from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone

class User(AbstractUser):
    is_farmer = models.BooleanField(default=False)
    is_buyer = models.BooleanField(default=False)
    is_logistics_partner = models.BooleanField(default=False)
    phone = models.CharField(max_length=20, blank=True)
    location = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(is_farmer=True) | models.Q(is_buyer=True) | models.Q(is_staff=True),
                name='user_must_have_role'
            )
        ]

    def clean(self):
        from django.core.exceptions import ValidationError
        if not self.is_farmer and not self.is_buyer and not self.is_staff:
            raise ValidationError("User must have at least one role (farmer, buyer, or staff).")

    def __str__(self):
        return self.username

class Produce(models.Model):
    UNIT_CHOICES = [
        ('kg', 'Kilogram'),
        ('ton', 'Ton'),
        ('bag', 'Bag'),
        ('box', 'Box'),
        ('piece', 'Piece'),
    ]
    
    farmer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='produce')
    category = models.ForeignKey('ProduceCategory', on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.CharField(max_length=10, choices=UNIT_CHOICES, default='kg')
    quantity_available = models.PositiveIntegerField(default=1)
    photo = models.ImageField(upload_to='produce_photos/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved = models.BooleanField(default=False)
    is_organic = models.BooleanField(default=False)
    harvest_date = models.DateField(null=True, blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)
    
    def __str__(self):
        return f"{self.name} by {self.farmer.username}"

    class Meta:
        ordering = ['-created_at']
        constraints = [
            models.CheckConstraint(
                check=models.Q(price__gt=0),
                name='produce_price_positive'
            ),
            models.CheckConstraint(
                check=models.Q(quantity_available__gte=0),
                name='produce_quantity_non_negative'
            )
        ]

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.price <= 0:
            raise ValidationError("Price must be greater than 0.")
        if self.quantity_available < 0:
            raise ValidationError("Quantity available cannot be negative.")

class MarketPrice(models.Model):
    crop_name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.CharField(max_length=20, default='kg')  # Add this line
    source = models.CharField(max_length=100)
    date = models.DateField()
    approved = models.BooleanField(default=False)  # Admin-verified

    def __str__(self):
        return f"{self.crop_name} - ${self.price} ({self.date})"

    class Meta:
        ordering = ['-date']
        unique_together = ('crop_name', 'source', 'date')

class SMSAlert(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    sent_at = models.DateTimeField(auto_now_add=True)
    delivered = models.BooleanField(default=False)

    def __str__(self):
        return f"SMS to {self.user.username} - {self.sent_at.strftime('%Y-%m-%d %H:%M')}"

    class Meta:
        ordering = ['-sent_at']

class ContactRequest(models.Model):
    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='contact_requests')
    farmer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_contact_requests')
    produce = models.ForeignKey(Produce, on_delete=models.CASCADE, related_name='contact_requests')
    message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, default='pending')  # pending, accepted, rejected

    def __str__(self):
        return f"Contact from {self.buyer.username} to {self.farmer.username} about {self.produce.name}"

    class Meta:
        ordering = ['-created_at']

class PriceSource(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    contact = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class ProduceCategory(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=100, blank=True)  # Optional: icon name or path

    def __str__(self):
        return self.name

class Collection(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='collection_images/', blank=True, null=True)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='collections')
    produce = models.ManyToManyField('Produce', related_name='collections', blank=True)
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class Favorite(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    produce = models.ForeignKey('Produce', on_delete=models.CASCADE, null=True, blank=True)
    collection = models.ForeignKey('Collection', on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        # Ensure user can't favorite the same item twice
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'produce'],
                condition=models.Q(produce__isnull=False),
                name='unique_user_produce_favorite'
            ),
            models.UniqueConstraint(
                fields=['user', 'collection'],
                condition=models.Q(collection__isnull=False),
                name='unique_user_collection_favorite'
            ),
            models.CheckConstraint(
                check=models.Q(produce__isnull=False) | models.Q(collection__isnull=False),
                name='favorite_must_have_produce_or_collection'
            )
        ]

    def clean(self):
        from django.core.exceptions import ValidationError
        if not self.produce and not self.collection:
            raise ValidationError("Favorite must have either a produce or collection.")
        if self.produce and self.collection:
            raise ValidationError("Favorite cannot have both produce and collection.")

class Watchlist(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='watchlist')
    produce = models.ForeignKey('Produce', on_delete=models.CASCADE, null=True, blank=True)
    collection = models.ForeignKey('Collection', on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        # Ensure user can't watchlist the same item twice
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'produce'],
                condition=models.Q(produce__isnull=False),
                name='unique_user_produce_watchlist'
            ),
            models.UniqueConstraint(
                fields=['user', 'collection'],
                condition=models.Q(collection__isnull=False),
                name='unique_user_collection_watchlist'
            ),
            models.CheckConstraint(
                check=models.Q(produce__isnull=False) | models.Q(collection__isnull=False),
                name='watchlist_must_have_produce_or_collection'
            )
        ]

    def clean(self):
        from django.core.exceptions import ValidationError
        if not self.produce and not self.collection:
            raise ValidationError("Watchlist item must have either a produce or collection.")
        if self.produce and self.collection:
            raise ValidationError("Watchlist item cannot have both produce and collection.")

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    location = models.CharField(max_length=100, blank=True)
    # Add more fields as needed

    def __str__(self):
        return f"Profile for {self.user.username}"

class Region(models.Model):
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=20, choices=[('region', 'Region'), ('city', 'City'), ('town', 'Town')])
    parent_region = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='sub_regions')

    def __str__(self):
        return f"{self.name} ({self.type})"

    class Meta:
        unique_together = ('name', 'type')
        ordering = ['name']

class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('logistics_pending', 'Pending Logistics'),
        ('logistics_assigned', 'Logistics Assigned'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='buyer_orders')
    farmer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='farmer_orders')
    produce = models.ForeignKey(Produce, on_delete=models.CASCADE, related_name='orders')
    quantity = models.PositiveIntegerField(default=1)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    order_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    updated_at = models.DateTimeField(auto_now=True)
    delivery_location = models.CharField(max_length=200, blank=True)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"Order {self.id} by {self.buyer.username} on {self.order_date.strftime('%Y-%m-%d')}"

    class Meta:
        ordering = ['-order_date']

class LogisticsPartner(models.Model):
    # Link to User account for authentication
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='logistics_profile', null=True, blank=True)

    # Business information
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    contact_phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    service_regions = models.ManyToManyField(Region, related_name='logistics_partners')
    base_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    registration_date = models.DateTimeField(auto_now_add=True)
    is_verified = models.BooleanField(default=False)
    website = models.URLField(blank=True)

    # Additional logistics-specific fields
    vehicle_types = models.CharField(max_length=500, blank=True, help_text="Comma-separated list of vehicle types")
    capacity = models.CharField(max_length=100, blank=True, help_text="e.g., '5 tons', '1000 kg'")
    business_license = models.CharField(max_length=100, blank=True)
    insurance_details = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    def average_rating(self):
        ratings = self.reviews.all()
        if ratings:
            return sum(review.rating for review in ratings) / ratings.count()
        return 0.0

    class Meta:
        ordering = ['name']

class OrderLogistics(models.Model):
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='logistics')
    logistics_partner = models.ForeignKey(LogisticsPartner, on_delete=models.SET_NULL, null=True, related_name='assigned_orders')
    selection_date = models.DateTimeField(auto_now_add=True)
    estimated_delivery = models.DateField(null=True, blank=True)
    tracking_number = models.CharField(max_length=100, blank=True)
    cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Logistics for Order {self.order.id} with {self.logistics_partner.name if self.logistics_partner else 'None'}"

class LogisticsPartnerReview(models.Model):
    logistics_partner = models.ForeignKey(LogisticsPartner, on_delete=models.CASCADE, related_name='reviews')
    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='logistics_reviews')
    rating = models.PositiveSmallIntegerField(choices=[(i, str(i)) for i in range(1, 6)], default=3)
    review_text = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Review for {self.logistics_partner.name} by {self.buyer.username} ({self.rating}/5)"

    class Meta:
        unique_together = ('logistics_partner', 'buyer')
        ordering = ['-created_at']

# Direct Messaging System
class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('buyer_farmer', 'Buyer-Farmer'),
        ('buyer_logistics', 'Buyer-Logistics'),
        ('farmer_logistics', 'Farmer-Logistics'),
        ('group', 'Group Chat'),
    ]

    type = models.CharField(max_length=20, choices=CONVERSATION_TYPES, default='buyer_farmer')
    participants = models.ManyToManyField(User, related_name='conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Optional: Link to specific order or produce for context
    related_order = models.ForeignKey(Order, on_delete=models.CASCADE, null=True, blank=True, related_name='conversations')
    related_produce = models.ForeignKey(Produce, on_delete=models.CASCADE, null=True, blank=True, related_name='conversations')

    # Conversation metadata
    title = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        participant_names = ", ".join([user.username for user in self.participants.all()[:3]])
        return f"{self.get_type_display()}: {participant_names}"

    def get_last_message(self):
        return self.messages.order_by('-created_at').first()

    def get_unread_count(self, user):
        return self.messages.filter(read_by__user=user).exclude(sender=user).count()

    class Meta:
        ordering = ['-updated_at']

class Message(models.Model):
    MESSAGE_TYPES = [
        ('text', 'Text'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System'),
    ]

    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')

    # Message content
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField()

    # File attachments
    attachment = models.FileField(upload_to='message_attachments/', null=True, blank=True)

    # Message metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_edited = models.BooleanField(default=False)

    # Reply functionality
    reply_to = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')

    def __str__(self):
        return f"{self.sender.username}: {self.content[:50]}..."

    class Meta:
        ordering = ['created_at']

class MessageRead(models.Model):
    """Track which users have read which messages"""
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='read_by')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='read_messages')
    read_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('message', 'user')

class MessageNotification(models.Model):
    """Notifications for new messages"""
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_notifications')
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='notifications')
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Notification for {self.recipient.username}: {self.message.content[:30]}..."

    class Meta:
        ordering = ['-created_at']
