#!/usr/bin/env python3
"""
AgriMarket Setup Script
This script helps set up the AgriMarket project for development.
"""

import os
import sys
import subprocess
import platform

def run_command(command, cwd=None, show_output=True):
    """Run a command and return its success status."""
    try:
        print(f"Running: {command}")
        if show_output:
            result = subprocess.run(command, shell=True, cwd=cwd, text=True)
            return result.returncode == 0
        else:
            result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"Error running command: {command}")
                print(f"Error output: {result.stderr}")
                return False
            return True
    except Exception as e:
        print(f"Exception running command {command}: {e}")
        return False

def check_requirements():
    """Check if required tools are installed."""
    print("Checking requirements...")
    
    # Check Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("Error: Python 3.8+ is required")
            return False
        print(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except:
        print("Error: Could not determine Python version")
        return False
    
    # Check pip
    if not run_command("pip --version", show_output=False):
        print("Error: pip is not installed or not in PATH")
        return False
    print("✓ pip is available")
    
    # Check Node.js
    if not run_command("node --version", show_output=False):
        print("Warning: Node.js is not installed. Frontend setup will be skipped.")
        return "no-node"
    print("✓ Node.js is available")
    
    # Check npm
    if not run_command("npm --version", show_output=False):
        print("Warning: npm is not installed. Frontend setup will be skipped.")
        return "no-npm"
    print("✓ npm is available")
    
    return True

def setup_backend():
    """Set up the Django backend."""
    print("\n" + "="*50)
    print("Setting up Django backend...")
    print("="*50)
    
    backend_dir = os.path.join(os.getcwd(), 'backend')
    
    if not os.path.exists(backend_dir):
        print(f"Error: Backend directory not found at {backend_dir}")
        return False
    
    # Check if requirements.txt exists
    requirements_file = os.path.join(backend_dir, 'requirements.txt')
    if not os.path.exists(requirements_file):
        print(f"Error: requirements.txt not found at {requirements_file}")
        return False
    
    # Install Python dependencies
    print("\nInstalling Python dependencies...")
    if not run_command("pip install -r requirements.txt", cwd=backend_dir):
        print("Failed to install Python dependencies")
        print("You may need to activate your virtual environment first:")
        print("  Windows: myenv\\Scripts\\activate")
        print("  Linux/Mac: source myenv/bin/activate")
        return False
    
    # Run migrations
    print("\nCreating database migrations...")
    if not run_command("python manage.py makemigrations", cwd=backend_dir):
        print("Warning: Failed to create migrations (this might be normal if no changes)")
    
    print("\nRunning database migrations...")
    if not run_command("python manage.py migrate", cwd=backend_dir):
        print("Failed to run migrations")
        return False
    
    print("\n✓ Backend setup completed successfully!")
    print("\nOptional: Create a superuser account")
    print("Run: cd backend && python manage.py createsuperuser")
    
    return True

def setup_frontend():
    """Set up the React frontend."""
    print("\n" + "="*50)
    print("Setting up React frontend...")
    print("="*50)
    
    frontend_dir = os.path.join(os.getcwd(), 'frontend', 'agrimarket_frontend')
    
    if not os.path.exists(frontend_dir):
        print(f"Error: Frontend directory not found at {frontend_dir}")
        return False
    
    # Check if package.json exists
    package_json = os.path.join(frontend_dir, 'package.json')
    if not os.path.exists(package_json):
        print(f"Error: package.json not found at {package_json}")
        return False
    
    # Install Node.js dependencies
    print("\nInstalling Node.js dependencies...")
    if not run_command("npm install", cwd=frontend_dir):
        print("Failed to install Node.js dependencies")
        return False
    
    print("\n✓ Frontend setup completed successfully!")
    return True

def main():
    """Main setup function."""
    print("🌾 Welcome to AgriMarket Setup! 🌾")
    print("This script will help you set up the development environment.")
    print()
    
    # Check if we're in the right directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")
    
    if not os.path.exists('backend') or not os.path.exists('frontend'):
        print("\nError: Please run this script from the AgriMarket root directory.")
        print("Expected structure:")
        print("  AgriMarket/")
        print("  ├── backend/")
        print("  ├── frontend/")
        print("  └── setup.py")
        sys.exit(1)
    
    # Check requirements
    req_check = check_requirements()
    if req_check == False:
        print("\nPlease install the required tools and try again.")
        sys.exit(1)
    
    # Setup backend
    print("\nStarting backend setup...")
    if not setup_backend():
        print("\n❌ Backend setup failed!")
        print("\nTroubleshooting tips:")
        print("1. Make sure you're in a Python virtual environment")
        print("2. Check that Python 3.8+ is installed")
        print("3. Ensure pip is working correctly")
        sys.exit(1)
    
    # Setup frontend (if Node.js is available)
    if req_check not in ["no-node", "no-npm"]:
        print("\nStarting frontend setup...")
        if not setup_frontend():
            print("\n❌ Frontend setup failed!")
            print("\nTroubleshooting tips:")
            print("1. Make sure Node.js 16+ is installed")
            print("2. Check that npm is working correctly")
            print("3. Try running 'npm cache clean --force'")
            sys.exit(1)
    else:
        print("\n⚠️  Skipping frontend setup (Node.js/npm not available)")
    
    print("\n" + "="*60)
    print("🎉 Setup completed successfully!")
    print("="*60)
    print("\nTo start the development servers:")
    print("\n📱 Backend (Django):")
    print("  cd backend")
    print("  python manage.py runserver")
    print("  → Available at: http://127.0.0.1:8000")
    print("  → Admin panel: http://127.0.0.1:8000/admin")
    print("  → API docs: http://127.0.0.1:8000/api")
    
    if req_check not in ["no-node", "no-npm"]:
        print("\n🌐 Frontend (React):")
        print("  cd frontend/agrimarket_frontend")
        print("  npm run dev")
        print("  → Available at: http://127.0.0.1:5173")
    
    print("\n📚 Additional commands:")
    print("  Create superuser: cd backend && python manage.py createsuperuser")
    print("  View logs: Check terminal output when running servers")
    print("\n🚀 Happy coding!")

if __name__ == "__main__":
    main()