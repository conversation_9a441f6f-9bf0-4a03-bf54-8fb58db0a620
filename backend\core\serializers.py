from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Produce, ProduceCategory, MarketPrice, SMSAlert, ContactRequest, PriceSource, Collection, Favorite, Watchlist, UserProfile, Region, Order, LogisticsPartner, OrderLogistics, LogisticsPartnerReview, Conversation, Message, MessageRead, MessageNotification

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_farmer', 'is_buyer', 'is_logistics_partner', 'is_staff', 'is_superuser', 'is_active', 'phone', 'location', 'created_at', 'updated_at']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 'last_name', 'is_farmer', 'is_buyer', 'phone', 'location']
        extra_kwargs = {
            'email': {'required': True},
            'username': {'required': True},
        }

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")

        # Ensure user selects exactly one role
        is_farmer = attrs.get('is_farmer', False)
        is_buyer = attrs.get('is_buyer', False)

        if not is_farmer and not is_buyer:
            raise serializers.ValidationError("Please select either farmer or buyer role")

        if is_farmer and is_buyer:
            raise serializers.ValidationError("Please select only one role")

        return attrs

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')

        # Security: Ensure these are always False for public registration
        validated_data['is_staff'] = False
        validated_data['is_superuser'] = False

        user = User.objects.create_user(password=password, **validated_data)
        return user

class UserLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    role = serializers.CharField(required=False)

class ProduceCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProduceCategory
        fields = ['id', 'name', 'description', 'icon']

class ProduceSerializer(serializers.ModelSerializer):
    farmer = UserSerializer(read_only=True)
    category = ProduceCategorySerializer(read_only=True)

    class Meta:
        model = Produce
        fields = ['id', 'farmer', 'category', 'name', 'description', 'price', 'unit', 'quantity_available', 'photo', 'created_at', 'updated_at', 'approved', 'is_organic', 'harvest_date', 'contact_phone']
        read_only_fields = ['created_at', 'updated_at', 'approved']

class MarketPriceSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketPrice
        fields = ['id', 'crop_name', 'price', 'source', 'date', 'approved']

class SMSAlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSAlert
        fields = ['id', 'user', 'message', 'sent_at', 'delivered']
        read_only_fields = ['sent_at', 'delivered']

class ContactRequestSerializer(serializers.ModelSerializer):
    buyer = UserSerializer(read_only=True)
    farmer = UserSerializer(read_only=True)
    produce = ProduceSerializer(read_only=True)

    class Meta:
        model = ContactRequest
        fields = ['id', 'buyer', 'farmer', 'produce', 'message', 'created_at', 'status']
        read_only_fields = ['created_at']

class PriceSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = PriceSource
        fields = ['id', 'name', 'description', 'contact']

class CollectionSerializer(serializers.ModelSerializer):
    owner = UserSerializer(read_only=True)
    produce = ProduceSerializer(many=True, read_only=True)

    class Meta:
        model = Collection
        fields = ['id', 'name', 'description', 'image', 'owner', 'produce', 'verified', 'created_at']
        read_only_fields = ['created_at', 'verified']

class FavoriteSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    produce = ProduceSerializer(read_only=True)
    collection = CollectionSerializer(read_only=True)

    class Meta:
        model = Favorite
        fields = ['id', 'user', 'produce', 'collection', 'created_at']
        read_only_fields = ['created_at']

class WatchlistSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    produce = ProduceSerializer(read_only=True)
    collection = CollectionSerializer(read_only=True)

    class Meta:
        model = Watchlist
        fields = ['id', 'user', 'produce', 'collection', 'created_at']
        read_only_fields = ['created_at']

class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = ['id', 'user', 'bio', 'avatar', 'location']

class RegionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Region
        fields = ['id', 'name', 'type', 'parent_region']
        read_only_fields = ['id', 'parent_region']

class OrderSerializer(serializers.ModelSerializer):
    buyer = UserSerializer(read_only=True)
    farmer = UserSerializer(read_only=True)
    produce = ProduceSerializer(read_only=True)
    region = RegionSerializer(read_only=True)

    class Meta:
        model = Order
        fields = ['id', 'buyer', 'farmer', 'produce', 'quantity', 'total_price', 'order_date', 'status', 'updated_at', 'delivery_location', 'region']
        read_only_fields = ['id', 'buyer', 'farmer', 'order_date', 'status', 'updated_at']

class LogisticsPartnerReviewSerializer(serializers.ModelSerializer):
    buyer = UserSerializer(read_only=True)

    class Meta:
        model = LogisticsPartnerReview
        fields = ['id', 'logistics_partner', 'buyer', 'rating', 'review_text', 'created_at']
        read_only_fields = ['id', 'buyer', 'created_at']

class LogisticsPartnerSerializer(serializers.ModelSerializer):
    service_regions = RegionSerializer(many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()

    class Meta:
        model = LogisticsPartner
        fields = ['id', 'name', 'description', 'contact_phone', 'email', 'service_regions', 'base_rate', 'registration_date', 'is_verified', 'website', 'average_rating']
        read_only_fields = ['id', 'registration_date', 'is_verified', 'average_rating']

    def get_average_rating(self, obj):
        return obj.average_rating()

class LogisticsPartnerRegistrationSerializer(serializers.ModelSerializer):
    service_regions = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        help_text="List of region objects with id, name, and type"
    )

    class Meta:
        model = LogisticsPartner
        fields = ['name', 'description', 'contact_phone', 'email', 'service_regions', 'base_rate', 'website']

    def create(self, validated_data):
        service_regions_data = validated_data.pop('service_regions', [])
        logistics_partner = LogisticsPartner.objects.create(**validated_data)

        # Add service regions
        region_ids = []
        for region_data in service_regions_data:
            region_id = region_data.get('id')
            if region_id:
                region_ids.append(region_id)

        if region_ids:
            # Get existing regions by ID
            regions = Region.objects.filter(id__in=region_ids)
            logistics_partner.service_regions.set(regions)

        return logistics_partner

class LogisticsPartnerRegistrationSerializer(serializers.ModelSerializer):
    username = serializers.CharField(write_only=True)
    password = serializers.CharField(write_only=True, min_length=8)
    email = serializers.EmailField(write_only=True)

    class Meta:
        model = LogisticsPartner
        fields = ['username', 'password', 'email', 'name', 'description', 'contact_phone',
                 'service_regions', 'base_rate', 'website', 'vehicle_types', 'capacity',
                 'business_license', 'insurance_details']

    def create(self, validated_data):
        # Extract user data
        username = validated_data.pop('username')
        password = validated_data.pop('password')
        email = validated_data.pop('email')

        # Create user account
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            is_logistics_partner=True
        )

        # Create logistics partner profile
        logistics_partner = LogisticsPartner.objects.create(
            user=user,
            email=email,  # Also store in logistics partner for business purposes
            **validated_data
        )

        return logistics_partner

# Messaging Serializers
class MessageSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)
    reply_to = serializers.SerializerMethodField()
    is_read_by_user = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = ['id', 'conversation', 'sender', 'message_type', 'content', 'attachment',
                 'created_at', 'updated_at', 'is_edited', 'reply_to', 'is_read_by_user']
        read_only_fields = ['id', 'created_at', 'updated_at', 'is_edited']

    def get_reply_to(self, obj):
        if obj.reply_to:
            return {
                'id': obj.reply_to.id,
                'content': obj.reply_to.content[:100],
                'sender': obj.reply_to.sender.username
            }
        return None

    def get_is_read_by_user(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.read_by.filter(user=request.user).exists()
        return False

class ConversationSerializer(serializers.ModelSerializer):
    participants = UserSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    related_order = serializers.SerializerMethodField()
    related_produce = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = ['id', 'type', 'participants', 'title', 'is_active', 'created_at',
                 'updated_at', 'last_message', 'unread_count', 'related_order', 'related_produce']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_last_message(self, obj):
        last_message = obj.get_last_message()
        if last_message:
            return {
                'id': last_message.id,
                'content': last_message.content,
                'sender': last_message.sender.username,
                'created_at': last_message.created_at,
                'message_type': last_message.message_type
            }
        return None

    def get_unread_count(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_unread_count(request.user)
        return 0

    def get_related_order(self, obj):
        if obj.related_order:
            return {
                'id': obj.related_order.id,
                'status': obj.related_order.status
            }
        return None

    def get_related_produce(self, obj):
        if obj.related_produce:
            return {
                'id': obj.related_produce.id,
                'name': obj.related_produce.name,
                'farmer': obj.related_produce.farmer.username
            }
        return None

class MessageNotificationSerializer(serializers.ModelSerializer):
    message = MessageSerializer(read_only=True)

    class Meta:
        model = MessageNotification
        fields = ['id', 'message', 'is_read', 'created_at']
        read_only_fields = ['id', 'created_at']

class OrderLogisticsSerializer(serializers.ModelSerializer):
    logistics_partner = LogisticsPartnerSerializer(read_only=True)

    class Meta:
        model = OrderLogistics
        fields = ['id', 'order', 'logistics_partner', 'selection_date', 'estimated_delivery', 'tracking_number', 'cost', 'updated_at']
        read_only_fields = ['id', 'order', 'selection_date', 'updated_at']
