import React, { useState, useEffect, useRef } from 'react';
import axiosInstance from '../api/axiosInstance';
import { useAuth } from '../api/AuthContext';
import Navbar from '../components/Navbar';

const Messages = () => {
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation.id);
      markConversationAsRead(selectedConversation.id);
    }
  }, [selectedConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchConversations = async () => {
    try {
      const response = await axiosInstance.get('conversations/');
      setConversations(response.data.results || response.data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Failed to load conversations');
      setLoading(false);
    }
  };

  const fetchMessages = async (conversationId) => {
    try {
      const response = await axiosInstance.get(`messages/?conversation=${conversationId}`);
      setMessages(response.data.results || response.data);
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError('Failed to load messages');
    }
  };

  const markConversationAsRead = async (conversationId) => {
    try {
      await axiosInstance.post(`conversations/${conversationId}/mark_as_read/`);
      // Update conversation unread count
      setConversations(prev => 
        prev.map(conv => 
          conv.id === conversationId 
            ? { ...conv, unread_count: 0 }
            : conv
        )
      );
    } catch (err) {
      console.error('Error marking conversation as read:', err);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedConversation || sendingMessage) return;

    setSendingMessage(true);
    try {
      const response = await axiosInstance.post('messages/', {
        conversation: selectedConversation.id,
        content: newMessage.trim(),
        message_type: 'text'
      });

      setMessages(prev => [...prev, response.data]);
      setNewMessage('');
      
      // Update conversation's last message
      setConversations(prev =>
        prev.map(conv =>
          conv.id === selectedConversation.id
            ? {
                ...conv,
                last_message: {
                  content: newMessage.trim(),
                  sender: user.username,
                  created_at: new Date().toISOString()
                }
              }
            : conv
        )
      );
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const getParticipantNames = (conversation) => {
    return conversation.participants
      .filter(p => p.id !== user.id)
      .map(p => p.username)
      .join(', ');
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container page-content">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading messages...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container-fluid page-content">
        <div className="row">
          {/* Conversations List */}
          <div className="col-md-4 col-lg-3">
            <div className="card h-100">
              <div className="card-header d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Messages</h5>
                <button className="btn btn-sm btn-primary">
                  <span className="material-icons">add</span>
                </button>
              </div>
              <div className="card-body p-0" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
                {conversations.length === 0 ? (
                  <div className="text-center p-4">
                    <span className="material-icons text-muted" style={{ fontSize: '48px' }}>
                      chat_bubble_outline
                    </span>
                    <p className="text-muted mt-2">No conversations yet</p>
                  </div>
                ) : (
                  conversations.map(conversation => (
                    <div
                      key={conversation.id}
                      className={`conversation-item p-3 border-bottom cursor-pointer ${
                        selectedConversation?.id === conversation.id ? 'bg-light' : ''
                      }`}
                      onClick={() => setSelectedConversation(conversation)}
                      style={{ cursor: 'pointer' }}
                    >
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <h6 className="mb-1">{getParticipantNames(conversation)}</h6>
                          <p className="mb-1 text-muted small">
                            {conversation.last_message?.content || 'No messages yet'}
                          </p>
                          <small className="text-muted">
                            {conversation.last_message?.created_at && formatTime(conversation.last_message.created_at)}
                          </small>
                        </div>
                        {conversation.unread_count > 0 && (
                          <span className="badge bg-primary rounded-pill">
                            {conversation.unread_count}
                          </span>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Messages Area */}
          <div className="col-md-8 col-lg-9">
            {selectedConversation ? (
              <div className="card h-100">
                {/* Chat Header */}
                <div className="card-header d-flex justify-content-between align-items-center">
                  <div>
                    <h5 className="mb-0">{getParticipantNames(selectedConversation)}</h5>
                    <small className="text-muted">
                      {selectedConversation.type.replace('_', ' ').toUpperCase()}
                      {selectedConversation.related_produce && 
                        ` • About: ${selectedConversation.related_produce.name}`
                      }
                    </small>
                  </div>
                  <div className="dropdown">
                    <button className="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            data-bs-toggle="dropdown">
                      <span className="material-icons">more_vert</span>
                    </button>
                    <ul className="dropdown-menu">
                      <li><a className="dropdown-item" href="#">View Profile</a></li>
                      <li><a className="dropdown-item" href="#">Block User</a></li>
                      <li><hr className="dropdown-divider" /></li>
                      <li><a className="dropdown-item text-danger" href="#">Delete Conversation</a></li>
                    </ul>
                  </div>
                </div>

                {/* Messages */}
                <div className="card-body" style={{ height: '50vh', overflowY: 'auto' }}>
                  {error && (
                    <div className="alert alert-danger alert-dismissible fade show">
                      {error}
                      <button type="button" className="btn-close" onClick={() => setError(null)}></button>
                    </div>
                  )}

                  {messages.length === 0 ? (
                    <div className="text-center text-muted">
                      <span className="material-icons" style={{ fontSize: '48px' }}>
                        chat
                      </span>
                      <p className="mt-2">Start the conversation!</p>
                    </div>
                  ) : (
                    messages.map(message => (
                      <div
                        key={message.id}
                        className={`d-flex mb-3 ${
                          message.sender.id === user.id ? 'justify-content-end' : 'justify-content-start'
                        }`}
                      >
                        <div
                          className={`message-bubble p-3 rounded-3 ${
                            message.sender.id === user.id
                              ? 'bg-primary text-white'
                              : 'bg-light text-dark'
                          }`}
                          style={{ maxWidth: '70%' }}
                        >
                          {message.sender.id !== user.id && (
                            <small className="fw-bold d-block mb-1">
                              {message.sender.username}
                            </small>
                          )}
                          <p className="mb-1">{message.content}</p>
                          <small className={`d-block ${
                            message.sender.id === user.id ? 'text-white-50' : 'text-muted'
                          }`}>
                            {formatTime(message.created_at)}
                          </small>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className="card-footer">
                  <form onSubmit={sendMessage}>
                    <div className="input-group">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Type a message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        disabled={sendingMessage}
                      />
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={!newMessage.trim() || sendingMessage}
                      >
                        {sendingMessage ? (
                          <span className="spinner-border spinner-border-sm" role="status">
                            <span className="visually-hidden">Sending...</span>
                          </span>
                        ) : (
                          <span className="material-icons">send</span>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            ) : (
              <div className="card h-100 d-flex align-items-center justify-content-center">
                <div className="text-center text-muted">
                  <span className="material-icons" style={{ fontSize: '64px' }}>
                    forum
                  </span>
                  <h4 className="mt-3">Select a conversation</h4>
                  <p>Choose a conversation from the left to start messaging</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Messages;
