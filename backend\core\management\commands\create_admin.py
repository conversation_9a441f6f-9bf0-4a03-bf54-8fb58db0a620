from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
import getpass

User = get_user_model()

class Command(BaseCommand):
    help = 'Create an admin user securely'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Admin username',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Admin email',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='Admin password (not recommended for production)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.WARNING('Creating Admin User - This should only be done by system administrators')
        )
        
        # Get username
        username = options.get('username')
        if not username:
            username = input('Username: ')
        
        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.ERROR(f'User "{username}" already exists!')
            )
            return
        
        # Get email
        email = options.get('email')
        if not email:
            email = input('Email: ')
        
        # Get password
        password = options.get('password')
        if not password:
            password = getpass.getpass('Password: ')
            password_confirm = getpass.getpass('Confirm password: ')
            
            if password != password_confirm:
                self.stdout.write(
                    self.style.ERROR('Passwords do not match!')
                )
                return
        
        try:
            # Create admin user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                is_staff=True,
                is_superuser=True,
                is_farmer=False,
                is_buyer=False
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'Admin user "{username}" created successfully!')
            )
            self.stdout.write(
                self.style.WARNING('Remember to keep admin credentials secure!')
            )
            
        except ValidationError as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating user: {e}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Unexpected error: {e}')
            )