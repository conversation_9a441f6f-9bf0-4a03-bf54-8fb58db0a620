/* Override Bootstrap CSS variables for an organic, earthy theme */

:root,
[data-bs-theme="light"] {
  --bs-primary: #4e944f; /* Organic green */
  --bs-primary-rgb: 78, 148, 79;
  --bs-success: #f6f5ec; /* Off-white for freshness */
  --bs-success-rgb: 246, 245, 236;
  --bs-warning: #ffd166; /* Brighter yellow for contrast */
  --bs-warning-rgb: 255, 209, 102;
  --bs-danger: #e94f37; /* Vibrant red for accents */
  --bs-danger-rgb: 233, 79, 55;
  --bs-info: #4a90e2; /* Crisp blue for info */
  --bs-info-rgb: 74, 144, 226;
  --bs-light: #fff; /* Pure white for backgrounds */
  --bs-light-rgb: 255, 255, 255;
  --bs-dark: #3a3a2c; /* Deep grey for text */
  --bs-dark-rgb: 58, 58, 44;
  --bs-body-bg: #f9faf6; /* Very light, fresh background */
  --bs-body-color: #2c3e2f; /* Deep greenish-grey for text */
  --bs-card-bg: #fff; /* Pure white cards */
  --bs-navbar-bg: #f6f5ec; /* Off-white navbar */
  --bs-input-bg: #fff; /* White input fields */
  --bs-input-color: #2c3e2f; /* Deep greenish-grey input text */
}

[data-bs-theme="dark"] {
  --bs-primary: #8fc490; /* Softer muted green */
  --bs-primary-rgb: 143, 196, 144;
  --bs-success: #b6e388; /* Pale green */
  --bs-success-rgb: 182, 227, 136;
  --bs-warning: #ffe066; /* Soft yellow (unchanged, for accent) */
  --bs-warning-rgb: 255, 224, 102;
  --bs-danger: #6fa3d9; /* Muted blue for accents */
  --bs-danger-rgb: 111, 163, 217;
  --bs-info: #a3c585; /* Light green (unchanged) */
  --bs-info-rgb: 163, 197, 133;
  --bs-light: #232d23; /* Deep forest green (unchanged) */
  --bs-light-rgb: 35, 45, 35;
  --bs-dark: #e6f2e6; /* Misty light green (unchanged) */
  --bs-dark-rgb: 230, 242, 230;
  --bs-body-bg: #23272a; /* Muted gray background (unchanged) */
  --bs-body-color: #e6f2e6; /* Misty light green (unchanged) */
  --bs-card-bg: #26292c; /* Slightly darker card for contrast */
  --bs-navbar-bg: #232d23; /* Unchanged */
  --bs-input-bg: #232d23; /* Unchanged */
  --bs-input-color: #e6f2e6; /* Use body color for input text */
}

[data-bs-theme="dark"] body {
  background: linear-gradient(135deg, #23272a 0%, #26292c 100%);
  color: var(--bs-body-color);
}

body {
  font-family: 'Lora', 'Changa', Arial, sans-serif;
  background: var(--bs-body-bg);
  min-height: 100vh;
  color: var(--bs-body-color);
  transition: background 0.3s, color 0.3s;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--bs-primary);
  font-family: 'Lora', serif;
  letter-spacing: 0.01em;
}

a, .nav-link, .navbar-brand {
  color: var(--bs-primary) !important;
  transition: color 0.2s;
}

a:hover, .nav-link:hover, .navbar-brand:hover {
  color: var(--bs-danger) !important;
}

.navbar {
  background: var(--bs-navbar-bg) !important;
  box-shadow: 0 2px 8px rgba(var(--bs-primary-rgb), 0.04);
  border-bottom: 2px solid var(--bs-success);
  transition: background 0.3s, color 0.3s, border-color 0.3s;
}

.navbar .navbar-brand {
  color: var(--bs-primary) !important;
  font-weight: 700;
  letter-spacing: 0.03em;
  font-size: 1.4rem;
  transition: color 0.2s;
}

.navbar .nav-link {
  color: var(--bs-dark);
  font-weight: 500;
  border-radius: 0.5rem;
  margin: 0 0.25rem;
  padding: 0.5rem 1rem;
  transition: background 0.2s, color 0.2s;
}

.navbar .nav-link.active, .navbar .nav-link:focus, .navbar .nav-link:hover {
  background: var(--bs-success);
  color: var(--bs-primary) !important;
}

[data-bs-theme="dark"] .navbar {
  background: var(--bs-navbar-bg) !important;
  box-shadow: 0 2px 8px rgba(143, 196, 144, 0.08);
  border-bottom: 2px solid #8fc490;
}

[data-bs-theme="dark"] .navbar .navbar-brand {
  color: #8fc490 !important;
}

[data-bs-theme="dark"] .navbar .nav-link {
  color: #e6f2e6;
  background: transparent;
}

[data-bs-theme="dark"] .navbar .nav-link.active, [data-bs-theme="dark"] .navbar .nav-link:focus, [data-bs-theme="dark"] .navbar .nav-link:hover {
  background: #26292c;
  color: #8fc490 !important;
}

.card {
  background: var(--bs-card-bg);
  border: 1px solid var(--bs-warning);
  box-shadow: 0 2px 8px rgba(var(--bs-success-rgb), 0.10);
  border-radius: 1rem;
  transition: background 0.3s, color 0.3s;
}

[data-bs-theme="dark"] .card {
  background: var(--bs-card-bg);
  color: var(--bs-body-color);
  border: 1px solid #8fc490;
  box-shadow: 0 2px 16px rgba(143, 196, 144, 0.10);
}

[data-bs-theme="dark"] h1, [data-bs-theme="dark"] h2, [data-bs-theme="dark"] h3, [data-bs-theme="dark"] h4, [data-bs-theme="dark"] h5, [data-bs-theme="dark"] h6 {
  color: #b6e388;
}

[data-bs-theme="dark"] a, [data-bs-theme="dark"] .nav-link, [data-bs-theme="dark"] .navbar-brand {
  color: #8fc490 !important;
}

[data-bs-theme="dark"] a:hover, [data-bs-theme="dark"] .nav-link:hover, [data-bs-theme="dark"] .navbar-brand:hover {
  color: #ffe066 !important;
}

input, textarea, .form-control {
  border-radius: 0.75rem;
  border-color: var(--bs-success);
  background: var(--bs-input-bg);
  color: var(--bs-input-color);
  transition: background 0.3s, color 0.3s;
}

[data-bs-theme="dark"] input,
[data-bs-theme="dark"] textarea,
[data-bs-theme="dark"] .form-control {
  color: var(--bs-input-color);
  background: #232d23;
  border-color: #8fc490;
}

[data-bs-theme="dark"] input::placeholder,
[data-bs-theme="dark"] textarea::placeholder,
[data-bs-theme="dark"] .form-control::placeholder {
  color: #b6e388;
  opacity: 0.8;
}

::-webkit-scrollbar {
  width: 10px;
  background: var(--bs-body-bg);
}
::-webkit-scrollbar-thumb {
  background: #4e944f;
  border-radius: 5px;
}

/* Add a subtle organic pattern overlay (optional, comment out if not wanted) */
/* body::before {
  content: "";
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  pointer-events: none;
  opacity: 0.04;
  background: url('/assets/img/organic-pattern.png') repeat;
  z-index: 0;
} */