import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { FaArrowRight, FaChevronLeft, FaChevronRight, FaHeart } from "react-icons/fa";
import defaultProduceImage from "../assets/img/01.jpg";
import defaultAvatar from "../assets/img/avatar/3.jpg";
import axiosInstance from "../api/axiosInstance";
import "./styles/PopularProduceSection.css";
import "../App.css";

const timeOptions = [
  "Last 24 Hours",
  "Last 7 Days",
  "Last 30 Days",
];

function PopularProduceSection() {
  const [selectedTime, setSelectedTime] = useState(timeOptions[0]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [originalData, setOriginalData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch produce data from backend
  useEffect(() => {
    const fetchProduceData = async () => {
      setError(null);
      setLoading(true);
      try {
        const response = await axiosInstance.get('produce/', {
          params: {
            approved: true,
            ordering: '-created_at'
          }
        });
        
        if (response.data?.length > 0) {
          setOriginalData(response.data);
          setFilteredData(response.data); // Initially show all data
        } else {
          setError("No approved produce found.");
          setOriginalData([]);
          setFilteredData([]);
        }
      } catch (err) {
        console.error("Error fetching produce data:", err);
        setError("Failed to load produce. Please try again later.");
        setOriginalData([]);
        setFilteredData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProduceData();
  }, []);

  // Filter produce based on selected time option
  useEffect(() => {
    if (originalData.length > 0) {
      const now = new Date();
      let filtered = [...originalData];
      
      if (selectedTime === "Last 24 Hours") {
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        filtered = filtered.filter(item => new Date(item.created_at) > oneDayAgo);
      } else if (selectedTime === "Last 7 Days") {
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(item => new Date(item.created_at) > sevenDaysAgo);
      } else if (selectedTime === "Last 30 Days") {
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(item => new Date(item.created_at) > thirtyDaysAgo);
      }
      
      setFilteredData(filtered);
      setCurrentIndex(0); // Reset to first item when filtering
    }
  }, [selectedTime, originalData]);

  const handlePrev = () => {
    setCurrentIndex(prev => (prev === 0 ? filteredData.length - 1 : prev - 1));
  };
  
  const handleNext = () => {
    setCurrentIndex(prev => (prev === filteredData.length - 1 ? 0 : prev + 1));
  };

  // Calculate end time (improved version)
  const getEndTime = (createdAt) => {
    const createdDate = new Date(createdAt);
    const endDate = new Date(createdDate.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from creation
    const now = new Date();
    
    if (now > endDate) return "Ended";
    
    const diff = endDate - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    return `${days}d ${hours}h`;
  };

  const currentItem = filteredData[currentIndex];

  return (
    <section className="position-relative popular-produce-section py-5">
      <div className="position-absolute start-0 top-0 w-100 h-50 w-md-75 bg-success bg-opacity-10 rounded-top-end-5 top-gradient"></div>
      <div className="container pt-5 position-relative produce-container">
        <div className="d-flex flex-column flex-md-row align-items-center justify-content-md-between mb-4">
          <div className="d-flex flex-sm-row flex-column align-items-center mb-3 mb-md-0">
            <h2 className="mb-3 mb-sm-0 me-sm-3 display-6 text-success">Popular Produce</h2>
            <select
              className="form-select text-secondary fs-5 border-0 fw-bold bg-transparent time-select"
              value={selectedTime}
              onChange={e => setSelectedTime(e.target.value)}
              aria-label="Select time period"
            >
              {timeOptions.map(opt => (
                <option key={opt} value={opt}>{opt}</option>
              ))}
            </select>
          </div>
          <Link to="/produce-explore" className="btn btn-primary btn-hover d-flex align-items-center">
            <FaArrowRight className="me-2" /> Explore All Produce
          </Link>
        </div>
      </div>
      
      <div className="overflow-hidden">
        <div className="container pt-3 pb-5">
          {loading ? (
            <div className="d-flex justify-content-center align-items-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : error ? (
            <div className="alert alert-danger">
              {error}
            </div>
          ) : filteredData.length === 0 ? (
            <div className="alert alert-info">
              No produce available for the selected time period.
            </div>
          ) : (
            <div className="d-flex justify-content-center align-items-center mb-4">
              <button 
                className="btn btn-outline-primary rounded-pill me-3" 
                onClick={handlePrev} 
                disabled={filteredData.length <= 1}
                aria-label="Previous produce"
              >
                <FaChevronLeft size={24} />
              </button>
              
              <div className="produce-card">
                <div className="card p-2 pb-0 card-hover overflow-hidden hover-lift shadow-sm">
                  <div className="position-relative overflow-hidden rounded">
                    <img 
                      src={currentItem?.photo || defaultProduceImage} 
                      className="img-fluid card-img-top" 
                      alt={currentItem?.name || "Produce image"} 
                      onError={(e) => {e.target.src = defaultProduceImage}}
                    />
                    <div className="position-absolute start-0 bottom-0 px-3 py-2 mb-3 d-flex flex-column align-items-start ms-3 bg-dark rounded bg-opacity-50 text-white countdown">
                      <span className="d-block small">Ends in:</span>
                      <div className="d-flex align-items-center small">
                        {currentItem?.created_at ? getEndTime(currentItem.created_at) : "N/A"}
                      </div>
                    </div>
                    <div className="position-absolute end-0 top-0 me-4 mt-4 z-index-2">
                      <button 
                        className="btn btn-favorite btn-white p-0 height-2x px-2 rounded-pill d-flex align-items-center justify-content-center position-relative"
                        aria-label="Add to favorites"
                      >
                        <FaHeart className="text-danger me-1" /> 
                        {Math.floor(Math.random() * 30) + 1}
                      </button>
                    </div>
                    <div className="position-absolute start-50 top-50 translate-middle hover-visible">
                      <span className="btn btn-white" role="button">Contact Farmer</span>
                    </div>
                  </div>
                  <div className="card-body px-2">
                    <div className="d-flex align-items-center mb-2">
                      <span className="d-flex align-items-center flex-shrink-0 position-relative z-index-2">
                        <img 
                          src={currentItem?.farmer?.profile?.avatar || defaultAvatar} 
                          className="rounded-circle flex-shrink-0 me-2 farmer-avatar" 
                          alt={currentItem?.farmer?.username ? `${currentItem.farmer.username}'s avatar` : "Farmer avatar"}
                          onError={(e) => {e.target.src = defaultAvatar}}
                        />
                        <span className="flex-grow-1 small text-muted">
                          @{currentItem?.farmer?.username || "farmer"}
                        </span>
                      </span>
                    </div>
                    <h5 className="mb-3 text-truncate text-primary">
                      {currentItem?.name || "Unnamed Produce"}
                    </h5>
                    <div className="px-3 py-2 rounded bg-light">
                      <div className="row">
                        <div className="col-6 border-end">
                          <small className="text-muted">Price:</small><br />
                          <strong>${currentItem?.price || "N/A"}</strong>
                        </div>
                        <div className="col-6 ps-lg-5">
                          <small className="text-muted">Contact:</small><br />
                          <strong>{currentItem?.contact_phone || "Available"}</strong>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Link 
                    to={`/produce/${currentItem?.id}`} 
                    className="stretched-link"
                    aria-label={`View details for ${currentItem?.name}`}
                  />
                </div>
              </div>
              
              <button 
                className="btn btn-outline-primary rounded-pill ms-3" 
                onClick={handleNext} 
                disabled={filteredData.length <= 1}
                aria-label="Next produce"
              >
                <FaChevronRight size={24} />
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

export default PopularProduceSection;