"""
Custom middleware for AgriMarket
"""

import logging
import time
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to handle errors gracefully and return JSON responses for API endpoints
    """

    def process_exception(self, request, exception):
        """Handle exceptions and return appropriate JSON responses for API calls"""

        # Only handle API requests
        if not request.path.startswith('/api/'):
            return None

        logger.error(f"API Error: {exception}", exc_info=True)

        # Return JSON error response
        return JsonResponse({
            'error': 'Internal server error',
            'message': str(exception) if hasattr(exception, 'message') else 'An unexpected error occurred',
            'status_code': 500
        }, status=500)


class LoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log API requests and responses
    """

    def process_request(self, request):
        """Log incoming requests"""
        if request.path.startswith('/api/'):
            request.start_time = time.time()
            logger.info(f"API Request: {request.method} {request.path} from {request.META.get('REMOTE_ADDR')}")

    def process_response(self, request, response):
        """Log outgoing responses"""
        if request.path.startswith('/api/') and hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            logger.info(f"API Response: {request.method} {request.path} -> {response.status_code} ({duration:.3f}s)")

        return response