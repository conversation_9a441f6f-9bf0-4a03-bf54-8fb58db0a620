# AgriMarket Backend

Django REST Framework backend for the AgriMarket platform.

## Quick Setup

### Option 1: Automated Setup
```bash
cd backend
python setup_backend.py
python manage.py runserver
```

### Option 2: Manual Setup
```bash
cd backend
pip install -r requirements.txt
python manage.py migrate
python manage.py create_sample_data
python manage.py runserver
```

## Create Admin User (Optional)
```bash
python manage.py createsuperuser
```

## API Endpoints

### Authentication
- `POST /api/register/` - User registration
- `POST /api/login/` - User login

### Produce
- `GET /api/produce/` - List all produce
- `GET /api/produce/approved/` - List approved produce only
- `GET /api/produce/organic/` - List organic produce only
- `POST /api/produce/` - Create new produce (requires authentication)

### Categories
- `GET /api/categories/` - List produce categories

### Other Endpoints
- `GET /api/prices/` - Market prices
- `GET /api/collections/` - Collections
- `GET /api/favorites/` - User favorites (requires authentication)
- `GET /api/watchlist/` - User watchlist (requires authentication)

## Sample Data

The setup script creates:
- 2 farmer users (farmer1, farmer2) with password: password123
- 4 produce categories (Vegetables, Fruits, Grains, Herbs)
- 5 sample produce items

## Admin Panel

Access the admin panel at: http://127.0.0.1:8000/admin

## Development

The backend runs on: http://127.0.0.1:8000
API base URL: http://127.0.0.1:8000/api/