#!/usr/bin/env python3
"""
Fix AgriMarket Backend Issues
This script helps diagnose and fix common backend issues.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None):
    """Run a command and return the result."""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Success")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
        else:
            print(f"❌ Failed (exit code: {result.returncode})")
            if result.stderr.strip():
                print(f"Error: {result.stderr.strip()}")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
        
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, "", str(e)

def check_virtual_env():
    """Check if we're in a virtual environment."""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment is active")
        return True
    else:
        print("⚠️  No virtual environment detected")
        return False

def fix_backend():
    """Fix common backend issues."""
    print("🔧 Fixing AgriMarket Backend")
    print("=" * 40)
    
    # Check current directory
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Please run this script from the AgriMarket root directory")
        return False
    
    os.chdir(backend_dir)
    print(f"📁 Working in: {os.getcwd()}")
    
    # Check virtual environment
    check_virtual_env()
    
    # Step 1: Install requirements
    print("\n📦 Installing requirements...")
    success, _, _ = run_command("pip install -r requirements.txt")
    if not success:
        print("⚠️  Requirements installation failed, continuing anyway...")
    
    # Step 2: Check Django installation
    print("\n🔍 Checking Django...")
    success, output, _ = run_command("python -c \"import django; print(f'Django {django.get_version()}')\"")
    if not success:
        print("❌ Django not properly installed")
        return False
    
    # Step 3: Check Django configuration
    print("\n⚙️  Checking Django configuration...")
    success, _, error = run_command("python manage.py check")
    if not success:
        print(f"❌ Django configuration issues: {error}")
        return False
    
    # Step 4: Create migrations
    print("\n🗄️  Creating migrations...")
    run_command("python manage.py makemigrations")
    
    # Step 5: Run migrations
    print("\n🗄️  Running migrations...")
    success, _, error = run_command("python manage.py migrate")
    if not success:
        print(f"❌ Migration failed: {error}")
        return False
    
    # Step 6: Create some initial data
    print("\n📊 Creating initial data...")
    create_initial_data()
    
    # Step 7: Test the server
    print("\n🧪 Testing Django server...")
    success, _, _ = run_command("python manage.py check --deploy")
    
    print("\n" + "=" * 40)
    print("🎉 Backend setup complete!")
    print("\n📋 Next steps:")
    print("1. Start the server: python manage.py runserver 127.0.0.1:8000")
    print("2. Test the API: http://127.0.0.1:8000/api/categories/")
    print("3. Start the frontend in another terminal")
    
    return True

def create_initial_data():
    """Create some initial data for testing."""
    script = '''
import os
import django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agrimarket_backend.settings")
django.setup()

from core.models import ProduceCategory

# Create some basic categories
categories = [
    {"name": "Vegetables", "description": "Fresh vegetables"},
    {"name": "Fruits", "description": "Fresh fruits"},
    {"name": "Grains", "description": "Cereals and grains"},
    {"name": "Herbs", "description": "Herbs and spices"},
]

for cat_data in categories:
    category, created = ProduceCategory.objects.get_or_create(
        name=cat_data["name"],
        defaults={"description": cat_data["description"]}
    )
    if created:
        print(f"Created category: {category.name}")
    else:
        print(f"Category already exists: {category.name}")

print("Initial data setup complete!")
'''
    
    # Write the script to a temporary file
    with open("create_data.py", "w") as f:
        f.write(script)
    
    # Run the script
    success, output, error = run_command("python create_data.py")
    
    # Clean up
    try:
        os.remove("create_data.py")
    except:
        pass
    
    if success:
        print("✅ Initial data created")
    else:
        print(f"⚠️  Initial data creation failed: {error}")

def main():
    """Main function."""
    if not fix_backend():
        print("\n❌ Backend setup failed!")
        print("💡 Try running the commands manually:")
        print("   cd backend")
        print("   pip install -r requirements.txt")
        print("   python manage.py migrate")
        print("   python manage.py runserver")
        sys.exit(1)

if __name__ == "__main__":
    main()
