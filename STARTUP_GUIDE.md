# 🚀 AgriMarket Startup Guide

## Quick Start (Automated)

```bash
python start_agrimarket.py
```

## Manual Start (Step by Step)

### 1. Start Backend

```bash
# Navigate to backend directory
cd backend

# Activate virtual environment (if it exists)
# Windows:
myenv\Scripts\activate
# Linux/Mac:
source myenv/bin/activate

# Install requirements
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Start Django server
python manage.py runserver 127.0.0.1:8000
```

### 2. Start Frontend (in a new terminal)

```bash
# Navigate to frontend directory
cd frontend/agrimarket_frontend

# Install dependencies (if not done before)
npm install

# Start development server
npm run dev
```

## 🔍 Troubleshooting Connection Issues

### Issue: "Unable to connect to server"

**Check 1: Is the backend running?**
- Open http://127.0.0.1:8000 in your browser
- You should see a Django page or API response

**Check 2: Check backend logs**
- Look at the terminal where you started the backend
- Check for any error messages

**Check 3: Test API endpoint directly**
- Open http://127.0.0.1:8000/api/categories/ in your browser
- You should see JSON data or an empty array

**Check 4: CORS configuration**
- Make sure your .env file has the correct CORS settings:
```
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173
```

### Common Solutions

1. **Backend not starting:**
   ```bash
   cd backend
   python manage.py check
   python manage.py migrate
   ```

2. **Port conflicts:**
   - Backend should run on port 8000
   - Frontend should run on port 5173
   - Make sure no other services are using these ports

3. **Virtual environment issues:**
   ```bash
   cd backend
   # Create new virtual environment if needed
   python -m venv myenv
   # Activate it
   myenv\Scripts\activate  # Windows
   source myenv/bin/activate  # Linux/Mac
   # Install requirements
   pip install -r requirements.txt
   ```

4. **Database issues:**
   ```bash
   cd backend
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **Frontend dependencies:**
   ```bash
   cd frontend/agrimarket_frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

## 🧪 Testing the Connection

### Test Backend API:
```bash
# Test if backend is responding
curl http://127.0.0.1:8000/api/categories/

# Should return JSON response (might be empty array)
```

### Test Frontend:
- Open http://127.0.0.1:5173
- Should load without "Unable to connect to server" error

## 📋 Checklist

- [ ] Backend running on http://127.0.0.1:8000
- [ ] Frontend running on http://127.0.0.1:5173
- [ ] API endpoint http://127.0.0.1:8000/api/categories/ accessible
- [ ] No CORS errors in browser console
- [ ] .env file configured correctly

## 🆘 Still Having Issues?

1. **Check the browser console** (F12) for error messages
2. **Check backend terminal** for Django error messages
3. **Verify ports** - make sure 8000 and 5173 are free
4. **Try the automated startup script**: `python start_agrimarket.py`

## 📞 Quick Debug Commands

```bash
# Check if ports are in use
netstat -an | findstr :8000  # Windows
netstat -an | grep :8000     # Linux/Mac

# Test backend health
curl http://127.0.0.1:8000/admin/  # Should show Django admin login

# Check Django configuration
cd backend
python manage.py check --deploy
```
