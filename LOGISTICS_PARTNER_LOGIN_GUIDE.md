# 🚚 Logistics Partner Login Guide

## 🎯 **How Logistics Partners Log In**

### **Method 1: Dedicated Logistics Login Page**
1. **Go to**: `http://127.0.0.1:5173/logistics-login`
2. **Enter credentials**: Username and password from registration
3. **Click "Login to Dashboard"**
4. **Redirected to**: Logistics Dashboard automatically

### **Method 2: Regular Login Page**
1. **Go to**: `http://127.0.0.1:5173/login`
2. **Enter credentials**: Username and password
3. **System automatically detects** logistics partner role
4. **Access dashboard** via "Logistics Hub" dropdown

### **Method 3: Via Services Menu**
1. **Click "Services"** dropdown in navbar
2. **Select "Logistics Partner Login"**
3. **Enter credentials** on dedicated login page
4. **Access dashboard** directly

## 🔐 **Login Process Details**

### **Authentication Flow**
```
1. User enters username/password
2. System authenticates credentials
3. Checks if user.is_logistics_partner = True
4. Returns user data with logistics role
5. Frontend shows Logistics Hub navigation
6. User can access logistics dashboard
```

### **What Happens After Login**
- ✅ **Navbar Updates**: Shows "Logistics Hub" dropdown
- ✅ **Role Recognition**: System knows user is logistics partner
- ✅ **Dashboard Access**: Can navigate to logistics dashboard
- ✅ **Messaging**: Can communicate with buyers/farmers
- ✅ **Order Management**: Can view and update deliveries

## 🧪 **Testing the Login System**

### **Step 1: Create Test Logistics Partner**
```bash
# Option A: Use registration form
1. Go to: http://127.0.0.1:5173/logistics-register
2. Fill out form with test data:
   - Username: test_logistics
   - Password: testpass123
   - Company: Test Logistics Co.
   - Email: <EMAIL>
3. Submit registration

# Option B: Create via Django admin/shell
python manage.py shell
from core.models import User, LogisticsPartner
user = User.objects.create_user(
    username='test_logistics',
    password='testpass123',
    email='<EMAIL>',
    is_logistics_partner=True
)
logistics = LogisticsPartner.objects.create(
    user=user,
    name='Test Logistics Co.',
    email='<EMAIL>',
    contact_phone='+263 77 123 4567'
)
```

### **Step 2: Test Login**
1. **Go to**: `http://127.0.0.1:5173/logistics-login`
2. **Enter**:
   - Username: `test_logistics`
   - Password: `testpass123`
3. **Click "Login to Dashboard"**
4. **Should redirect** to logistics dashboard

### **Step 3: Verify Access**
- ✅ Check navbar shows "Logistics Hub" dropdown
- ✅ Access dashboard at `/logistics-dashboard`
- ✅ Check user dropdown shows "Messages"
- ✅ Verify role-based navigation works

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **Issue: "User is not registered as a logistics partner"**
**Solution**: 
```python
# Fix user role in Django shell
from core.models import User
user = User.objects.get(username='your_username')
user.is_logistics_partner = True
user.save()
```

#### **Issue: "Logistics partner profile not found"**
**Solution**:
```python
# Create logistics partner profile
from core.models import User, LogisticsPartner
user = User.objects.get(username='your_username')
LogisticsPartner.objects.create(
    user=user,
    name='Your Company Name',
    email=user.email,
    contact_phone='+263 77 123 4567'
)
```

#### **Issue: Login successful but no Logistics Hub in navbar**
**Solution**: 
- Check if `user.is_logistics_partner` is `True`
- Verify `hasRole("logistics")` function in AuthContext
- Clear browser cache and re-login

## 🎯 **Login Options Summary**

### **For New Logistics Partners**
1. **Register**: `/logistics-register` → Creates account
2. **Wait for approval**: Admin verifies business
3. **Login**: `/logistics-login` → Access dashboard

### **For Existing Logistics Partners**
1. **Direct Login**: `/logistics-login` → Quick access
2. **Regular Login**: `/login` → Works with any user type
3. **Services Menu**: Services → Logistics Partner Login

### **For All Users**
- **Regular Login**: `/login` → Detects user role automatically
- **Role-based Navigation**: Shows appropriate menus
- **Cross-platform**: Same login works everywhere

## 🚀 **Quick Start for Testing**

### **1. Create Test Account**
```bash
# In Django shell
from core.models import User, LogisticsPartner
user = User.objects.create_user(
    username='logistics_test',
    password='test123456',
    email='<EMAIL>',
    is_logistics_partner=True
)
LogisticsPartner.objects.create(
    user=user,
    name='Test Logistics',
    email='<EMAIL>'
)
```

### **2. Test Login**
- URL: `http://127.0.0.1:5173/logistics-login`
- Username: `logistics_test`
- Password: `test123456`

### **3. Verify Features**
- Dashboard access
- Order management
- Messaging system
- Profile management

## 🎊 **Result**

Logistics partners can now:
- ✅ **Register** their business accounts
- ✅ **Login** using dedicated or regular login
- ✅ **Access** their professional dashboard
- ✅ **Manage** delivery assignments
- ✅ **Communicate** with buyers and farmers
- ✅ **Track** their business performance

**The login system is flexible and user-friendly, supporting multiple access methods while maintaining security and role-based access control!** 🚚🔐
