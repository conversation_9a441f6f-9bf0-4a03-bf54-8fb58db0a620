#!/usr/bin/env python3
"""
Debug admin user issue comprehensively
"""

import os
import django

def main():
    """Debug admin user issue"""
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()
    
    from core.models import User
    
    print("🔍 COMPREHENSIVE ADMIN DEBUG")
    print("=" * 50)
    
    # Check all users
    print("📋 ALL USERS IN DATABASE:")
    users = User.objects.all()
    for user in users:
        print(f"   {user.username}: staff={user.is_staff}, super={user.is_superuser}, active={user.is_active}")
    
    print(f"\nTotal users: {users.count()}")
    
    # Check admin user specifically
    print("\n🔍 ADMIN USER DETAILS:")
    try:
        admin = User.objects.get(username='admin')
        print(f"   Found admin user: {admin.username}")
        print(f"   ID: {admin.id}")
        print(f"   Email: {admin.email}")
        print(f"   is_staff: {admin.is_staff} (type: {type(admin.is_staff)})")
        print(f"   is_superuser: {admin.is_superuser} (type: {type(admin.is_superuser)})")
        print(f"   is_active: {admin.is_active} (type: {type(admin.is_active)})")
        print(f"   is_farmer: {admin.is_farmer} (type: {type(admin.is_farmer)})")
        print(f"   is_buyer: {admin.is_buyer} (type: {type(admin.is_buyer)})")
        print(f"   created_at: {admin.created_at}")
        print(f"   last_login: {admin.last_login}")
        
        # Force update admin
        print(f"\n🔧 FORCING ADMIN UPDATE:")
        admin.is_staff = True
        admin.is_superuser = True
        admin.is_active = True
        admin.is_farmer = False
        admin.is_buyer = False
        admin.save()
        
        # Refresh and check again
        admin.refresh_from_db()
        print(f"   After save - is_staff: {admin.is_staff}")
        print(f"   After save - is_superuser: {admin.is_superuser}")
        
    except User.DoesNotExist:
        print("   ❌ NO ADMIN USER FOUND!")
        print("   Creating admin user now...")
        
        admin = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"   ✅ Created admin: is_staff={admin.is_staff}, is_superuser={admin.is_superuser}")
    
    # Test the profile endpoint
    print(f"\n🌐 TESTING PROFILE ENDPOINT:")
    try:
        from django.contrib.auth import authenticate
        from rest_framework.authtoken.models import Token
        
        # Authenticate admin
        auth_user = authenticate(username='admin', password='admin123')
        if auth_user:
            print(f"   ✅ Authentication successful")
            print(f"   Auth user is_staff: {auth_user.is_staff}")
            
            # Get or create token
            token, created = Token.objects.get_or_create(user=auth_user)
            print(f"   Token: {token.key[:10]}...")
            
            # Test what the profile endpoint returns
            print(f"\n📡 SIMULATING PROFILE API RESPONSE:")
            profile_data = {
                'id': auth_user.id,
                'username': auth_user.username,
                'email': auth_user.email,
                'is_staff': auth_user.is_staff,
                'is_superuser': auth_user.is_superuser,
                'is_active': auth_user.is_active,
                'is_farmer': auth_user.is_farmer,
                'is_buyer': auth_user.is_buyer,
            }
            
            for key, value in profile_data.items():
                print(f"   {key}: {value} (type: {type(value)})")
                
        else:
            print(f"   ❌ Authentication failed!")
            
    except Exception as e:
        print(f"   ❌ Error testing profile: {e}")
    
    # Check if profile endpoint exists
    print(f"\n🔍 CHECKING PROFILE ENDPOINT:")
    try:
        from core.views import UserProfileViewSet
        print(f"   ✅ UserProfileViewSet exists")
    except ImportError as e:
        print(f"   ❌ UserProfileViewSet import error: {e}")
    
    # Check URLs
    print(f"\n🔍 CHECKING URLS:")
    try:
        from django.urls import reverse
        try:
            profile_url = reverse('userprofile-me')
            print(f"   ✅ Profile URL exists: {profile_url}")
        except:
            print(f"   ⚠️  Profile URL 'userprofile-me' not found")
            
        # Check all URL patterns
        from django.conf import settings
        from django.urls import get_resolver
        resolver = get_resolver()
        
        print(f"   Available URL patterns:")
        for pattern in resolver.url_patterns:
            if hasattr(pattern, 'name') and pattern.name:
                print(f"     - {pattern.name}")
                
    except Exception as e:
        print(f"   ❌ URL check error: {e}")

if __name__ == "__main__":
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        print("   cd backend")
        print("   python debug_admin_issue.py")
    else:
        main()
