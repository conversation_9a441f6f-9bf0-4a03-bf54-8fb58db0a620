#!/usr/bin/env python3
"""
Test script for logistics partner registration API
Run from backend directory: python test_logistics_api.py
"""

import requests
import json
import os
import sys

def test_server_running():
    """Check if Django server is running"""
    print("🔍 Checking if Django server is running...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/categories/", timeout=5)
        if response.status_code == 200:
            print("✅ Django server is running")
            return True
        else:
            print(f"⚠️  Django server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Django server is not running")
        print("   Start it with: python manage.py runserver 127.0.0.1:8000")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {e}")
        return False

def test_regions_endpoint():
    """Test if regions endpoint is working"""
    print("\n🌍 Testing Regions Endpoint...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/regions/", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            count = len(data) if isinstance(data, list) else len(data.get('results', []))
            print(f"✅ Regions endpoint working - {count} regions found")
            
            if count == 0:
                print("⚠️  No regions found. Run: python create_regions_simple.py")
                return False
            
            # Show first few regions
            regions = data if isinstance(data, list) else data.get('results', [])
            print("📋 Sample regions:")
            for region in regions[:3]:
                print(f"   - {region.get('name')} ({region.get('type')})")
            return True
        else:
            print(f"❌ Regions endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Regions test failed: {e}")
        return False

def test_logistics_registration():
    """Test the logistics partner registration endpoint"""
    print("\n🧪 Testing Logistics Partner Registration...")
    
    # Get a sample region first
    try:
        regions_response = requests.get("http://127.0.0.1:8000/api/regions/", timeout=5)
        if regions_response.status_code == 200:
            regions_data = regions_response.json()
            regions = regions_data if isinstance(regions_data, list) else regions_data.get('results', [])
            if not regions:
                print("❌ No regions available for testing")
                return False
            
            # Use first region for test
            test_region = regions[0]
        else:
            print("❌ Could not fetch regions for test")
            return False
    except Exception as e:
        print(f"❌ Error fetching regions: {e}")
        return False
    
    # Test data
    test_data = {
        "name": "Test Logistics Company",
        "description": "A test logistics company for AgriMarket",
        "contact_phone": "+263771234567",
        "email": "<EMAIL>",
        "base_rate": 15.50,
        "website": "https://testlogistics.co.zw",
        "service_regions": [
            {
                "id": test_region["id"],
                "name": test_region["name"],
                "type": test_region["type"]
            }
        ]
    }
    
    # Test the endpoint
    url = "http://127.0.0.1:8000/api/logistics-partners/register/"
    
    print(f"🔗 Testing URL: {url}")
    print(f"📦 Test data:")
    print(f"   Name: {test_data['name']}")
    print(f"   Email: {test_data['email']}")
    print(f"   Phone: {test_data['contact_phone']}")
    print(f"   Regions: {test_data['service_regions']}")
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"📋 Response Data:")
            print(json.dumps(response_data, indent=2))
        except:
            print(f"📋 Response Text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
            return True
        elif response.status_code == 400:
            print("❌ Bad Request - Check the error details above")
            return False
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is the Django server running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 AgriMarket Logistics Registration API Test")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        print("   cd backend")
        print("   python test_logistics_api.py")
        sys.exit(1)
    
    # Test server
    if not test_server_running():
        sys.exit(1)
    
    # Test regions
    if not test_regions_endpoint():
        print("\n💡 To fix regions issue:")
        print("   python create_regions_simple.py")
        sys.exit(1)
    
    # Test logistics registration
    if test_logistics_registration():
        print("\n🎉 All tests passed!")
        print("\n📋 Next Steps:")
        print("1. Test the frontend form: http://127.0.0.1:5173/logistics-register")
        print("2. Fill out the form and submit")
        print("3. Check for success message")
    else:
        print("\n❌ Logistics registration test failed")
        print("\n🔧 Troubleshooting:")
        print("1. Check Django server logs for errors")
        print("2. Verify all required fields are present")
        print("3. Check serializer validation")

if __name__ == "__main__":
    main()
