import React, { useState } from "react";
import axiosInstance from "../api/axiosInstance";
import Navbar from "../components/Navbar";
import { useAuth } from "../api/AuthContext";
import { useNavigate } from "react-router-dom";

function LoginPage() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    if (!role) {
      setError("Please select a role.");
      setLoading(false);
      return;
    }

    if (!username.trim() || !password.trim()) {
      setError("Please enter both username and password.");
      setLoading(false);
      return;
    }

    try {
      const response = await axiosInstance.post("/login/", {
        username: username.trim(),
        password,
        role, // send role to backend for validation
      });

      if (response.data && response.data.token) {
        login(response.data.user || { username }, response.data.token);
        navigate("/");
      } else {
        setError("Login failed: No token returned");
      }
    } catch (err) {
      console.error("Login error:", err);
      console.error("Error details:", {
        message: err.message,
        response: err.response,
        status: err.response?.status,
        data: err.response?.data,
        config: err.config
      });

      if (err.response?.status === 429) {
        setError("Too many login attempts. Please try again later.");
      } else if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else if (err.response?.status === 401) {
        setError("Invalid username or password.");
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError("Unable to connect to server. Please check your internet connection.");
      } else {
        setError("Login failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <div className="container page-content d-flex flex-column align-items-center justify-content-center" style={{ maxWidth: 400, minHeight: 'calc(100vh - 160px)' }}>
        <div className="card shadow p-4 w-100">
          <h2 className="mb-2 text-center text-primary">Login</h2>
          <p className="text-center text-muted mb-4">
            Access your AgriMarket account to buy or sell fresh produce in Zimbabwe
          </p>
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Username</label>
              <input
                type="text"
                className="form-control"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                placeholder="Enter your username"
              />
            </div>
            <div className="mb-3">
              <label className="form-label">Password</label>
              <input
                type="password"
                className="form-control"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="Enter your password"
              />
            </div>
            <div className="mb-3">
              <label className="form-label">Login as</label>
              <select className="form-select" value={role} onChange={e => setRole(e.target.value)} required>
                <option value="">Select role...</option>
                <option value="farmer">Farmer</option>
                <option value="buyer">Buyer</option>
                <option value="admin">Admin</option>
              </select>
              <div className="form-text">
                <small className="text-muted">
                  <strong>Farmer:</strong> Manage your produce listings<br/>
                  <strong>Buyer:</strong> Browse and purchase fresh produce
                </small>
              </div>
            </div>
            {error && (
              <div className="alert alert-danger d-flex align-items-center">
                <span className="material-icons me-2">error</span>
                {error}
              </div>
            )}
            <button
              type="submit"
              className="btn btn-primary w-100 py-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Logging in...
                </>
              ) : (
                'Login'
              )}
            </button>
          </form>
          <div className="text-center mt-3">
            <p className="text-muted">
              Don&apos;t have an account?
              <a href="/register" className="text-decoration-none ms-1">Register here</a>
            </p>
          </div>
          <div className="mt-4 p-3 bg-light rounded">
            <h6 className="text-primary mb-2">Why use AgriMarket?</h6>
            <ul className="list-unstyled small text-muted mb-0">
              <li>✓ Direct connection between farmers and buyers</li>
              <li>✓ Fair pricing and transparent marketplace</li>
              <li>✓ Support local agriculture in Zimbabwe</li>
              <li>✓ Fresh produce from farm to table</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}

export default LoginPage;
