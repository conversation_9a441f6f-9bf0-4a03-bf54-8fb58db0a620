import React from "react";
import { useAuth } from "../api/AuthContext";

function Profile() {
  const { user } = useAuth();

  let role = "";
  if (user) {
    if (user.is_staff) role = "Admin";
    else if (user.is_farmer) role = "Farmer";
    else if (user.is_buyer) role = "Buyer";
  }

  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <h1 className="mb-4 text-primary">My Profile</h1>
        <div className="card p-4 mb-4">
          <h5>
            User: <span className="text-success">@{user?.username || "-"}</span>
          </h5>
          <p>Email: {user?.email || "-"}</p>
          <p>
            Role: <span className="fw-bold">{role || "-"}</span>
          </p>
          <button className="btn btn-outline-primary">Edit Profile</button>
        </div>
        <div className="alert alert-info">
          Profile details and settings coming soon!
        </div>
      </div>
    </>
  );
}

export default Profile;
