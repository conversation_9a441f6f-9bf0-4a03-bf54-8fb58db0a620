#!/usr/bin/env python3
"""
Validate AgriMarket setup and configuration.
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a file exists."""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False

def check_env_file():
    """Check if .env file exists and has required variables."""
    env_path = ".env"
    if not check_file_exists(env_path, ".env file"):
        return False
    
    required_vars = [
        'SECRET_KEY',
        'DEBUG',
        'ALLOWED_HOSTS',
        'CORS_ALLOWED_ORIGINS'
    ]
    
    missing_vars = []
    with open(env_path, 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables present")
        return True

def check_backend_setup():
    """Check backend setup."""
    print("\n🔍 Checking Backend Setup...")
    
    # Check if backend directory exists
    if not check_file_exists("backend", "Backend directory"):
        return False
    
    # Check requirements.txt
    if not check_file_exists("backend/requirements.txt", "Requirements file"):
        return False
    
    # Check if Django is installed
    try:
        os.chdir("backend")
        result = subprocess.run([sys.executable, "-c", "import django; print(django.get_version())"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Django installed: {result.stdout.strip()}")
        else:
            print("❌ Django not installed or not accessible")
            return False
    except Exception as e:
        print(f"❌ Error checking Django: {e}")
        return False
    finally:
        os.chdir("..")
    
    return True

def check_frontend_setup():
    """Check frontend setup."""
    print("\n🔍 Checking Frontend Setup...")
    
    frontend_path = "frontend/agrimarket_frontend"
    
    # Check if frontend directory exists
    if not check_file_exists(frontend_path, "Frontend directory"):
        return False
    
    # Check package.json
    package_json_path = f"{frontend_path}/package.json"
    if not check_file_exists(package_json_path, "package.json"):
        return False
    
    # Check if node_modules exists
    node_modules_path = f"{frontend_path}/node_modules"
    if not check_file_exists(node_modules_path, "node_modules directory"):
        print("⚠️  Run 'npm install' in frontend/agrimarket_frontend")
        return False
    
    return True

def check_database():
    """Check database setup."""
    print("\n🔍 Checking Database Setup...")
    
    db_path = "backend/db.sqlite3"
    if check_file_exists(db_path, "SQLite database"):
        print("✅ Database file exists")
        return True
    else:
        print("⚠️  Run 'python manage.py migrate' in backend directory")
        return False

def main():
    """Main validation function."""
    print("🌾 AgriMarket Setup Validation")
    print("=" * 40)
    
    all_good = True
    
    # Check environment file
    print("\n🔍 Checking Environment Configuration...")
    if not check_env_file():
        all_good = False
        print("💡 Run 'python generate_secret_key.py' to generate a secure secret key")
    
    # Check backend
    if not check_backend_setup():
        all_good = False
    
    # Check frontend
    if not check_frontend_setup():
        all_good = False
    
    # Check database
    if not check_database():
        all_good = False
    
    print("\n" + "=" * 40)
    if all_good:
        print("🎉 All checks passed! Your AgriMarket setup looks good.")
        print("\n📋 Next steps:")
        print("1. Start backend: cd backend && python manage.py runserver")
        print("2. Start frontend: cd frontend/agrimarket_frontend && npm run dev")
    else:
        print("⚠️  Some issues found. Please fix them before running the application.")
        print("\n📋 Common fixes:")
        print("1. Copy .env.example to .env and update values")
        print("2. Run: cd backend && pip install -r requirements.txt")
        print("3. Run: cd backend && python manage.py migrate")
        print("4. Run: cd frontend/agrimarket_frontend && npm install")

if __name__ == "__main__":
    main()
