import React, { useCallback } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../api/AuthContext";

function setTheme(theme) {
  document.documentElement.setAttribute("data-bs-theme", theme);
  localStorage.setItem("agrimarket-theme", theme);
}

function getSavedTheme() {
  return localStorage.getItem("agrimarket-theme") || "light";
}

function Navbar() {
  React.useEffect(() => {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    const handleMouseMove = (e) => {
      const rect = navbar.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      navbar.style.setProperty('--mouse-x', `${x}px`);
      navbar.style.setProperty('--mouse-y', `${y}px`);
      const hoveredLink = e.target.closest('.nav-link');
      if (hoveredLink) {
        const linkRect = hoveredLink.getBoundingClientRect();
        const linkX = e.clientX - linkRect.left;
        const linkY = e.clientY - linkRect.top;
        hoveredLink.style.setProperty('--mouse-x', `${linkX}px`);
        hoveredLink.style.setProperty('--mouse-y', `${linkY}px`);
      }
    };
    const handleMouseLeave = (link) => {
      link.style.removeProperty('--mouse-x');
      link.style.removeProperty('--mouse-y');
    };
    navbar?.addEventListener('mousemove', handleMouseMove);
    navLinks.forEach(link => {
      link.addEventListener('mouseleave', () => handleMouseLeave(link));
    });
    return () => {
      navbar?.removeEventListener('mousemove', handleMouseMove);
      navLinks.forEach(link => {
        link.removeEventListener('mouseleave', () => handleMouseLeave(link));
      });
    };
  }, []);
  React.useEffect(() => {
    setTheme(getSavedTheme());
  }, []);
  const handleThemeChange = useCallback((theme) => {
    setTheme(theme);
  }, []);
  const navigate = useNavigate();
  const { user, logout, hasRole } = useAuth();
  return (
    <nav className="navbar navbar-expand-md fixed-top">
      <div className="container-fluid py-2 px-3 px-md-4">
        {/* Back Button */}
        <button
          className="btn btn-icon btn-outline-light me-3 d-none d-lg-inline-flex align-items-center"
          onClick={() => navigate(-1)}
          aria-label="Go Back"
          type="button"
        >
          <span className="material-icons navbar-icon">arrow_back</span>
        </button>
        {/* Brand - Text and Icon Only */}
        <Link className="navbar-brand d-flex align-items-center me-3 me-md-4" to="/" style={{ minWidth: 0 }}>
          <div className="brand-text d-flex flex-column align-items-start justify-content-center" style={{lineHeight: 1.1, minWidth: 0}}>
            <span className="fw-bold" style={{ fontSize: 26, fontFamily: 'Changa, Segoe UI, Arial, sans-serif', letterSpacing: 0.5 }}>
              <span style={{ color: '#29ab4e', fontWeight: 800, fontFamily: 'inherit', display: 'inline-flex', alignItems: 'center' }}>
                Agri
                <span className="material-icons ms-1 align-middle" style={{ fontSize: 22, color: '#29ab4e', verticalAlign: 'middle' }}>eco</span>
              </span>
              <span style={{ color: '#212529', fontWeight: 800, fontFamily: 'inherit', marginLeft: 2 }}>Market</span>
            </span>
            <span className="d-none d-sm-block" style={{ fontFamily: '"Pacifico", cursive, Arial', fontSize: 15, color: '#7bb661', fontStyle: 'italic', marginTop: -2, marginLeft: 2, letterSpacing: 0.2 }}>
              Fresh From Farm
            </span>
          </div>
        </Link>
        {/* Mobile Toggle */}
        <button 
          className="navbar-toggler border-0 p-1" 
          type="button" 
          data-bs-toggle="collapse" 
          data-bs-target="#navbarNav" 
          aria-controls="navbarNav" 
          aria-expanded="false" 
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon opacity-75"></span>
        </button>
        {/* Navigation Links */}
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav ms-auto gap-1 gap-md-2">

            {/* Marketplace Dropdown */}
            <li className="nav-item dropdown">
              <a
                className="nav-link dropdown-toggle d-flex align-items-center"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span className="material-icons me-2 navbar-icon">store</span>
                Marketplace
              </a>
              <ul className="dropdown-menu">
                <li>
                  <Link to="/produce-explore" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">store</span>
                    Browse Market
                  </Link>
                </li>
                <li>
                  <Link to="/market-prices" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">trending_up</span>
                    Market Prices
                  </Link>
                </li>
                <li>
                  <Link to="/categories" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">category</span>
                    Categories
                  </Link>
                </li>
              </ul>
            </li>
            {/* Farmer Dashboard Dropdown */}
            {user && hasRole("farmer") && (
              <li className="nav-item dropdown">
                <a
                  className="nav-link dropdown-toggle d-flex align-items-center"
                  href="#"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span className="material-icons me-2 navbar-icon">agriculture</span>
                  Farmer Hub
                </a>
                <ul className="dropdown-menu">
                  <li>
                    <Link to="/post-produce" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">add_box</span>
                      Post Produce
                    </Link>
                  </li>
                  <li>
                    <Link to="/my-listings" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">inventory</span>
                      My Listings
                    </Link>
                  </li>
                  <li><hr className="dropdown-divider" /></li>
                  <li>
                    <Link to="/farmer-analytics" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">analytics</span>
                      Analytics
                    </Link>
                  </li>
                </ul>
              </li>
            )}
            {/* Buyer Dashboard Dropdown */}
            {user && hasRole("buyer") && (
              <li className="nav-item dropdown">
                <a
                  className="nav-link dropdown-toggle d-flex align-items-center"
                  href="#"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span className="material-icons me-2 navbar-icon">shopping_bag</span>
                  Buyer Hub
                </a>
                <ul className="dropdown-menu">
                  <li>
                    <Link to="/favorites" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">favorite</span>
                      Favorites
                    </Link>
                  </li>
                  <li>
                    <Link to="/watchlist" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">visibility</span>
                      Watchlist
                    </Link>
                  </li>
                  <li>
                    <Link to="/orders" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">shopping_cart</span>
                      My Orders
                    </Link>
                  </li>
                  <li><hr className="dropdown-divider" /></li>
                  <li>
                    <Link to="/collections" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">collections</span>
                      Collections
                    </Link>
                  </li>
                </ul>
              </li>
            )}

            {/* Logistics Partner Dashboard Dropdown */}
            {user && hasRole("logistics") && (
              <li className="nav-item dropdown">
                <a
                  className="nav-link dropdown-toggle d-flex align-items-center"
                  href="#"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span className="material-icons me-2 navbar-icon">local_shipping</span>
                  Logistics Hub
                </a>
                <ul className="dropdown-menu">
                  <li>
                    <Link to="/logistics-dashboard" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">dashboard</span>
                      Dashboard
                    </Link>
                  </li>
                  <li>
                    <Link to="/my-deliveries" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">local_shipping</span>
                      My Deliveries
                    </Link>
                  </li>
                  <li>
                    <Link to="/delivery-history" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">history</span>
                      Delivery History
                    </Link>
                  </li>
                  <li><hr className="dropdown-divider" /></li>
                  <li>
                    <Link to="/logistics-profile" className="dropdown-item d-flex align-items-center">
                      <span className="material-icons me-2">business</span>
                      Business Profile
                    </Link>
                  </li>
                </ul>
              </li>
            )}




            {/* Services Dropdown */}
            <li className="nav-item dropdown">
              <a
                className="nav-link dropdown-toggle d-flex align-items-center"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span className="material-icons me-2 navbar-icon">business</span>
                Services
              </a>
              <ul className="dropdown-menu">
                <li>
                  <Link to="/logistics-register" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">local_shipping</span>
                    Logistics Registration
                  </Link>
                </li>
                <li>
                  <Link to="/logistics-login" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">login</span>
                    Logistics Partner Login
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">contact_support</span>
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link to="/about" className="dropdown-item d-flex align-items-center">
                    <span className="material-icons me-2">info</span>
                    About Us
                  </Link>
                </li>
                {user && user.is_superuser && (
                  <>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <Link to="/superuser-admin-management" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">supervisor_account</span>
                        Manage Admins
                      </Link>
                    </li>
                  </>
                )}
              </ul>
            </li>
            {/* User Account Section */}
            {user && (
              <>
                {/* User Account Dropdown */}
                <li className="nav-item dropdown">
                  <a 
                    className="nav-link dropdown-toggle d-flex align-items-center" 
                    href="#" 
                    role="button" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                  >
                    <span className="material-icons me-2 navbar-icon">person</span>
                    {user.username || "Account"}
                    <span className={`badge ms-2 ${user.is_superuser ? 'bg-danger' : 'bg-primary'}`}>
                      {user.is_superuser ? "Superuser" : hasRole("admin") ? "Admin" : hasRole("farmer") ? "Farmer" : hasRole("buyer") ? "Buyer" : "User"}
                    </span>
                  </a>
                  <ul className="dropdown-menu dropdown-menu-end">
                    <li>
                      <Link to="/profile" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">account_circle</span>
                        Profile
                      </Link>
                    </li>
                    <li>
                      <Link to="/settings" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">settings</span>
                        Settings
                      </Link>
                    </li>
                    {hasRole("admin") && (
                      <li>
                        <Link to="/comprehensive-admin" className="dropdown-item d-flex align-items-center">
                          <span className="material-icons me-2">dashboard</span>
                          Admin Dashboard
                        </Link>
                      </li>
                    )}
                    <li>
                      <Link to="/activity" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">history</span>
                        Activity
                      </Link>
                    </li>
                    <li>
                      <Link to="/messages" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">message</span>
                        Messages
                      </Link>
                    </li>
                    <li>
                      <Link to="/notifications" className="dropdown-item d-flex align-items-center">
                        <span className="material-icons me-2">notifications</span>
                        Notifications
                      </Link>
                    </li>
                    <li><hr className="dropdown-divider" /></li>
                    <li>
                      <button 
                        className="dropdown-item d-flex align-items-center w-100 border-0 bg-transparent" 
                        onClick={logout}
                      >
                        <span className="material-icons me-2">logout</span>
                        Logout
                      </button>
                    </li>
                  </ul>
                </li>
              </>
            )}
            {/* Guest-only Links */}
            {!user && (
              <>
                <li className="nav-item">
                  <Link to="/login" className="nav-link d-flex align-items-center">
                    <span className="material-icons me-2 navbar-icon">login</span>
                    Login
                  </Link>
                </li>
                <li className="nav-item">
                  <Link to="/register" className="nav-link d-flex align-items-center">
                    <span className="material-icons me-2 navbar-icon">person_add</span>
                    Sign Up
                  </Link>
                </li>
              </>
            )}

            {/* About Link - Always visible */}
            <li className="nav-item">
              <Link to="/about" className="nav-link d-flex align-items-center">
                <span className="material-icons me-2 navbar-icon">info</span>
                About
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
