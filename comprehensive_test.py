#!/usr/bin/env python3
"""
Comprehensive test script for AgriMarket
Tests backend, frontend, and integration
"""

import os
import sys
import requests
import subprocess
import time
import json
from pathlib import Path

def test_backend_health():
    """Test if backend is running and healthy"""
    print("🔍 Testing Backend Health...")
    
    endpoints = [
        ("http://127.0.0.1:8000/admin/", "Django Admin"),
        ("http://127.0.0.1:8000/api/categories/", "Categories API"),
        ("http://127.0.0.1:8000/api/produce/", "Produce API"),
        ("http://127.0.0.1:8000/api/market-prices/", "Market Prices API"),
        ("http://127.0.0.1:8000/swagger/", "API Documentation"),
    ]
    
    results = []
    for url, name in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code in [200, 302]:  # 302 for admin redirect
                print(f"✅ {name}: OK ({response.status_code})")
                results.append(True)
            else:
                print(f"⚠️  {name}: HTTP {response.status_code}")
                results.append(False)
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Connection failed")
            results.append(False)
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
            results.append(False)
    
    return all(results)

def test_api_endpoints():
    """Test API endpoints with actual requests"""
    print("\n🔍 Testing API Endpoints...")
    
    # Test categories
    try:
        response = requests.get("http://127.0.0.1:8000/api/categories/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            categories = data if isinstance(data, list) else data.get('results', [])
            print(f"✅ Categories: {len(categories)} found")
            if categories:
                print(f"   Sample: {categories[0].get('name', 'Unknown')}")
        else:
            print(f"⚠️  Categories: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Categories: {e}")
    
    # Test produce
    try:
        response = requests.get("http://127.0.0.1:8000/api/produce/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            produce = data if isinstance(data, list) else data.get('results', [])
            print(f"✅ Produce: {len(produce)} found")
        else:
            print(f"⚠️  Produce: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Produce: {e}")
    
    # Test authentication endpoints
    auth_endpoints = [
        "/api/register/",
        "/api/login/",
    ]
    
    for endpoint in auth_endpoints:
        try:
            # Test with OPTIONS to check if endpoint exists
            response = requests.options(f"http://127.0.0.1:8000{endpoint}", timeout=5)
            if response.status_code in [200, 405]:  # 405 is OK for OPTIONS
                print(f"✅ {endpoint}: Available")
            else:
                print(f"⚠️  {endpoint}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def test_frontend_health():
    """Test if frontend is accessible"""
    print("\n🔍 Testing Frontend Health...")
    
    try:
        response = requests.get("http://127.0.0.1:5173", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend: Accessible")
            return True
        else:
            print(f"⚠️  Frontend: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend: Not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Frontend: Error - {e}")
        return False

def test_database_integrity():
    """Test database integrity"""
    print("\n🔍 Testing Database Integrity...")
    
    # Check if we can import Django models
    try:
        sys.path.insert(0, str(Path("backend").absolute()))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
        
        import django
        django.setup()
        
        from core.models import User, ProduceCategory, Produce
        
        # Test basic queries
        user_count = User.objects.count()
        category_count = ProduceCategory.objects.count()
        produce_count = Produce.objects.count()
        
        print(f"✅ Database accessible")
        print(f"   Users: {user_count}")
        print(f"   Categories: {category_count}")
        print(f"   Produce: {produce_count}")
        
        # Test admin user exists
        admin_exists = User.objects.filter(is_superuser=True).exists()
        if admin_exists:
            print("✅ Admin user exists")
        else:
            print("⚠️  No admin user found")
        
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🔍 Testing CORS Configuration...")
    
    try:
        # Test preflight request
        headers = {
            'Origin': 'http://127.0.0.1:5173',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        
        response = requests.options("http://127.0.0.1:8000/api/categories/", 
                                  headers=headers, timeout=5)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        if any(cors_headers.values()):
            print("✅ CORS headers present")
            for header, value in cors_headers.items():
                if value:
                    print(f"   {header}: {value}")
        else:
            print("⚠️  CORS headers missing")
        
        return True
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False

def test_file_structure():
    """Test project file structure"""
    print("\n🔍 Testing File Structure...")
    
    required_files = [
        "backend/manage.py",
        "backend/agrimarket_backend/settings.py",
        "backend/core/models.py",
        "backend/core/views.py",
        "backend/core/urls.py",
        "backend/requirements.txt",
        "frontend/agrimarket_frontend/package.json",
        "frontend/agrimarket_frontend/src/App.jsx",
        "frontend/agrimarket_frontend/src/api/axiosInstance.js",
        ".env",
    ]
    
    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 COMPREHENSIVE AGRIMARKET TEST")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Database Integrity", test_database_integrity),
        ("Backend Health", test_backend_health),
        ("API Endpoints", test_api_endpoints),
        ("CORS Configuration", test_cors_configuration),
        ("Frontend Health", test_frontend_health),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("Your AgriMarket project is working correctly!")
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed.")
        print("Please check the issues above and fix them.")
    
    return passed == len(results)

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("AgriMarket Comprehensive Test Script")
        print("Usage: python comprehensive_test.py")
        print("\nThis script tests:")
        print("- File structure integrity")
        print("- Database connectivity and data")
        print("- Backend API endpoints")
        print("- Frontend accessibility")
        print("- CORS configuration")
        print("\nMake sure both backend and frontend are running before testing.")
        return
    
    print("🌾 AgriMarket Comprehensive Test")
    print("Make sure both backend and frontend are running!")
    print("Backend: http://127.0.0.1:8000")
    print("Frontend: http://127.0.0.1:5173")
    print()
    
    input("Press Enter to continue with testing...")
    
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
