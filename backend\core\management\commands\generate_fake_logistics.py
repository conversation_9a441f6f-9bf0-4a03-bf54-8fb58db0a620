from django.core.management.base import BaseCommand
from faker import Faker
from core.models import LogisticsPartner, Region
import random

class Command(BaseCommand):
    help = 'Generate fake logistics partner data for testing purposes'

    def add_arguments(self, parser):
        parser.add_argument('count', type=int, help='Number of fake logistics partners to generate', nargs='?', default=10)

    def handle(self, *args, **options):
        fake = Faker()
        count = options['count']
        regions = list(Region.objects.all())
        
        if not regions:
            self.stdout.write(self.style.ERROR('No regions found in the database. Please load region data first.'))
            return

        created_count = 0
        for _ in range(count):
            # Generate fake data for logistics partner
            name = fake.company() + " Logistics"
            description = fake.paragraph(nb_sentences=3)
            contact_phone = fake.phone_number()
            email = fake.company_email()
            base_rate = round(random.uniform(5.0, 50.0), 2)
            website = fake.url()
            
            # Randomly select some regions for the partner to serve
            num_regions = random.randint(1, min(5, len(regions)))
            service_regions = random.sample(regions, num_regions)
            
            # Create the logistics partner
            partner = LogisticsPartner.objects.create(
                name=name,
                description=description,
                contact_phone=contact_phone,
                email=email,
                base_rate=base_rate,
                website=website,
                is_verified=True  # Set to True for testing visibility in frontend
            )
            partner.service_regions.set(service_regions)
            created_count += 1
            
            self.stdout.write(self.style.SUCCESS(f'Created logistics partner: {name} serving {num_regions} region(s)'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} fake logistics partners'))
