# Role-Based Navigation System

## Overview
The AgriMarket navbar now implements role-based navigation that shows/hides links based on user roles and authentication status.

## Navigation Structure

### 🌐 Public Links (Always Visible)
Available to all users regardless of authentication status:
- **Browse Market** - View all available produce
- **Market Prices** - Check current market pricing
- **Categories** - Browse produce by category
- **About** - Information about AgriMarket

### 👨‍🌾 Farmer-Only Links
Visible only to users with `is_farmer: true`:
- **Post Produce** - Add new produce listings
- **My Listings** - Manage existing produce listings

### 🛒 Buyer-Only Links
Visible only to users with `is_buyer: true`:
- **Favorites** - View saved favorite items
- **Watchlist** - Monitor items of interest

### 👑 Admin-Only Links
Visible only to users with `is_staff: true`:
- **Admin Dashboard** - Administrative controls and oversight

### 🔐 Authenticated User Links
Visible to all logged-in users regardless of role:
- **Collections** - User's saved collections
- **Activity** - User activity history
- **Account Dropdown** - Profile, settings, and logout

### 👤 Guest-Only Links
Visible only to non-authenticated users:
- **Login** - User authentication
- **Sign Up** - New user registration

## User Account Dropdown

The account dropdown includes:
- **Role Badge** - Shows current user role (Admin/Farmer/Buyer/User)
- **Profile** - User profile management
- **Settings** - Account settings
- **Role-specific links** - Additional links based on user role
- **Logout** - Sign out functionality

## Role Detection

The system uses the `hasRole()` function from AuthContext:
```javascript
const { user, hasRole } = useAuth();

// Check roles
hasRole("admin")  // Returns true if user.is_staff === true
hasRole("farmer") // Returns true if user.is_farmer === true
hasRole("buyer")  // Returns true if user.is_buyer === true
```

## Implementation Examples

### Farmer Navigation
```jsx
{user && hasRole("farmer") && (
  <>
    <li className="nav-item">
      <Link to="/post-produce">Post Produce</Link>
    </li>
    <li className="nav-item">
      <Link to="/my-listings">My Listings</Link>
    </li>
  </>
)}
```

### Buyer Navigation
```jsx
{user && hasRole("buyer") && (
  <>
    <li className="nav-item">
      <Link to="/favorites">Favorites</Link>
    </li>
    <li className="nav-item">
      <Link to="/watchlist">Watchlist</Link>
    </li>
  </>
)}
```

### Admin Navigation
```jsx
{user && hasRole("admin") && (
  <li className="nav-item">
    <Link to="/admin-dashboard">Admin Dashboard</Link>
  </li>
)}
```

## Benefits

1. **Security** - Users only see links they have permission to access
2. **User Experience** - Clean, relevant navigation for each user type
3. **Role Clarity** - Clear indication of user role with badges
4. **Responsive Design** - Works on both desktop and mobile
5. **Maintainable** - Easy to add/remove role-specific features

## Testing Different Roles

To test the navigation system:

1. **As Guest**: Only see public links + Login/Sign Up
2. **As Farmer**: See public links + Post Produce + My Listings + authenticated user features
3. **As Buyer**: See public links + Favorites + Watchlist + authenticated user features
4. **As Admin**: See all links including Admin Dashboard

## Future Enhancements

- **Multi-role Support**: Users with multiple roles (e.g., farmer + buyer)
- **Permission-based Links**: More granular permissions beyond basic roles
- **Dynamic Menu**: Server-driven navigation configuration
- **Role Switching**: Allow users to switch between roles if they have multiple