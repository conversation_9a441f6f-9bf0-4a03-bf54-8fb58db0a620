from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    User, Produce, MarketPrice, SMSAlert, ContactRequest, PriceSource, ProduceCategory,
    Collection, Favorite, Watchlist, UserProfile, Region, Order, LogisticsPartner,
    OrderLogistics, LogisticsPartnerReview
)

# Custom User Admin
class CustomUserAdmin(UserAdmin):
    model = User
    list_display = ('username', 'email', 'is_farmer', 'is_buyer', 'is_staff', 'is_active', 'date_joined')
    list_filter = ('is_farmer', 'is_buyer', 'is_staff', 'is_active')
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email', 'phone', 'location')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'is_farmer', 'is_buyer', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'is_farmer', 'is_buyer', 'is_staff', 'is_active'),
        }),
    )
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('username',)

# Inline for OrderLogistics to show in Order admin
class OrderLogisticsInline(admin.StackedInline):
    model = OrderLogistics
    can_delete = False
    verbose_name_plural = 'Logistics Details'
    fk_name = 'order'
    extra = 0

# Admin for Order with inline OrderLogistics
@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'buyer', 'farmer', 'produce', 'quantity', 'total_price', 'order_date', 'status', 'updated_at')
    list_filter = ('status', 'order_date', 'updated_at', 'region')
    search_fields = ('buyer__username', 'farmer__username', 'produce__name', 'delivery_location')
    inlines = (OrderLogisticsInline,)
    readonly_fields = ('order_date', 'updated_at')

# Admin for Produce
@admin.register(Produce)
class ProduceAdmin(admin.ModelAdmin):
    list_display = ('name', 'farmer', 'category', 'price', 'unit', 'quantity_available', 'approved', 'created_at', 'updated_at')
    list_filter = ('approved', 'is_organic', 'category', 'created_at', 'updated_at')
    search_fields = ('name', 'description', 'farmer__username')
    readonly_fields = ('created_at', 'updated_at')

# Admin for MarketPrice
@admin.register(MarketPrice)
class MarketPriceAdmin(admin.ModelAdmin):
    list_display = ('crop_name', 'price', 'source', 'date', 'approved')
    list_filter = ('approved', 'date')
    search_fields = ('crop_name', 'source')

# Admin for SMSAlert
@admin.register(SMSAlert)
class SMSAlertAdmin(admin.ModelAdmin):
    list_display = ('user', 'message', 'sent_at', 'delivered')
    list_filter = ('delivered', 'sent_at')
    search_fields = ('message', 'user__username')
    readonly_fields = ('sent_at',)

# Admin for ContactRequest
@admin.register(ContactRequest)
class ContactRequestAdmin(admin.ModelAdmin):
    list_display = ('buyer', 'produce', 'created_at', 'status')
    list_filter = ('status', 'created_at')
    search_fields = ('buyer__username', 'produce__name', 'message')
    readonly_fields = ('created_at',)

# Admin for PriceSource
@admin.register(PriceSource)
class PriceSourceAdmin(admin.ModelAdmin):
    list_display = ('name', 'contact')
    search_fields = ('name', 'description', 'contact')

# Admin for ProduceCategory
@admin.register(ProduceCategory)
class ProduceCategoryAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name', 'description')

# Admin for Collection
@admin.register(Collection)
class CollectionAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'verified', 'created_at')
    list_filter = ('verified', 'created_at')
    search_fields = ('name', 'description', 'owner__username')
    readonly_fields = ('created_at',)

# Admin for Favorite
@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'produce', 'collection', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username',)
    readonly_fields = ('created_at',)

# Admin for Watchlist
@admin.register(Watchlist)
class WatchlistAdmin(admin.ModelAdmin):
    list_display = ('user', 'produce', 'collection', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username',)
    readonly_fields = ('created_at',)

# Admin for UserProfile
@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'location')
    search_fields = ('user__username', 'bio', 'location')

# Admin for Region
@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'parent_region')
    list_filter = ('type',)
    search_fields = ('name',)

# Admin for LogisticsPartner
@admin.register(LogisticsPartner)
class LogisticsPartnerAdmin(admin.ModelAdmin):
    list_display = ('name', 'contact_phone', 'email', 'base_rate', 'is_verified', 'registration_date')
    list_filter = ('is_verified', 'registration_date')
    search_fields = ('name', 'description', 'email', 'contact_phone')
    filter_horizontal = ('service_regions',)
    readonly_fields = ('registration_date',)

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion if the partner is assigned to orders
        if obj and obj.assigned_orders.exists():
            return False
        return super().has_delete_permission(request, obj)

# Admin for OrderLogistics
@admin.register(OrderLogistics)
class OrderLogisticsAdmin(admin.ModelAdmin):
    list_display = ('order', 'logistics_partner', 'selection_date', 'estimated_delivery', 'tracking_number', 'cost', 'updated_at')
    list_filter = ('selection_date', 'updated_at', 'estimated_delivery')
    search_fields = ('order__id', 'logistics_partner__name', 'tracking_number')
    readonly_fields = ('selection_date', 'updated_at')

# Admin for LogisticsPartnerReview
@admin.register(LogisticsPartnerReview)
class LogisticsPartnerReviewAdmin(admin.ModelAdmin):
    list_display = ('logistics_partner', 'buyer', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('logistics_partner__name', 'buyer__username', 'review_text')
    readonly_fields = ('created_at',)

# Register User with CustomUserAdmin
admin.site.register(User, CustomUserAdmin)
