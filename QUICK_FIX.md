# 🚀 Quick Fix for 404 Error

## The Problem
You're getting a 404 error because the Django backend isn't properly set up. The most likely cause is that database migrations haven't been run.

## 🔧 Solution (Step by Step)

### Step 1: Fix the Backend
Since you're already in the backend directory with the virtual environment activated, run these commands:

```bash
# You should be in: D:\My Web Projects\AgriMarket\backend
# With (myenv) showing in your prompt

# 1. Install requirements (if not done)
pip install -r requirements.txt

# 2. Create and run migrations
python manage.py makemigrations
python manage.py migrate

# 3. Create some initial data
python manage.py shell
```

In the Django shell, run:
```python
from core.models import ProduceCategory

# Create basic categories
categories = [
    {"name": "Vegetables", "description": "Fresh vegetables"},
    {"name": "Fruits", "description": "Fresh fruits"},
    {"name": "Grains", "description": "Cereals and grains"},
]

for cat_data in categories:
    category, created = ProduceCategory.objects.get_or_create(
        name=cat_data["name"],
        defaults={"description": cat_data["description"]}
    )
    print(f"{'Created' if created else 'Found'}: {category.name}")

exit()
```

### Step 2: Start the Server
```bash
python manage.py runserver 127.0.0.1:8000
```

### Step 3: Test the Backend
Open your browser and go to: http://127.0.0.1:8000/api/categories/

You should see JSON data like:
```json
[
  {
    "id": 1,
    "name": "Vegetables",
    "description": "Fresh vegetables",
    "icon": ""
  },
  ...
]
```

### Step 4: Start the Frontend
Open a NEW terminal and run:
```bash
cd "D:\My Web Projects\AgriMarket\frontend\agrimarket_frontend"
npm run dev
```

## 🎯 Alternative: Automated Fix

If you want to automate the above steps, go back to the root directory and run:

```bash
cd ..
python fix_backend.py
```

## 🧪 Quick Test

After the backend is running, test these URLs in your browser:

1. **Categories**: http://127.0.0.1:8000/api/categories/
2. **Admin**: http://127.0.0.1:8000/admin/
3. **API Root**: http://127.0.0.1:8000/api/

All should return data or show pages (not 404 errors).

## 🔍 Troubleshooting

### If you still get 404 errors:

1. **Check Django is running**:
   ```bash
   python manage.py check
   ```

2. **Check URL patterns**:
   ```bash
   python manage.py show_urls
   ```

3. **Check for errors in the Django terminal**

### If migrations fail:

1. **Delete migration files** (except `__init__.py`):
   ```bash
   # In backend/core/migrations/
   # Keep only __init__.py, delete other .py files
   ```

2. **Reset migrations**:
   ```bash
   python manage.py makemigrations core
   python manage.py migrate
   ```

## ✅ Expected Result

After following these steps:
- ✅ Backend runs without errors on http://127.0.0.1:8000
- ✅ http://127.0.0.1:8000/api/categories/ shows JSON data
- ✅ Frontend loads without "Unable to connect to server" error
- ✅ You can access the AgriMarket homepage

## 🆘 Still Having Issues?

If you're still getting 404 errors after following these steps:

1. **Share the Django terminal output** - Look for any error messages
2. **Check the browser Network tab** (F12 → Network) to see the exact request being made
3. **Try accessing http://127.0.0.1:8000/admin/** - This should always work if Django is running

The key is making sure the database migrations are run and some initial data exists!
