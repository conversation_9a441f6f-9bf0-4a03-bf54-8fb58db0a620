#!/usr/bin/env python
"""
Backend setup script for AgriMarket
This script sets up the backend with sample data for testing.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_backend():
    """Set up the backend with sample data."""
    print("🌾 Setting up AgriMarket Backend...")
    
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()
    
    print("✅ Django environment set up")
    
    # Run migrations
    print("📦 Running database migrations...")
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrations completed")
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    
    # Create sample data
    print("🌱 Creating sample data...")
    try:
        execute_from_command_line(['manage.py', 'create_sample_data'])
        print("✅ Sample data created")
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False
    
    print("\n🎉 Backend setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Start the backend server: python manage.py runserver")
    print("2. Visit the admin panel: http://127.0.0.1:8000/admin")
    print("3. API endpoints: http://127.0.0.1:8000/api")
    print("\n👤 Sample users created:")
    print("- farmer1 / password123 (Farmer)")
    print("- farmer2 / password123 (Farmer)")
    print("\n🥕 Sample produce items have been added to test the frontend!")
    
    return True

if __name__ == "__main__":
    setup_backend()