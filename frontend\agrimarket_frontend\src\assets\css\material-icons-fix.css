/* Material Icons Fix - Override any global font-family that might affect Material Icons */

/* Primary Material Icons styling with highest specificity */
body .material-icons,
html .material-icons,
.material-icons,
span.material-icons,
i.material-icons {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-wrap: normal !important;
  white-space: nowrap !important;
  direction: ltr !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
  -webkit-font-feature-settings: 'liga' !important;
  -moz-font-feature-settings: 'liga' !important;
  display: inline-block !important;
  line-height: 1 !important;
  font-size: 24px !important;
  vertical-align: middle !important;
}

/* Ensure Material Icons work in all contexts */
[class*="material-icons"] {
  font-family: 'Material Icons' !important;
}

/* Ensure Material Icons override any inherited font styles */
* .material-icons,
*:before .material-icons,
*:after .material-icons {
  font-family: 'Material Icons' !important;
}

/* Override Changa font specifically for Material Icons */
.material-icons {
  font-family: 'Material Icons' !important;
}

/* Navbar specific Material Icons */
.navbar .material-icons,
.navbar .navbar-icon {
  font-family: 'Material Icons' !important;
  font-size: 20px !important;
}

/* Button specific Material Icons */
.btn .material-icons {
  font-family: 'Material Icons' !important;
  font-size: 18px !important;
  margin-right: 0.5rem !important;
}

/* Hero section Material Icons */
.hero-section .material-icons {
  font-family: 'Material Icons' !important;
  font-size: 48px !important;
}