#!/usr/bin/env python3
"""
Create regions data for Zimbabwe in AgriMarket
"""

import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()

def create_zimbabwe_regions():
    """Create regions and cities for Zimbabwe"""
    from core.models import Region
    
    print("🌍 Creating Zimbabwe Regions and Cities...")
    
    # Zimbabwe provinces and major cities
    zimbabwe_data = {
        "Harare": {
            "type": "region",
            "cities": ["Harare", "Chitungwiza", "Epworth", "Ruwa"]
        },
        "Bulawayo": {
            "type": "region", 
            "cities": ["Bulawayo"]
        },
        "Manicaland": {
            "type": "region",
            "cities": ["Mutare", "Rusape", "Chipinge", "Nyanga", "Makoni"]
        },
        "Mashonaland Central": {
            "type": "region",
            "cities": ["Bindura", "Shamva", "Mount Darwin", "Guruve", "Centenary"]
        },
        "Mashonaland East": {
            "type": "region", 
            "cities": ["Marondera", "Macheke", "Wedza", "Hwedza", "Seke"]
        },
        "Mashonaland West": {
            "type": "region",
            "cities": ["Chinhoyi", "Kariba", "Makonde", "Zvimba", "Hurungwe"]
        },
        "Masvingo": {
            "type": "region",
            "cities": ["Masvingo", "Chiredzi", "Zaka", "Bikita", "Gutu"]
        },
        "Matabeleland North": {
            "type": "region",
            "cities": ["Victoria Falls", "Hwange", "Binga", "Lupane", "Tsholotsho"]
        },
        "Matabeleland South": {
            "type": "region",
            "cities": ["Gwanda", "Beitbridge", "Plumtree", "Filabusi", "Esigodini"]
        },
        "Midlands": {
            "type": "region",
            "cities": ["Gweru", "Kwekwe", "Redcliff", "Shurugwi", "Gokwe"]
        }
    }
    
    created_regions = 0
    created_cities = 0
    
    for province_name, province_data in zimbabwe_data.items():
        # Create or get the province
        province, created = Region.objects.get_or_create(
            name=province_name,
            type='region',
            defaults={'parent_region': None}
        )
        
        if created:
            print(f"✅ Created province: {province_name}")
            created_regions += 1
        else:
            print(f"📋 Province already exists: {province_name}")
        
        # Create cities for this province
        for city_name in province_data['cities']:
            city, created = Region.objects.get_or_create(
                name=city_name,
                type='city',
                defaults={'parent_region': province}
            )
            
            if created:
                print(f"  ✅ Created city: {city_name}")
                created_cities += 1
            else:
                print(f"  📋 City already exists: {city_name}")
    
    print(f"\n📊 Summary:")
    print(f"   Provinces created: {created_regions}")
    print(f"   Cities created: {created_cities}")
    print(f"   Total regions in database: {Region.objects.count()}")
    
    return True

def test_regions_api():
    """Test if regions can be accessed via API"""
    print("\n🧪 Testing Regions API...")
    
    from core.models import Region
    from core.serializers import RegionSerializer
    
    # Get all regions
    regions = Region.objects.all()
    print(f"Total regions in database: {regions.count()}")
    
    if regions.exists():
        # Test serialization
        serializer = RegionSerializer(regions, many=True)
        print(f"✅ Regions can be serialized")
        print(f"Sample region: {serializer.data[0] if serializer.data else 'None'}")
        return True
    else:
        print("❌ No regions found in database")
        return False

def main():
    """Main function"""
    print("🌍 Zimbabwe Regions Setup for AgriMarket")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("manage.py"):
        print("❌ Please run this script from the backend directory")
        sys.exit(1)
    
    # Setup Django
    setup_django()
    
    # Create regions
    if create_zimbabwe_regions():
        print("✅ Regions created successfully!")
    else:
        print("❌ Failed to create regions")
        sys.exit(1)
    
    # Test API
    if test_regions_api():
        print("✅ Regions API test successful!")
    else:
        print("❌ Regions API test failed")
        sys.exit(1)
    
    print("\n🎉 Zimbabwe regions setup complete!")
    print("\n📋 Next steps:")
    print("1. Restart your Django server if it's running")
    print("2. Test the regions endpoint: http://127.0.0.1:8000/api/regions/")
    print("3. The LogisticsRegister page should now work properly")

if __name__ == "__main__":
    main()
