from django.core.management.base import BaseCommand
from faker import Faker
from core.models import (
    Produce, ProduceCategory, MarketPrice, UserProfile, Region,
    Order, LogisticsPartner, OrderLogistics, LogisticsPartnerReview,
    Collection, Favorite, Watchlist, PriceSource, SMSAlert, ContactRequest
)
from django.contrib.auth import get_user_model
User = get_user_model()

import random
from datetime import datetime, timedelta

class Command(BaseCommand):
    help = 'Generate fake data for all models for testing purposes'

    def add_arguments(self, parser):
        parser.add_argument('count', type=int, help='Number of fake entries to generate per model', nargs='?', default=5)

    def handle(self, *args, **options):
        fake = Faker()
        count = options['count']
        self.stdout.write(self.style.NOTICE('Generating fake data for all models...'))

        # Generate fake users (farmers, buyers, admins)
        farmers = []
        buyers = []
        admins = []
        for i in range(count):
            # Farmer
            farmer_username = f"farmer_{i+1}"
            farmer_email = fake.email()
            farmer = User.objects.create_user(
                username=farmer_username,
                email=farmer_email,
                password='password123',
                is_staff=False
            )
            farmer.is_farmer = True
            farmer.save()
            farmers.append(farmer)
            UserProfile.objects.create(
                user=farmer,
                bio=fake.paragraph(nb_sentences=2),
                phone_number=fake.phone_number(),
                address=fake.address(),
                role='farmer'
            )
            
            # Buyer
            buyer_username = f"buyer_{i+1}"
            buyer_email = fake.email()
            buyer = User.objects.create_user(
                username=buyer_username,
                email=buyer_email,
                password='password123',
                is_staff=False
            )
            buyer.is_buyer = True
            buyer.save()
            buyers.append(buyer)
            UserProfile.objects.create(
                user=buyer,
                bio=fake.paragraph(nb_sentences=2),
                phone_number=fake.phone_number(),
                address=fake.address(),
                role='buyer'
            )
            
            # Admin (only one admin for simplicity)
            if i == 0:
                admin_username = "admin_user"
                admin_email = fake.email()
                admin = User.objects.create_user(
                    username=admin_username,
                    email=admin_email,
                    password='password123',
                    is_staff=True,
                    is_superuser=True
                )
                admins.append(admin)
                UserProfile.objects.create(
                    user=admin,
                    bio=fake.paragraph(nb_sentences=2),
                    phone_number=fake.phone_number(),
                    address=fake.address(),
                    role='admin'
                )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(farmers)} farmers, {len(buyers)} buyers, and {len(admins)} admin(s)'))

        # Generate fake produce categories
        categories = []
        category_names = ['Fruits', 'Vegetables', 'Grains', 'Dairy', 'Meat']
        for name in category_names:
            category = ProduceCategory.objects.create(name=name, description=fake.sentence())
            categories.append(category)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(categories)} produce categories'))

        # Generate fake produce
        produces = []
        for i in range(count * 2):
            produce = Produce.objects.create(
                name=fake.word().capitalize() + " " + random.choice(['Apple', 'Carrot', 'Wheat', 'Milk', 'Beef']),
                description=fake.paragraph(nb_sentences=3),
                category=random.choice(categories),
                farmer=random.choice(farmers),
                price=round(random.uniform(1.0, 20.0), 2),
                quantity_available=random.randint(10, 100),
                unit='kg',
                approved=True,
                harvest_date=fake.date_this_year(),
                location=fake.city()
            )
            produces.append(produce)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(produces)} produce items'))

        # Generate fake market prices
        market_prices = []
        for produce in produces:
            for _ in range(random.randint(1, 3)):
                market_price = MarketPrice.objects.create(
                    produce=produce,
                    price=round(random.uniform(produce.price * 0.8, produce.price * 1.2), 2),
                    market_name=fake.company() + " Market",
                    location=fake.city(),
                    date_reported=fake.date_this_month(),
                    approved=True
                )
                market_prices.append(market_price)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(market_prices)} market prices'))

        # Generate fake price sources
        price_sources = []
        for i in range(count):
            source = PriceSource.objects.create(
                name=fake.company() + " Price Source",
                url=fake.url(),
                description=fake.sentence()
            )
            price_sources.append(source)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(price_sources)} price sources'))

        # Generate fake regions (if not already loaded)
        regions = list(Region.objects.all())
        if not regions:
            self.stdout.write(self.style.ERROR('No regions found in the database. Please load region data first.'))
            return
        else:
            self.stdout.write(self.style.SUCCESS(f'Found {len(regions)} regions in the database'))

        # Generate fake logistics partners
        logistics_partners = []
        for i in range(count):
            name = fake.company() + " Logistics"
            num_regions = random.randint(1, min(5, len(regions)))
            service_regions = random.sample(regions, num_regions)
            partner = LogisticsPartner.objects.create(
                name=name,
                description=fake.paragraph(nb_sentences=3),
                contact_phone=fake.phone_number(),
                email=fake.company_email(),
                base_rate=round(random.uniform(5.0, 50.0), 2),
                website=fake.url(),
                is_verified=True
            )
            partner.service_regions.set(service_regions)
            logistics_partners.append(partner)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(logistics_partners)} logistics partners'))

        # Generate fake orders
        orders = []
        for i in range(count * 2):
            order = Order.objects.create(
                buyer=random.choice(buyers),
                farmer=random.choice(farmers),
                produce=random.choice(produces),
                quantity=random.randint(1, 10),
                total_price=round(random.uniform(10.0, 200.0), 2),
                status=random.choice(['pending', 'confirmed', 'shipped', 'delivered', 'logistics_pending', 'logistics_assigned']),
                order_date=fake.date_time_this_month(),
                region=random.choice(regions)
            )
            orders.append(order)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(orders)} orders'))

        # Generate fake order logistics
        order_logistics = []
        for order in orders:
            if order.status in ['logistics_assigned', 'shipped', 'delivered']:
                logistics = OrderLogistics.objects.create(
                    order=order,
                    logistics_partner=random.choice(logistics_partners),
                    tracking_number=fake.uuid4()[:8],
                    estimated_delivery_date=fake.date_time_between(start_date='+1d', end_date='+7d'),
                    actual_delivery_date=fake.date_time_this_month() if order.status == 'delivered' else None,
                    cost=round(random.uniform(5.0, 50.0), 2)
                )
                order_logistics.append(logistics)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(order_logistics)} order logistics entries'))

        # Generate fake logistics partner reviews
        reviews = []
        for order in orders:
            if order.status == 'delivered':
                review = LogisticsPartnerReview.objects.create(
                    logistics_partner=order.orderlogistics.logistics_partner if hasattr(order, 'orderlogistics') else random.choice(logistics_partners),
                    buyer=order.buyer,
                    rating=random.randint(1, 5),
                    comment=fake.paragraph(nb_sentences=2),
                    created_at=fake.date_time_this_month()
                )
                reviews.append(review)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(reviews)} logistics partner reviews'))

        # Generate fake collections
        collections = []
        for buyer in buyers:
            for _ in range(random.randint(0, 2)):
                collection = Collection.objects.create(
                    owner=buyer,
                    name=fake.word().capitalize() + " Collection",
                    description=fake.sentence()
                )
                collection.produce.set(random.sample(produces, random.randint(1, min(3, len(produces)))))
                collections.append(collection)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(collections)} collections'))

        # Generate fake favorites
        favorites = []
        for buyer in buyers:
            for produce in random.sample(produces, random.randint(1, min(5, len(produces)))):
                favorite = Favorite.objects.create(
                    user=buyer,
                    produce=produce
                )
                favorites.append(favorite)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(favorites)} favorites'))

        # Generate fake watchlist
        watchlist_items = []
        for buyer in buyers:
            for produce in random.sample(produces, random.randint(1, min(5, len(produces)))):
                watchlist_item = Watchlist.objects.create(
                    user=buyer,
                    produce=produce
                )
                watchlist_items.append(watchlist_item)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(watchlist_items)} watchlist items'))

        # Generate fake SMS alerts
        sms_alerts = []
        for buyer in buyers:
            for _ in range(random.randint(0, 3)):
                alert = SMSAlert.objects.create(
                    user=buyer,
                    message=fake.sentence(),
                    sent_at=fake.date_time_this_month(),
                    is_read=random.choice([True, False])
                )
                sms_alerts.append(alert)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(sms_alerts)} SMS alerts'))

        # Generate fake contact requests
        contact_requests = []
        for buyer in buyers:
            for farmer in random.sample(farmers, random.randint(1, min(3, len(farmers)))):
                request = ContactRequest.objects.create(
                    buyer=buyer,
                    farmer=farmer,
                    produce=random.choice(produces),
                    message=fake.paragraph(nb_sentences=2),
                    status=random.choice(['pending', 'accepted', 'rejected']),
                    created_at=fake.date_time_this_month()
                )
                contact_requests.append(request)
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(contact_requests)} contact requests'))

        self.stdout.write(self.style.SUCCESS('Fake data generation completed for all models!'))
