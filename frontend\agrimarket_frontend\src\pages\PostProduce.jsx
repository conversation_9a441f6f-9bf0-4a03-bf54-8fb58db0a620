import React, { useState, useEffect } from "react";
import axiosInstance from "../api/axiosInstance";
import Navbar from "../components/Navbar";
import { useAuth } from "../api/AuthContext";

const PostProduce = ({ onSuccess }) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [unit, setUnit] = useState("");
  const [quantityAvailable, setQuantityAvailable] = useState("");
  const [category, setCategory] = useState("");
  const [categories, setCategories] = useState([]);
  const [photo, setPhoto] = useState(null);
  const [contactPhone, setContactPhone] = useState("");
  const [isOrganic, setIsOrganic] = useState(false);
  const [harvestDate, setHarvestDate] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const { hasRole } = useAuth();

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axiosInstance.get("categories/");
        setCategories(response.data.results || response.data);
      } catch (err) {
        console.error("Error fetching categories:", err);
      }
    };
    fetchCategories();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess(false);
    setLoading(true);

    // Validation
    if (!name || !description || !price || !unit || !quantityAvailable || !category) {
      setError("Please fill in all required fields");
      setLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append("name", name);
    formData.append("description", description);
    formData.append("price", price);
    formData.append("unit", unit);
    formData.append("quantity_available", quantityAvailable);
    formData.append("category", category);
    formData.append("is_organic", isOrganic);
    if (photo) formData.append("photo", photo);
    if (contactPhone) formData.append("contact_phone", contactPhone);
    if (harvestDate) formData.append("harvest_date", harvestDate);

    try {
      await axiosInstance.post("produce/", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      setSuccess(true);
      // Reset form
      setName("");
      setDescription("");
      setPrice("");
      setUnit("");
      setQuantityAvailable("");
      setCategory("");
      setPhoto(null);
      setContactPhone("");
      setIsOrganic(false);
      setHarvestDate("");
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error("Error posting produce:", err);
      if (err.response?.data) {
        if (typeof err.response.data === "string") {
          setError(err.response.data);
        } else if (err.response.data.detail) {
          setError(err.response.data.detail);
        } else {
          // Handle field-specific errors
          const errors = Object.entries(err.response.data)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('; ');
          setError(errors);
        }
      } else {
        setError("Failed to post produce. Please check your input and try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!hasRole("farmer") && !hasRole("admin")) {
    return (      <>
        <Navbar />
        <div className="nav-spacer" />
        <div className="container py-5 text-center">
          <div className="alert alert-danger">Only farmers or admins can post produce.</div>
        </div>
      </>
    );
  }

  return (
    <>      <Navbar />
      <div className="nav-spacer" />
      <div className="container py-5">
        <h2>Post Produce</h2>
        <form onSubmit={handleSubmit} encType="multipart/form-data">
          <div className="mb-3">
            <label className="form-label">Name</label>
            <input type="text" className="form-control" value={name} onChange={e => setName(e.target.value)} required />
          </div>
          <div className="mb-3">
            <label className="form-label">Description</label>
            <textarea className="form-control" value={description} onChange={e => setDescription(e.target.value)} required />
          </div>
          <div className="mb-3">
            <label className="form-label">Price</label>
            <input type="text" className="form-control" value={price} onChange={e => setPrice(e.target.value)} required />
          </div>
          <div className="mb-3">
            <label className="form-label">Photo</label>
            <input type="file" className="form-control" onChange={e => setPhoto(e.target.files[0])} />
          </div>
          <div className="mb-3">
            <label className="form-label">Contact Phone</label>
            <input type="text" className="form-control" value={contactPhone} onChange={e => setContactPhone(e.target.value)} required />
          </div>
          {error && <div className="alert alert-danger">{error}</div>}
          {success && <div className="alert alert-success">Produce posted successfully!</div>}
          <button type="submit" className="btn btn-primary">Submit</button>
        </form>
      </div>
    </>
  );
};

export default PostProduce;
