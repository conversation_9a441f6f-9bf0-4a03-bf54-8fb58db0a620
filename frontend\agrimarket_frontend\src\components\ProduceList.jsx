import React, { useEffect, useState } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import axiosInstance from "../api/axiosInstance";
import { useAuth } from "../api/AuthContext";

function ProduceList() {
  const [produce, setProduce] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user, hasRole } = useAuth();

  useEffect(() => {
    axiosInstance.get("produce/")
      .then(res => {
        console.log("API Response:", res.data); // Debug log
        // Handle both paginated and non-paginated responses
        const produceData = res.data.results || res.data || [];
        setProduce(Array.isArray(produceData) ? produceData : []);
        setLoading(false);
      })
      .catch(err => {
        console.error("Error fetching produce:", err);
        setProduce([]);
        setLoading(false);
      });
  }, []);

  if (loading) return <div className="container py-5 text-center"><div className="spinner-border text-success" role="status"><span className="visually-hidden">Loading...</span></div></div>;

  return (
    <div className="container mt-4">
      <h2 className="mb-4 text-primary">Available Produce</h2>
      {produce.length === 0 ? (
        <div className="alert alert-info text-center">
          <h5>No produce available</h5>
          <p>Be the first to list your produce!</p>
        </div>
      ) : (
        <ul className="list-group shadow-sm">
          {produce.map(item => (
            <li key={item.id} className="list-group-item d-flex flex-column flex-md-row align-items-md-center justify-content-between py-3">
              <div>
                <strong className="text-success">{item.name}</strong> <span className="text-muted">- ${item.price}</span>
                <div className="small text-secondary">{item.description}</div>
              </div>
              <div className="mt-2 mt-md-0">
                {(hasRole("admin") || (user && item.farmer && user.username === item.farmer.username)) && (
                  <>
                    <button className="btn btn-sm btn-outline-primary me-2">Edit</button>
                    <button className="btn btn-sm btn-danger">Delete</button>
                  </>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default ProduceList;