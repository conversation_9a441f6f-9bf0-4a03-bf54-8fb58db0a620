/* Utility Classes */

/* Spacing Utilities */
.section-padding {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.section-padding-sm {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.nav-spacer {
  height: 6.5rem;
}

/* Size Utilities */
.w-64 {
  width: 64px;
}

.h-64 {
  height: 64px;
}

.w-48 {
  width: 48px;
}

.h-48 {
  height: 48px;
}

.w-80 {
  width: 80px;
}

.h-80 {
  height: 80px;
}

/* Form Utilities */
.form-input-standard {
  max-width: 320px;
  width: 100%;
}

.form-container-rounded {
  border-radius: 1.5rem;
  padding: 3rem;
}

/* Display & Layout Utilities */
.d-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column {
  flex-direction: column;
}

/* Image Utilities */
.img-cover {
  object-fit: cover;
}

.img-contain {
  object-fit: contain;
}

/* Animation Utilities */
.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--bs-box-shadow-sm);
}

/* Shadow Utilities */
.shadow-sm {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.shadow-md {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.shadow-lg {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* Hover Effects */
.hover-lift-sm {
  transition: transform 0.2s ease-in-out !important;
}

.hover-lift-sm:hover {
  transform: translateY(-2px) !important;
}

.hover-shadow-sm:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

/* Text Gradients */
.text-gradient-primary {
  background: linear-gradient(45deg, var(--bs-primary), var(--bs-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Border Utilities */
.border-dashed {
  border-style: dashed !important;
}

.border-primary-subtle {
  border-color: rgba(var(--bs-primary-rgb), 0.2) !important;
}

/* Backdrop Filters */
.backdrop-blur {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation Utilities */
.animate-fade-up {
  animation: fadeUp 0.5s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Padding */
.p-responsive {
  padding: clamp(1rem, 5vw, 3rem);
}

.py-responsive {
  padding-top: clamp(2rem, 8vw, 5rem);
  padding-bottom: clamp(2rem, 8vw, 5rem);
}

/* Grid Utilities */
.auto-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--auto-grid-min, 280px), 1fr));
  gap: var(--auto-grid-gap, 2rem);
}

/* Aspect Ratio */
.aspect-square {
  aspect-ratio: 1;
}

.aspect-video {
  aspect-ratio: 16/9;
}

/* Focus Ring */
.focus-ring {
  outline: none;
  transition: box-shadow 0.2s ease;
}

.focus-ring:focus-visible {
  box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.3);
}

/* Text Clamp */
.text-clamp-2 {
  display: -webkit-box;  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .section-padding {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  
  .section-padding-sm {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  
  .nav-spacer {
    height: 5rem;
  }
}

/* Border Radius Utilities */
.rounded-full {
  border-radius: 50%;
}

.rounded-xl {
  border-radius: 1rem;
}

.rounded-2xl {
  border-radius: 1.5rem;
}

.rounded-3xl {
  border-radius: 2rem;
}

/* Responsive Container Widths */
.container-narrow {
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-wide {
  max-width: 1440px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Responsive Text Sizes */
.text-responsive {
  font-size: clamp(1rem, 2vw + 0.5rem, 1.25rem);
}

.heading-responsive {
  font-size: clamp(1.5rem, 4vw + 1rem, 3rem);
}

/* Responsive Spacing */
.gap-responsive {
  gap: clamp(1rem, 2vw, 2rem);
}

.p-responsive {
  padding: clamp(1rem, 3vw, 3rem);
}

/* Responsive Grid */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(min(100%, var(--grid-min, 250px)), 1fr)
  );
  gap: var(--grid-gap, 1.5rem);
}

/* Responsive Flexbox */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: var(--flex-gap, 1rem);
}

/* Text Truncate */
.truncate-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover Transitions */
.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Interactive States */
.hover-opacity {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.hover-opacity:hover {
  opacity: 1;
}

/* Aspect Ratios */
.aspect-square {
  aspect-ratio: 1;
  object-fit: cover;
}

.aspect-video {
  aspect-ratio: 16/9;
  object-fit: cover;
}

/* Scrollbar Styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--bs-primary-rgb), 0.2) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  border-radius: 3px;
}

/* Focus Styles */
.focus-ring {
  transition: box-shadow 0.2s ease;
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.3);
}

/* Blend Modes */
.blend-multiply {
  mix-blend-mode: multiply;
}

.blend-overlay {
  mix-blend-mode: overlay;
}

/* Content Visibility */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}
