import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axiosInstance from '../api/axiosInstance';
import '../App.css';

/**
 * LogisticsRegister component for third-party logistics partners to register with AgriMarket.
 * Allows partners to provide company details and select service regions in Zimbabwe.
 * Registration is submitted for admin verification.
 */

const LogisticsRegister = () => {
  // Form state for logistics partner details
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [email, setEmail] = useState('');
  const [baseRate, setBaseRate] = useState('');
  const [website, setWebsite] = useState('');
  const [vehicleTypes, setVehicleTypes] = useState('');
  const [capacity, setCapacity] = useState('');
  const [businessLicense, setBusinessLicense] = useState('');
  const [insuranceDetails, setInsuranceDetails] = useState('');
  const [serviceRegions, setServiceRegions] = useState([]); // Selected region IDs
  const [availableRegions, setAvailableRegions] = useState([]); // Regions fetched from API
  // UI state for loading, error, and success messages
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        setLoading(true);
        let allRegions = [];
        let nextUrl = 'regions/';
        
        // Fetch all pages of regions data
        while (nextUrl) {
          // Remove /api/ prefix since axiosInstance already includes it
          const cleanUrl = nextUrl.startsWith('/api/') ? nextUrl.substring(5) : nextUrl;
          const response = await axiosInstance.get(cleanUrl);
          console.log('Regions API response:', response.data);
          const regionsData = response.data.results || response.data;
          allRegions = [...allRegions, ...regionsData];
          nextUrl = response.data.next || null;
        }
        
        setAvailableRegions(allRegions);
        if (allRegions.length === 0) {
          setError('No regions found in the database. Please contact support for assistance.');
        }
      } catch (err) {
        console.error('Error fetching regions:', err);
        if (err.response?.status === 404) {
          setError('Regions endpoint not found. Please ensure the backend is running and regions are configured.');
        } else if (err.response?.status >= 500) {
          setError('Server error while loading regions. Please try again later.');
        } else if (!err.response) {
          setError('Unable to connect to server. Please check if the backend is running.');
        } else {
          setError(`Failed to load regions: ${err.message}. Please try again or contact support.`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRegions();
  }, []);

  // Toggle selection of a region or sub-region for logistics service coverage
  const handleRegionToggle = (regionId) => {
    setServiceRegions(prev => 
      prev.includes(regionId) 
        ? prev.filter(id => id !== regionId) 
        : [...prev, regionId]
    );
  };

  // Select or deselect all sub-regions under a main region
  const handleSelectAllSubRegions = (regionId, subRegionIds, select) => {
    setServiceRegions(prev => {
      if (select) {
        // Add the main region and all sub-regions if not already selected
        const newIds = subRegionIds.filter(id => !prev.includes(id));
        return [...prev, regionId, ...newIds];
      } else {
        // Remove the main region and all sub-regions
        return prev.filter(id => id !== regionId && !subRegionIds.includes(id));
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!username || !password || !name || !contactPhone || !email || serviceRegions.length === 0) {
      setError('Please fill in all required fields and select at least one service region.');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long.');
      return;
    }

    try {
      // Format serviceRegions as an array of dictionaries with id, name, and type for backend
      const regionsToSubmit = serviceRegions.map(id => {
        const region = availableRegions.find(r => r.id === parseInt(id, 10));
        return {
          id: parseInt(id, 10),
          name: region ? region.name : '',
          type: region ? region.type : ''
        };
      });
      // Prepend https:// to website if no protocol is specified
      let formattedWebsite = website;
      if (website && !website.startsWith('http://') && !website.startsWith('https://')) {
        formattedWebsite = `https://${website}`;
      }
      console.log('Submitting logistics partner registration with data:', {
        name,
        description,
        contact_phone: contactPhone,
        email,
        base_rate: baseRate || 0.0,
        website: formattedWebsite,
        service_regions: regionsToSubmit
      });
      const data = {
        username,
        password,
        name,
        description,
        contact_phone: contactPhone,
        email,
        base_rate: parseFloat(baseRate) || 0.0,
        website: formattedWebsite,
        vehicle_types: vehicleTypes,
        capacity,
        business_license: businessLicense,
        insurance_details: insuranceDetails,
        service_regions: regionsToSubmit
      };
      const response = await axiosInstance.post('logistics-partners/register/', data);
      setSuccessMessage(response.data.message || 'Registration successful. Your application is pending admin verification.');
      setError(null);
      // Clear form
      setName('');
      setDescription('');
      setContactPhone('');
      setEmail('');
      setBaseRate('');
      setWebsite('');
      setServiceRegions([]);
    } catch (err) {
      console.error('Registration failed:', err);
      let errorMessage = 'Failed to register. Please check your details and try again.';

      if (err.response?.data) {
        if (err.response.data.message) {
          errorMessage = err.response.data.message;
        }

        if (err.response.data.errors) {
          const fieldErrors = Object.entries(err.response.data.errors)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('; ');
          errorMessage += ` Field errors: ${fieldErrors}`;
        }

        // Log detailed error for debugging
        console.error('Detailed error:', err.response.data);
      } else if (err.response?.status === 400) {
        errorMessage = 'Bad request. Please check all required fields are filled correctly.';
      } else if (err.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (!err.response) {
        errorMessage = 'Network error. Please check if the backend is running.';
      }

      setError(errorMessage);
      setSuccessMessage(null);
    }
  };

  if (loading) {
    return <div className="text-center mt-5">Loading...</div>;
  }

  return (
    <div className="container d-flex justify-content-center" style={{ paddingTop: '20px', minHeight: 'calc(100vh - 120px)' }}>
      <div className="card shadow-lg p-4 w-100" style={{ maxWidth: '800px', borderRadius: '15px' }}>
        <div className="card-body">
          <h2 className="card-title text-center mb-4" style={{ color: '#29ab4e', fontWeight: 'bold' }}>Logistics Partner Registration</h2>
          <p className="text-center mb-4">Register your logistics company to partner with AgriMarket. Your application will be reviewed by our admin team.</p>
          
          {error && <div className="alert alert-danger mb-4">{error}</div>}
          {successMessage && <div className="alert alert-success mb-4">{successMessage}</div>}
          
          {!successMessage && (
            <form onSubmit={handleSubmit}>
              {/* Account Information */}
              <h5 className="mb-3 text-primary">Account Information</h5>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="username" className="form-label">Username <span className="text-danger">*</span></label>
                  <input type="text" className="form-control" id="username" value={username} onChange={(e) => setUsername(e.target.value)} required placeholder="e.g., fasttrack_logistics" style={{ borderRadius: '8px' }} />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="password" className="form-label">Password <span className="text-danger">*</span></label>
                  <input type="password" className="form-control" id="password" value={password} onChange={(e) => setPassword(e.target.value)} required placeholder="Minimum 8 characters" style={{ borderRadius: '8px' }} />
                </div>
              </div>

              {/* Business Information */}
              <h5 className="mb-3 text-primary mt-4">Business Information</h5>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="name" className="form-label">Company Name <span className="text-danger">*</span></label>
                  <input type="text" className="form-control" id="name" value={name} onChange={(e) => setName(e.target.value)} required placeholder="e.g., FastTrack Logistics" style={{ borderRadius: '8px' }} />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="email" className="form-label">Business Email <span className="text-danger">*</span></label>
                  <input type="email" className="form-control" id="email" value={email} onChange={(e) => setEmail(e.target.value)} required placeholder="e.g., <EMAIL>" style={{ borderRadius: '8px' }} />
                </div>
              </div>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="contactPhone" className="form-label">Contact Phone <span className="text-danger">*</span></label>
                  <input type="tel" className="form-control" id="contactPhone" value={contactPhone} onChange={(e) => setContactPhone(e.target.value)} required placeholder="e.g., +263 77 123 4567" style={{ borderRadius: '8px' }} />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="website" className="form-label">Website</label>
                  <input type="text" className="form-control" id="website" value={website} onChange={(e) => setWebsite(e.target.value)} placeholder="e.g., fasttrack.co.zw" style={{ borderRadius: '8px' }} />
                </div>
              </div>
              <div className="mb-3">
                <label htmlFor="description" className="form-label">Description</label>
                <textarea className="form-control" id="description" rows="3" value={description} onChange={(e) => setDescription(e.target.value)} placeholder="Briefly describe your logistics services, coverage, and experience." style={{ borderRadius: '8px' }}></textarea>
              </div>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="baseRate" className="form-label">Base Rate (USD per km)</label>
                  <input type="number" className="form-control" id="baseRate" step="0.01" min="0" value={baseRate} onChange={(e) => setBaseRate(e.target.value)} placeholder="e.g., 0.50" style={{ borderRadius: '8px' }} />
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="capacity" className="form-label">Vehicle Capacity</label>
                  <input type="text" className="form-control" id="capacity" value={capacity} onChange={(e) => setCapacity(e.target.value)} placeholder="e.g., 5 tons, 1000 kg" style={{ borderRadius: '8px' }} />
                </div>
              </div>

              {/* Vehicle and Business Details */}
              <h5 className="mb-3 text-primary mt-4">Vehicle & Business Details</h5>
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label htmlFor="vehicleTypes" className="form-label">Vehicle Types</label>
                  <input type="text" className="form-control" id="vehicleTypes" value={vehicleTypes} onChange={(e) => setVehicleTypes(e.target.value)} placeholder="e.g., Truck, Van, Pickup" style={{ borderRadius: '8px' }} />
                  <small className="text-muted">Separate multiple types with commas</small>
                </div>
                <div className="col-md-6 mb-3">
                  <label htmlFor="businessLicense" className="form-label">Business License Number</label>
                  <input type="text" className="form-control" id="businessLicense" value={businessLicense} onChange={(e) => setBusinessLicense(e.target.value)} placeholder="e.g., BL123456" style={{ borderRadius: '8px' }} />
                </div>
              </div>
              <div className="mb-3">
                <label htmlFor="insuranceDetails" className="form-label">Insurance Details</label>
                <textarea className="form-control" id="insuranceDetails" rows="2" value={insuranceDetails} onChange={(e) => setInsuranceDetails(e.target.value)} placeholder="Provide details about your vehicle and cargo insurance coverage." style={{ borderRadius: '8px' }}></textarea>
              </div>
              <div className="mb-3">
                <label className="form-label">Service Regions <span className="text-danger">*</span></label>
                <p className="text-muted small">Select all regions, cities, or towns in Zimbabwe where you can provide logistics services.</p>
                {availableRegions.length === 0 ? (
                  <div>
                    <p className="text-warning">No regions available. Please refresh the page or contact support if the issue persists.</p>
                    <button
                      type="button"
                      className="btn btn-outline-secondary btn-sm mt-2"
                      onClick={() => {
                        setLoading(true);
                        setError(null);
                        const fetchRegions = async () => {
                          try {
                            let allRegions = [];
                            let nextUrl = '/api/regions/';
                            
                            // Fetch all pages of regions data
                            while (nextUrl) {
                              const response = await axiosInstance.get(nextUrl);
                              console.log('Regions API response on retry:', response.data);
                              const regionsData = response.data.results || response.data;
                              allRegions = [...allRegions, ...regionsData];
                              nextUrl = response.data.next || null;
                            }
                            
                            setAvailableRegions(allRegions);
                            if (allRegions.length === 0) {
                              setError('No regions found in the database. Please contact support for assistance.');
                            }
                          } catch (err) {
                            setError(`Failed to load regions: ${err.message}. Please try again or contact support.`);
                            console.error('Error fetching regions on retry:', err);
                          } finally {
                            setLoading(false);
                          }
                        };
                        fetchRegions();
                      }}
                      disabled={loading}
                    >
                      {loading ? 'Loading...' : 'Refresh Regions'}
                    </button>
                  </div>
                ) : (
                  <Select
                    isMulti
                    options={availableRegions
                      .filter(region => region.type === 'region')
                      .map(region => ({
                        label: `${region.name} (Region)`,
                        options: [
                          { value: region.id, label: `${region.name} (Region)` },
                          ...availableRegions
                            .filter(sub => sub.parent_region === region.id)
                            .map(subRegion => ({
                              value: subRegion.id,
                              label: `${subRegion.name} (${subRegion.type})`
                            }))
                        ]
                      }))}
                    value={serviceRegions.map(id => {
                      const region = availableRegions.find(r => r.id === id);
                      return region ? { value: id, label: `${region.name} (${region.type})` } : null;
                    }).filter(Boolean)}
                    onChange={(selectedOptions) => {
                      setServiceRegions(selectedOptions.map(option => option.value));
                    }}
                    placeholder="Select service regions..."
                    className="basic-multi-select"
                    classNamePrefix="select"
                    styles={{
                      control: (provided) => ({
                        ...provided,
                        maxHeight: '300px',
                        overflowY: 'auto',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '8px',
                        border: '1px solid #dee2e6'
                      }),
                      menu: (provided) => ({
                        ...provided,
                        zIndex: 9999
                      })
                    }}
                  />
                )}
              </div>
              <div className="d-grid gap-2 mt-4">
                <button type="submit" className="btn btn-primary btn-lg" style={{ backgroundColor: '#29ab4e', border: 'none', borderRadius: '8px' }}>Submit Registration</button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default LogisticsRegister;
