#!/usr/bin/env python3
"""
Test script to verify the AgriMarket backend is working properly.
"""

import requests
import json
import sys

def test_endpoint(url, description):
    """Test a single endpoint."""
    try:
        print(f"Testing {description}...")
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print(f"✅ {description}: OK")
            try:
                data = response.json()
                print(f"   Response: {json.dumps(data, indent=2)[:200]}...")
            except:
                print(f"   Response: {response.text[:100]}...")
            return True
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {description}: Connection failed - Backend not running?")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {description}: Timeout")
        return False
    except Exception as e:
        print(f"❌ {description}: Error - {e}")
        return False

def main():
    """Test the backend endpoints."""
    print("🧪 Testing AgriMarket Backend")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:8000"
    
    tests = [
        (f"{base_url}/", "Django root"),
        (f"{base_url}/api/health/", "Health check"),
        (f"{base_url}/api/categories/", "Categories endpoint"),
        (f"{base_url}/api/market-prices/", "Market prices endpoint"),
        (f"{base_url}/admin/", "Django admin"),
        (f"{base_url}/swagger/", "API documentation"),
    ]
    
    passed = 0
    total = len(tests)
    
    for url, description in tests:
        if test_endpoint(url, description):
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend is working correctly.")
    elif passed >= total - 2:
        print("⚠️  Most tests passed. Backend is mostly working.")
    else:
        print("❌ Multiple tests failed. Check backend configuration.")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure Django server is running: cd backend && python manage.py runserver")
        print("2. Check for error messages in the Django terminal")
        print("3. Verify database migrations: python manage.py migrate")
        print("4. Check .env file configuration")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
