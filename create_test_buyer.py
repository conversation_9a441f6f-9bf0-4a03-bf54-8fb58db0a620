#!/usr/bin/env python3
"""
Script to create a test buyer account for debugging
Run from backend directory: python manage.py shell < create_test_buyer.py
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import UserProfile

User = get_user_model()

def create_test_buyer():
    """Create a test buyer account"""
    print("🔧 Creating Test Buyer Account")
    print("=" * 40)
    
    # Check if test buyer already exists
    try:
        existing_buyer = User.objects.get(username='testbuyer')
        print(f"ℹ️  Test buyer already exists: {existing_buyer.username}")
        print(f"   🛒 Is Buyer: {existing_buyer.is_buyer}")
        print(f"   ✅ Is Active: {existing_buyer.is_active}")
        return existing_buyer
    except User.DoesNotExist:
        pass
    
    # Create new test buyer
    try:
        test_buyer = User.objects.create_user(
            username='testbuyer',
            email='<EMAIL>',
            password='buyer123',
            first_name='Test',
            last_name='Buyer',
            is_buyer=True,
            is_active=True,
            phone='+************',
            location='Harare, Zimbabwe'
        )
        
        # Create profile
        profile = UserProfile.objects.create(
            user=test_buyer,
            bio='Test buyer account for debugging login issues'
        )
        
        print(f"✅ Created test buyer account:")
        print(f"   👤 Username: {test_buyer.username}")
        print(f"   📧 Email: {test_buyer.email}")
        print(f"   🔑 Password: buyer123")
        print(f"   🛒 Is Buyer: {test_buyer.is_buyer}")
        print(f"   ✅ Is Active: {test_buyer.is_active}")
        print(f"   📄 Profile Created: Yes")
        
        return test_buyer
        
    except Exception as e:
        print(f"❌ Error creating test buyer: {e}")
        return None

def test_buyer_authentication():
    """Test buyer authentication"""
    print("\n🧪 Testing Buyer Authentication")
    print("=" * 40)
    
    from django.contrib.auth import authenticate
    
    # Test authentication
    user = authenticate(username='testbuyer', password='buyer123')
    
    if user:
        print(f"✅ Authentication successful!")
        print(f"   👤 User: {user.username}")
        print(f"   🛒 Is Buyer: {user.is_buyer}")
        print(f"   ✅ Is Active: {user.is_active}")
        print(f"   🔑 Is Authenticated: {user.is_authenticated}")
        
        # Test role validation
        if user.is_buyer:
            print("✅ Buyer role validation: PASSED")
        else:
            print("❌ Buyer role validation: FAILED")
            
    else:
        print("❌ Authentication failed!")
        
        # Check if user exists
        try:
            user = User.objects.get(username='testbuyer')
            print(f"   👤 User exists: {user.username}")
            print(f"   ✅ Is Active: {user.is_active}")
            print(f"   🔑 Has Usable Password: {user.has_usable_password()}")
        except User.DoesNotExist:
            print("   ❌ User does not exist")

def check_all_buyers():
    """Check all buyer accounts"""
    print("\n📊 All Buyer Accounts")
    print("=" * 40)
    
    buyers = User.objects.filter(is_buyer=True)
    
    if buyers.count() == 0:
        print("❌ No buyer accounts found in database")
    else:
        print(f"✅ Found {buyers.count()} buyer account(s):")
        
        for buyer in buyers:
            print(f"\n   👤 {buyer.username}")
            print(f"      📧 Email: {buyer.email}")
            print(f"      ✅ Active: {buyer.is_active}")
            print(f"      🔑 Usable Password: {buyer.has_usable_password()}")
            print(f"      📅 Created: {buyer.created_at}")
            
            # Check profile
            try:
                profile = UserProfile.objects.get(user=buyer)
                print(f"      📄 Profile: Exists")
            except UserProfile.DoesNotExist:
                print(f"      📄 Profile: Missing")

def main():
    """Main function"""
    print("🔍 BUYER LOGIN DIAGNOSTIC & FIX")
    print("=" * 50)
    
    # Create test buyer
    test_buyer = create_test_buyer()
    
    # Test authentication
    test_buyer_authentication()
    
    # Check all buyers
    check_all_buyers()
    
    print("\n" + "=" * 50)
    print("🎯 TESTING INSTRUCTIONS")
    print("=" * 50)
    print("1. Go to: http://127.0.0.1:5173/login")
    print("2. Use these credentials:")
    print("   Username: testbuyer")
    print("   Password: buyer123")
    print("   Role: Buyer")
    print("3. Click Login")
    print("4. Should redirect to home page")
    print("\nIf login still fails:")
    print("- Check browser console for errors")
    print("- Check Django server logs")
    print("- Verify frontend role handling")

if __name__ == "__main__":
    main()
