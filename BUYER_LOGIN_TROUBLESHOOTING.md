# 🛒 Buyer Login Issue Troubleshooting Guide

## 🎯 **Problem Summary**

You can log in with other roles (farmer, admin, logistics) but not with buyer accounts. Let's diagnose and fix this issue.

## 🔍 **Potential Causes**

### **1. No Buyer Accounts Exist**
- Users might not have registered as buyers
- Registration process might not be creating buyer accounts properly

### **2. Database Issues**
- `is_buyer` field might not be set correctly
- User profiles might be missing

### **3. Frontend Role Validation**
- AuthContext might not be handling buyer role correctly
- Login form might have buyer-specific issues

### **4. Backend Authentication**
- Role validation in login endpoint might be failing for buyers

## 🧪 **Step-by-Step Diagnosis**

### **Step 1: Check if Buyer Accounts Exist**

Run this in Django shell:
```python
python manage.py shell

from django.contrib.auth import get_user_model
User = get_user_model()

# Check all users
print("All users:")
for user in User.objects.all():
    print(f"{user.username}: farmer={user.is_farmer}, buyer={user.is_buyer}, staff={user.is_staff}")

# Count buyers
buyer_count = User.objects.filter(is_buyer=True).count()
print(f"\nTotal buyers: {buyer_count}")
```

### **Step 2: Create Test Buyer Account**

If no buyers exist, create one:
```python
# In Django shell
test_buyer = User.objects.create_user(
    username='testbuyer',
    email='<EMAIL>',
    password='buyer123',
    is_buyer=True,
    is_active=True
)

# Create profile
from core.models import UserProfile
UserProfile.objects.create(user=test_buyer)

print(f"Created buyer: {test_buyer.username}")
```

### **Step 3: Test Authentication**

Test if buyer can authenticate:
```python
# In Django shell
from django.contrib.auth import authenticate

user = authenticate(username='testbuyer', password='buyer123')
if user:
    print(f"✅ Authentication successful: {user.username}")
    print(f"Is buyer: {user.is_buyer}")
else:
    print("❌ Authentication failed")
```

## 🔧 **Quick Fixes**

### **Fix 1: Create Buyer Account via Registration**

1. **Go to registration page**: `http://127.0.0.1:5173/register`
2. **Fill the form**:
   - Username: `buyertest`
   - Email: `<EMAIL>`
   - Password: `testpass123`
   - Confirm Password: `testpass123`
   - Role: **🛒 Buyer - Purchase fresh produce**
3. **Submit the form**
4. **Check for success message**
5. **Try logging in** with the new buyer account

### **Fix 2: Manual Database Creation**

Create a buyer account directly in Django admin:

1. **Go to Django admin**: `http://127.0.0.1:8000/admin/`
2. **Login as admin**
3. **Go to Users section**
4. **Add new user**:
   - Username: `manualbuyer`
   - Password: `buyer123`
   - **Check**: `is_buyer` field
   - **Check**: `is_active` field
5. **Save the user**

### **Fix 3: Check Frontend Console**

1. **Open browser DevTools** (F12)
2. **Go to Console tab**
3. **Try logging in as buyer**
4. **Look for JavaScript errors**
5. **Check Network tab** for API call responses

## 🧪 **Testing Procedure**

### **Test 1: Register New Buyer**
```
1. Go to: http://127.0.0.1:5173/register
2. Username: newbuyer123
3. Email: <EMAIL>
4. Password: testpass123
5. Role: Buyer
6. Submit → Should show success
7. Go to login page
8. Login with new credentials
```

### **Test 2: Use Test Buyer Account**
```
1. Go to: http://127.0.0.1:5173/login
2. Username: testbuyer
3. Password: buyer123
4. Role: Buyer
5. Submit → Should redirect to home
```

### **Test 3: Check Backend Response**
```
1. Open browser DevTools
2. Go to Network tab
3. Try buyer login
4. Check POST request to /api/login/
5. Look at response status and data
```

## 🔍 **Common Error Messages**

### **"User is not registered as a buyer"**
- **Cause**: User exists but `is_buyer=False`
- **Fix**: Update user in Django admin or shell

### **"Invalid credentials"**
- **Cause**: Wrong username/password or user doesn't exist
- **Fix**: Check username/password or create account

### **"Network Error"**
- **Cause**: Django server not running
- **Fix**: Start Django server: `python manage.py runserver`

### **No error but login fails**
- **Cause**: Frontend AuthContext issue
- **Fix**: Check browser console for JavaScript errors

## 🛠️ **Manual Database Fix**

If you need to manually fix buyer accounts:

```python
# Django shell
from django.contrib.auth import get_user_model
from core.models import UserProfile

User = get_user_model()

# Find user and make them a buyer
user = User.objects.get(username='existing_username')
user.is_buyer = True
user.save()

# Ensure they have a profile
profile, created = UserProfile.objects.get_or_create(user=user)
if created:
    print(f"Created profile for {user.username}")
```

## 📊 **Expected Results**

### **Successful Buyer Login Should:**
1. ✅ Accept buyer credentials
2. ✅ Return authentication token
3. ✅ Redirect to home page
4. ✅ Show buyer-specific navbar options
5. ✅ Allow access to buyer features

### **Failed Login Indicators:**
1. ❌ Error message displayed
2. ❌ No redirect after submission
3. ❌ Console errors in browser
4. ❌ Network errors in DevTools

## 🎯 **Next Steps**

1. **Try registering a new buyer** using the registration form
2. **Test login** with the new buyer account
3. **Check Django admin** to verify buyer accounts exist
4. **Monitor browser console** for JavaScript errors
5. **Check Django logs** for backend errors

## 🚀 **Quick Test Commands**

Run these in Django shell to quickly diagnose:

```python
# Check buyers
from django.contrib.auth import get_user_model
User = get_user_model()
buyers = User.objects.filter(is_buyer=True)
print(f"Buyers: {[b.username for b in buyers]}")

# Create test buyer if none exist
if not buyers.exists():
    User.objects.create_user(
        username='quickbuyer',
        password='test123',
        email='<EMAIL>',
        is_buyer=True
    )
    print("Created quickbuyer account")
```

## 📋 **Summary**

The most likely cause is that **no buyer accounts exist** in the database. Try:

1. **Register a new buyer** via the registration form
2. **Create a test buyer** via Django shell
3. **Check existing users** in Django admin

**Once you have a buyer account, the login should work correctly based on the code analysis!** 🎉
