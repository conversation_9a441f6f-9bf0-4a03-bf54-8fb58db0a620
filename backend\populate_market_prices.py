import os
import django
import random
from datetime import date, timedelta

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agrimarket_backend.settings")
django.setup()

from core.models import MarketPrice

def populate_market_prices():
    """
    Populates the database with sample market prices.
    """
    prices_to_create = [
        {'crop_name': 'Maize', 'price': 250.00, 'unit': 'ton', 'source': '<PERSON><PERSON>'},
        {'crop_name': 'Soyabeans', 'price': 550.00, 'unit': 'ton', 'source': 'Bulawayo Market'},
        {'crop_name': 'Wheat', 'price': 400.00, 'unit': 'ton', 'source': '<PERSON><PERSON><PERSON>'},
        {'crop_name': 'Tobacco', 'price': 3.50, 'unit': 'kg', 'source': 'Marondera Auction Floors'},
        {'crop_name': 'Cotton', 'price': 0.80, 'unit': 'kg', 'source': 'Gokwe Market'},
        {'crop_name': 'Potatoes', 'price': 0.75, 'unit': 'kg', 'source': '<PERSON><PERSON> Mbare Musika'},
        {'crop_name': 'Tomatoes', 'price': 0.50, 'unit': 'kg', 'source': 'Bulawayo Market'},
        {'crop_name': 'Onions', 'price': 0.60, 'unit': 'kg', 'source': 'Mutare Sakubva'},
    ]

    for price_data in prices_to_create:
        # Create a market price entry for today
        MarketPrice.objects.get_or_create(
            crop_name=price_data['crop_name'],
            source=price_data['source'],
            date=date.today(),
            defaults={
                'price': price_data['price'],
                'unit': price_data['unit'],
                'approved': True
            }
        )

        # Create a few historical market price entries
        for i in range(1, 4):
            historical_date = date.today() - timedelta(weeks=i)
            historical_price = price_data['price'] * random.uniform(0.9, 1.1)
            MarketPrice.objects.get_or_create(
                crop_name=price_data['crop_name'],
                source=price_data['source'],
                date=historical_date,
                defaults={
                    'price': f"{historical_price:.2f}",
                    'unit': price_data['unit'],
                    'approved': True
                }
            )

    print("Successfully populated the database with sample market prices.")

if __name__ == "__main__":
    populate_market_prices()
