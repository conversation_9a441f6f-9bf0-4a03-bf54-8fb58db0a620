import React from "react";
import { Link } from "react-router-dom";

function Footer() {
  return (
    <footer className="footer border-top bg-body position-relative mt-5">
      <div className="container pt-5 pb-3">
        <div className="row">
          <div className="col-sm-6 col-md-3 mb-4">
            <h5 className="mb-3">Marketplace</h5>
            <ul className="list-unstyled">
              <li><Link to="/produce-explore">All Produce</Link></li>
              <li><Link to="/market-prices">Market Prices</Link></li>
              <li><Link to="/categories">Categories</Link></li>
              <li><Link to="/collections">Collections</Link></li>
              <li><Link to="/organic">Organic</Link></li>
              <li><Link to="/fruits">Fruits</Link></li>
              <li><Link to="/vegetables">Vegetables</Link></li>
              <li><Link to="/grains">Grains</Link></li>
            </ul>
          </div>
          <div className="col-sm-6 col-md-3 mb-4">
            <h5 className="mb-3">Profile</h5>
            <ul className="list-unstyled">
              <li><Link to="/profile">Profile</Link></li>
              <li><Link to="/favorites">Favorites</Link></li>
              <li><Link to="/watchlist">Watchlist</Link></li>
              <li><Link to="/my-listings">My Listings</Link></li>
              <li><Link to="/settings">Settings</Link></li>
            </ul>
          </div>
          <div className="col-sm-6 col-md-3 mb-4">
            <h5 className="mb-3">Resources</h5>
            <ul className="list-unstyled">
              <li><Link to="/help">Help Center</Link></li>
              <li><Link to="/partners">Partners</Link></li>
              <li><Link to="/activity">Activity</Link></li>
              <li><Link to="/taxes">Taxes</Link></li>
              <li><Link to="/rankings">Rankings</Link></li>
            </ul>
          </div>
          <div className="col-sm-6 col-md-3 mb-4">
            <h5 className="mb-3">Company</h5>
            <ul className="list-unstyled">
              <li><Link to="/about">About</Link></li>
              <li><Link to="/careers">Careers</Link></li>
              <li><Link to="/blog">Blog</Link></li>
              <li><Link to="/privacy">Privacy</Link></li>
              <li><Link to="/terms">Terms & Conditions</Link></li>
            </ul>
          </div>
        </div>
        <hr className="mt-0 mb-3" />
        <span className="d-block lh-sm small text-muted">© {new Date().getFullYear()} AgriMarket. All rights reserved.</span>
      </div>
    </footer>
  );
}

export default Footer;
