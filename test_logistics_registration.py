#!/usr/bin/env python3
"""
Test script for logistics partner registration
"""

import requests
import json

def test_logistics_registration():
    """Test the logistics partner registration endpoint"""
    print("🧪 Testing Logistics Partner Registration")
    print("=" * 50)
    
    # Test data
    test_data = {
        "name": "Test Logistics Company",
        "description": "A test logistics company for AgriMarket",
        "contact_phone": "+263771234567",
        "email": "<EMAIL>",
        "base_rate": 15.50,
        "website": "https://testlogistics.co.zw",
        "service_regions": [
            {"id": 1, "name": "Harare", "type": "region"},
            {"id": 2, "name": "Harare", "type": "city"}
        ]
    }
    
    # Test the endpoint
    url = "http://127.0.0.1:8000/api/logistics-partners/register/"
    
    print(f"🔗 Testing URL: {url}")
    print(f"📦 Test data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📋 Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📋 Response Text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
        elif response.status_code == 400:
            print("❌ Bad Request - Check the error details above")
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Is the Django server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_regions_endpoint():
    """Test if regions endpoint is working"""
    print("\n🌍 Testing Regions Endpoint")
    print("=" * 30)
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/regions/", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            count = len(data) if isinstance(data, list) else len(data.get('results', []))
            print(f"✅ Regions endpoint working - {count} regions found")
            
            # Show first few regions
            regions = data if isinstance(data, list) else data.get('results', [])
            if regions:
                print("📋 Sample regions:")
                for region in regions[:3]:
                    print(f"   - {region.get('name')} ({region.get('type')})")
        else:
            print(f"❌ Regions endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Regions test failed: {e}")

def main():
    """Main function"""
    print("🔧 AgriMarket Logistics Registration Test")
    print("=" * 60)
    
    # Test regions first
    test_regions_endpoint()
    
    # Test logistics registration
    test_logistics_registration()
    
    print("\n📋 Next Steps:")
    print("1. If regions test failed: Run 'python create_regions_simple.py' in backend/")
    print("2. If registration test failed: Check the error details above")
    print("3. If successful: Try the frontend form at http://127.0.0.1:5173/logistics-register")

if __name__ == "__main__":
    main()
