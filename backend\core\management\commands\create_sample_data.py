from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import Produce, ProduceCategory
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample data for testing'

    def handle(self, *args, **options):
        # Create categories
        categories_data = [
            {'name': 'Vegetables', 'description': 'Fresh vegetables'},
            {'name': 'Fruits', 'description': 'Fresh fruits'},
            {'name': 'Grains', 'description': 'Cereals and grains'},
            {'name': 'Herbs', 'description': 'Fresh herbs and spices'},
        ]
        
        categories = {}
        for cat_data in categories_data:
            category, created = ProduceCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f"Created category: {category.name}")

        # Create sample users
        farmer1, created = User.objects.get_or_create(
            username='farmer1',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': 'Farmer',
                'is_farmer': True,
                'phone': '+263771234567',
                'location': 'Harare'
            }
        )
        if created:
            farmer1.set_password('password123')
            farmer1.save()
            self.stdout.write("Created farmer1 user")

        farmer2, created = User.objects.get_or_create(
            username='farmer2',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Mary',
                'last_name': 'Grower',
                'is_farmer': True,
                'phone': '+263772345678',
                'location': 'Bulawayo'
            }
        )
        if created:
            farmer2.set_password('password123')
            farmer2.save()
            self.stdout.write("Created farmer2 user")

        # Create sample produce
        produce_data = [
            {
                'farmer': farmer1,
                'category': categories['Vegetables'],
                'name': 'Fresh Tomatoes',
                'description': 'Organic red tomatoes, freshly harvested',
                'price': Decimal('2.50'),
                'unit': 'kg',
                'quantity_available': 100,
                'is_organic': True,
                'approved': True,
                'contact_phone': '+263771234567'
            },
            {
                'farmer': farmer1,
                'category': categories['Vegetables'],
                'name': 'Green Peppers',
                'description': 'Fresh green bell peppers',
                'price': Decimal('3.00'),
                'unit': 'kg',
                'quantity_available': 50,
                'is_organic': False,
                'approved': True,
                'contact_phone': '+263771234567'
            },
            {
                'farmer': farmer2,
                'category': categories['Fruits'],
                'name': 'Sweet Oranges',
                'description': 'Juicy sweet oranges from Mazowe',
                'price': Decimal('1.80'),
                'unit': 'kg',
                'quantity_available': 200,
                'is_organic': True,
                'approved': True,
                'contact_phone': '+263772345678'
            },
            {
                'farmer': farmer2,
                'category': categories['Grains'],
                'name': 'White Maize',
                'description': 'Premium white maize grain',
                'price': Decimal('0.80'),
                'unit': 'kg',
                'quantity_available': 1000,
                'is_organic': False,
                'approved': True,
                'contact_phone': '+263772345678'
            },
            {
                'farmer': farmer1,
                'category': categories['Herbs'],
                'name': 'Fresh Basil',
                'description': 'Aromatic fresh basil leaves',
                'price': Decimal('5.00'),
                'unit': 'kg',
                'quantity_available': 10,
                'is_organic': True,
                'approved': True,
                'contact_phone': '+263771234567'
            }
        ]

        for produce_info in produce_data:
            produce, created = Produce.objects.get_or_create(
                farmer=produce_info['farmer'],
                name=produce_info['name'],
                defaults=produce_info
            )
            if created:
                self.stdout.write(f"Created produce: {produce.name}")

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )