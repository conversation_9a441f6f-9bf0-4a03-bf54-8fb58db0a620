# Superuser-Only Admin Role Assignment

## Overview

AgriMarket now implements a two-tier administrative system for enhanced security:

1. **Superusers** - Full system access, can assign admin roles
2. **Admins** - Limited administrative access, assigned by superusers only

## Security Model

### 🔐 **Superuser Privileges**
- Full system access (Django superuser)
- Can assign/revoke admin roles to any user
- Can access all admin functions
- Cannot be created through public registration

### 👑 **Admin Privileges**
- Limited administrative access
- Can moderate content and access admin dashboard
- Cannot assign admin roles to other users
- Must be assigned by superusers only

### 👥 **Regular Users**
- Farmer or Buyer roles only
- Can register through public signup
- Cannot become admin without superuser intervention

## Implementation

### Backend API Endpoints

#### Assign Admin Role
```
POST /api/users/{user_id}/assign_admin_role/
Authorization: Token {superuser_token}
```

#### Revoke Admin Role
```
POST /api/users/{user_id}/revoke_admin_role/
Authorization: Token {superuser_token}
```

#### List Admin Users
```
GET /api/users/admin_users/
Authorization: Token {superuser_token}
```

### Frontend Interface

#### Superuser Admin Management
- **URL**: `/superuser-admin-management`
- **Access**: Superusers only
- **Features**:
  - View all current admin users
  - Assign admin roles to existing users
  - Revoke admin roles from users
  - Real-time role management

### Management Commands

#### Create Superuser
```bash
cd backend
python manage.py createsuperuser
# or
python manage.py create_superuser --username superuser --email <EMAIL>
```

#### Assign Admin Role
```bash
cd backend
python manage.py assign_admin_role username_here
```

#### Revoke Admin Role
```bash
cd backend
python manage.py assign_admin_role username_here --revoke
```

## Security Features

### ✅ **Protection Mechanisms**

1. **Superuser-Only Access**
   - Only superusers can assign admin roles
   - API endpoints check `is_superuser` status
   - Frontend components verify superuser access

2. **Role Validation**
   - Cannot modify superuser roles through admin management
   - Prevents privilege escalation attacks
   - Clear separation of concerns

3. **Audit Trail**
   - All role changes are logged
   - User information displayed after changes
   - Clear success/error messaging

4. **Input Validation**
   - Validates user existence before role assignment
   - Prevents duplicate role assignments
   - Handles edge cases gracefully

### 🚫 **Restrictions**

1. **Public Registration**
   - Admin roles cannot be selected during signup
   - Only Farmer and Buyer roles available
   - Server-side validation prevents admin registration

2. **Role Modification**
   - Regular admins cannot assign admin roles
   - Superuser roles cannot be modified through web interface
   - Only superusers can manage admin roles

3. **API Security**
   - All admin management endpoints require superuser authentication
   - HTTP 403 Forbidden for non-superusers
   - Token-based authentication required

## User Interface

### Navigation
- **Superusers**: See "Manage Admins" link in navbar
- **Admins**: See "Admin Dashboard" link only
- **Regular Users**: No admin-related links

### Role Badges
- **Superuser**: Red badge in user dropdown
- **Admin**: Blue badge in user dropdown
- **Farmer/Buyer**: Appropriate colored badges

### Admin Management Interface
- Clean, intuitive interface for role management
- Real-time updates after role changes
- Clear visual indicators for current roles
- Bulk operations support

## Workflow

### Creating First Superuser
1. Use Django management command: `python manage.py createsuperuser`
2. Login to the system as superuser
3. Navigate to "Manage Admins" to assign admin roles

### Assigning Admin Roles
1. Login as superuser
2. Go to "Manage Admins" page
3. Find user in the list
4. Click "Make Admin" button
5. Confirm role assignment

### Revoking Admin Roles
1. Login as superuser
2. Go to "Manage Admins" page
3. Find admin user in the list
4. Click "Revoke Admin" button
5. Confirm role revocation

## Best Practices

### For Superusers
1. **Minimal Superuser Accounts**: Create only necessary superuser accounts
2. **Secure Credentials**: Use strong passwords and secure storage
3. **Regular Audits**: Review admin user list regularly
4. **Principle of Least Privilege**: Only assign admin roles when necessary
5. **Documentation**: Keep records of admin role assignments

### For System Administrators
1. **Backup Strategy**: Regular database backups before role changes
2. **Monitoring**: Monitor admin role assignments and usage
3. **Access Control**: Restrict superuser account access
4. **Security Updates**: Keep system dependencies updated
5. **Incident Response**: Have procedures for compromised accounts

### For Developers
1. **Code Review**: All admin-related code changes require review
2. **Testing**: Comprehensive testing of role assignment functionality
3. **Logging**: Implement proper logging for security events
4. **Error Handling**: Graceful error handling for edge cases
5. **Documentation**: Keep security documentation updated

## API Examples

### Assign Admin Role
```javascript
// Frontend example
const assignAdminRole = async (userId) => {
  try {
    const response = await axiosInstance.post(`/users/${userId}/assign_admin_role/`);
    console.log(response.data.message);
  } catch (error) {
    console.error(error.response.data.error);
  }
};
```

### Check Admin Users
```javascript
// Frontend example
const fetchAdminUsers = async () => {
  try {
    const response = await axiosInstance.get('/users/admin_users/');
    return response.data.admin_users;
  } catch (error) {
    console.error('Access denied or error fetching admin users');
  }
};
```

## Troubleshooting

### Common Issues

#### "Only superusers can assign admin roles"
- **Cause**: Non-superuser trying to assign admin roles
- **Solution**: Login with superuser account

#### "Cannot modify superuser roles"
- **Cause**: Attempting to modify superuser through admin interface
- **Solution**: Use Django admin or management commands for superuser changes

#### "User not found"
- **Cause**: Invalid user ID or username
- **Solution**: Verify user exists in the system

### Error Codes

- **403 Forbidden**: Non-superuser access attempt
- **400 Bad Request**: Invalid user ID or already has role
- **404 Not Found**: User does not exist
- **200 OK**: Successful role assignment/revocation

## Security Considerations

1. **Superuser Account Security**: Treat superuser accounts with extreme care
2. **Regular Audits**: Periodically review admin user assignments
3. **Access Logging**: Monitor superuser login and admin assignment activities
4. **Backup Strategy**: Regular backups before making role changes
5. **Incident Response**: Have procedures for handling compromised accounts

---

This security model ensures that admin privileges are carefully controlled and can only be assigned by trusted superusers, providing a robust security framework for the AgriMarket platform.