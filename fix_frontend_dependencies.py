#!/usr/bin/env python3
"""
Fix frontend dependencies for AgriMarket
Ensures all required packages are installed
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(command, cwd=None, description=""):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        if result.stdout.strip():
            print(f"✅ {result.stdout.strip()}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False, e.stderr

def check_node_npm():
    """Check if Node.js and npm are installed"""
    print("🔍 Checking Node.js and npm...")
    
    # Check Node.js
    success, output = run_command("node --version", description="Checking Node.js version")
    if not success:
        print("❌ Node.js is not installed. Please install Node.js from https://nodejs.org/")
        return False
    
    # Check npm
    success, output = run_command("npm --version", description="Checking npm version")
    if not success:
        print("❌ npm is not installed. Please install npm.")
        return False
    
    print("✅ Node.js and npm are available")
    return True

def fix_frontend_dependencies():
    """Fix frontend dependencies"""
    print("\n📦 Fixing Frontend Dependencies")
    print("=" * 40)
    
    frontend_dir = Path("frontend/agrimarket_frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    # Check package.json
    package_json_path = frontend_dir / "package.json"
    if not package_json_path.exists():
        print("❌ package.json not found!")
        return False
    
    # Read package.json to verify react-select is included
    try:
        with open(package_json_path, 'r') as f:
            package_data = json.load(f)
        
        dependencies = package_data.get('dependencies', {})
        if 'react-select' in dependencies:
            print(f"✅ react-select found in package.json: {dependencies['react-select']}")
        else:
            print("❌ react-select not found in package.json")
            return False
    except Exception as e:
        print(f"❌ Error reading package.json: {e}")
        return False
    
    # Clear npm cache and node_modules
    print("\n🧹 Cleaning npm cache and node_modules...")
    
    node_modules_dir = frontend_dir / "node_modules"
    package_lock_path = frontend_dir / "package-lock.json"
    
    # Remove node_modules if it exists
    if node_modules_dir.exists():
        print("Removing node_modules directory...")
        try:
            import shutil
            shutil.rmtree(node_modules_dir)
            print("✅ node_modules removed")
        except Exception as e:
            print(f"⚠️  Could not remove node_modules: {e}")
    
    # Remove package-lock.json if it exists
    if package_lock_path.exists():
        try:
            package_lock_path.unlink()
            print("✅ package-lock.json removed")
        except Exception as e:
            print(f"⚠️  Could not remove package-lock.json: {e}")
    
    # Clear npm cache
    run_command("npm cache clean --force", description="Clearing npm cache")
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    success, output = run_command("npm install", cwd=frontend_dir, 
                                 description="Installing npm packages")
    
    if not success:
        print("❌ npm install failed!")
        return False
    
    # Verify react-select is installed
    react_select_path = frontend_dir / "node_modules" / "react-select"
    if react_select_path.exists():
        print("✅ react-select successfully installed")
    else:
        print("❌ react-select installation failed")
        return False
    
    # Test if we can start the dev server (just check, don't actually start)
    print("\n🧪 Testing development server...")
    success, output = run_command("npm run build", cwd=frontend_dir,
                                 description="Testing build process")
    
    if success:
        print("✅ Frontend build test successful")
    else:
        print("⚠️  Frontend build test failed, but dependencies are installed")
    
    return True

def verify_all_dependencies():
    """Verify all required dependencies are installed"""
    print("\n🔍 Verifying Dependencies")
    print("=" * 40)
    
    frontend_dir = Path("frontend/agrimarket_frontend")
    required_packages = [
        "react",
        "react-dom", 
        "react-router-dom",
        "react-select",
        "react-icons",
        "axios",
        "bootstrap"
    ]
    
    missing_packages = []
    for package in required_packages:
        package_path = frontend_dir / "node_modules" / package
        if package_path.exists():
            print(f"✅ {package}")
        else:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        return False
    else:
        print("\n✅ All required packages are installed!")
        return True

def main():
    """Main function"""
    print("🔧 AgriMarket Frontend Dependencies Fix")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("frontend/agrimarket_frontend").exists():
        print("❌ Please run this script from the AgriMarket root directory")
        sys.exit(1)
    
    # Check Node.js and npm
    if not check_node_npm():
        sys.exit(1)
    
    # Fix dependencies
    if not fix_frontend_dependencies():
        print("❌ Frontend dependency fix failed!")
        sys.exit(1)
    
    # Verify all dependencies
    if not verify_all_dependencies():
        print("❌ Dependency verification failed!")
        sys.exit(1)
    
    print("\n🎉 Frontend Dependencies Fixed!")
    print("\n📋 Next steps:")
    print("1. Start the frontend:")
    print("   cd frontend/agrimarket_frontend")
    print("   npm run dev")
    print("\n2. The react-select import error should now be resolved!")
    print("3. Access your app at: http://127.0.0.1:5173")

if __name__ == "__main__":
    main()
