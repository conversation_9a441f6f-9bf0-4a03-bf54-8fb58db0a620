<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This workspace is for an AgriMarket SaaS platform using Django REST Framework for the backend and Vite + React for the frontend. Please generate code and suggestions that follow best practices for this stack, including API integration, authentication, and modular component design.
