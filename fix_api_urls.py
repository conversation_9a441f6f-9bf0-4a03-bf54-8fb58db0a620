#!/usr/bin/env python3
"""
Fix API URL issues and create regions data for AgriMarket
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def run_command(command, cwd=None, description=""):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        if result.stdout.strip():
            print(f"✅ {result.stdout.strip()}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False, e.stderr

def setup_django():
    """Setup Django environment"""
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False

    # Add backend directory to Python path
    backend_path = str(backend_dir.absolute())
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)

    # Change to backend directory
    original_dir = os.getcwd()
    os.chdir(backend_dir)

    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
        django.setup()
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        os.chdir(original_dir)  # Change back on error
        return False

def create_regions():
    """Create Zimbabwe regions"""
    print("\n🌍 Creating Zimbabwe Regions...")
    
    from core.models import Region
    
    # Zimbabwe provinces and major cities
    zimbabwe_data = {
        "Harare": ["Harare", "Chitungwiza", "Epworth", "Ruwa"],
        "Bulawayo": ["Bulawayo"],
        "Manicaland": ["Mutare", "Rusape", "Chipinge", "Nyanga"],
        "Mashonaland Central": ["Bindura", "Shamva", "Mount Darwin"],
        "Mashonaland East": ["Marondera", "Macheke", "Wedza"],
        "Mashonaland West": ["Chinhoyi", "Kariba", "Makonde"],
        "Masvingo": ["Masvingo", "Chiredzi", "Zaka"],
        "Matabeleland North": ["Victoria Falls", "Hwange", "Binga"],
        "Matabeleland South": ["Gwanda", "Beitbridge", "Plumtree"],
        "Midlands": ["Gweru", "Kwekwe", "Redcliff"]
    }
    
    created_regions = 0
    created_cities = 0
    
    for province_name, cities in zimbabwe_data.items():
        # Create province
        province, created = Region.objects.get_or_create(
            name=province_name,
            type='region',
            defaults={'parent_region': None}
        )
        
        if created:
            print(f"✅ Created province: {province_name}")
            created_regions += 1
        
        # Create cities
        for city_name in cities:
            city, created = Region.objects.get_or_create(
                name=city_name,
                type='city',
                defaults={'parent_region': province}
            )
            
            if created:
                created_cities += 1
    
    print(f"✅ Created {created_regions} provinces and {created_cities} cities")
    print(f"Total regions: {Region.objects.count()}")
    return True

def test_api_endpoints():
    """Test API endpoints"""
    print("\n🧪 Testing API Endpoints...")
    
    import requests
    
    endpoints = [
        "http://127.0.0.1:8000/api/regions/",
        "http://127.0.0.1:8000/api/categories/",
        "http://127.0.0.1:8000/api/logistics-partners/"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            if response.status_code == 200:
                data = response.json()
                count = len(data) if isinstance(data, list) else len(data.get('results', []))
                print(f"✅ {endpoint} - {count} items")
            else:
                print(f"⚠️  {endpoint} - HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint} - Server not running")
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")

def fix_migrations():
    """Fix any migration issues"""
    print("\n🗄️  Checking Migrations...")
    
    commands = [
        ("python manage.py makemigrations", "Creating migrations"),
        ("python manage.py migrate", "Running migrations"),
    ]
    
    for command, description in commands:
        success, output = run_command(command, description=description)
        if not success:
            print(f"⚠️  {description} had issues, but continuing...")

def main():
    """Main function"""
    print("🔧 AgriMarket API URL and Regions Fix")
    print("=" * 50)
    
    # Check current directory
    if not Path("backend").exists():
        print("❌ Please run this script from the AgriMarket root directory")
        sys.exit(1)
    
    # Setup Django
    if not setup_django():
        sys.exit(1)
    
    # Fix migrations
    fix_migrations()
    
    # Create regions
    if not create_regions():
        print("❌ Failed to create regions")
        sys.exit(1)
    
    # Go back to root directory
    os.chdir("..")
    
    print("\n🎉 API URL and Regions Fix Complete!")
    print("\n📋 What was fixed:")
    print("✅ Fixed double /api/ URL issue in LogisticsRegister")
    print("✅ Created Zimbabwe provinces and cities")
    print("✅ Enhanced error handling in frontend")
    print("✅ Fixed API endpoint URLs")
    
    print("\n🚀 Next steps:")
    print("1. Restart your Django server:")
    print("   cd backend")
    print("   python manage.py runserver 127.0.0.1:8000")
    print("\n2. Test the regions endpoint:")
    print("   http://127.0.0.1:8000/api/regions/")
    print("\n3. Test LogisticsRegister page:")
    print("   http://127.0.0.1:5173/logistics-register")
    
    print("\n🧪 Run this to test endpoints:")
    print("   python -c \"import requests; print(requests.get('http://127.0.0.1:8000/api/regions/').json())\"")

if __name__ == "__main__":
    main()
