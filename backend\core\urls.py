from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ProduceViewSet, ProduceCategoryViewSet, MarketPriceViewSet, SMSAlertViewSet,
    ContactRequestViewSet, PriceSourceViewSet, CollectionViewSet, FavoriteViewSet,
    WatchlistViewSet, UserProfileViewSet, RegionViewSet, OrderViewSet,
    LogisticsPartnerViewSet, OrderLogisticsViewSet, LogisticsPartnerReviewViewSet,
    ConversationViewSet, MessageViewSet, MessageNotificationViewSet,
    register_user, login_user, logout_user,
    admin_dashboard_stats, admin_pending_approvals, admin_approve_produce,
    admin_approve_logistics, admin_all_users
)

router = DefaultRouter()
router.register(r'produce', ProduceViewSet)
router.register(r'categories', ProduceCategoryViewSet)
router.register(r'market-prices', MarketPriceViewSet)
router.register(r'sms-alerts', SMSAlertViewSet, basename='sms-alerts')
router.register(r'contact-requests', ContactRequestViewSet, basename='contact-requests')
router.register(r'price-sources', PriceSourceViewSet, basename='price-sources')
router.register(r'collections', CollectionViewSet, basename='collections')
router.register(r'favorites', FavoriteViewSet, basename='favorites')
router.register(r'watchlist', WatchlistViewSet, basename='watchlist')
router.register(r'profile', UserProfileViewSet, basename='profile')
router.register(r'regions', RegionViewSet, basename='regions')
router.register(r'orders', OrderViewSet, basename='orders')
router.register(r'logistics-partners', LogisticsPartnerViewSet, basename='logistics-partners')
router.register(r'order-logistics', OrderLogisticsViewSet, basename='order-logistics')
router.register(r'logistics-reviews', LogisticsPartnerReviewViewSet, basename='logistics-reviews')
router.register(r'conversations', ConversationViewSet, basename='conversation')
router.register(r'messages', MessageViewSet, basename='message')
router.register(r'message-notifications', MessageNotificationViewSet, basename='messagenotification')

urlpatterns = [
    path('', include(router.urls)),
    # Authentication endpoints
    path('register/', register_user, name='register'),
    path('login/', login_user, name='login'),
    path('logout/', logout_user, name='logout'),
    # Admin endpoints
    path('admin/dashboard/stats/', admin_dashboard_stats, name='admin_dashboard_stats'),
    path('admin/pending-approvals/', admin_pending_approvals, name='admin_pending_approvals'),
    path('admin/approve-produce/<int:produce_id>/', admin_approve_produce, name='admin_approve_produce'),
    path('admin/approve-logistics/<int:logistics_id>/', admin_approve_logistics, name='admin_approve_logistics'),
    path('admin/users/', admin_all_users, name='admin_all_users'),
]
