import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aInstagram, FaW<PERSON>sapp, FaFacebookF } from "react-icons/fa6";

function StatsAndSocialSection() {
  return (
    <section className="container section-padding-sm">
      <div className="row mb-5">
        <div className="col-6 col-md-3 mb-4">
          <div className="d-flex align-items-center">
            <div className="stats-icon-container bg-success text-white">
              <FaLeaf size={28} />
            </div>
            <div className="stats-text-container">
              <div className="stats-value">2.7K</div>
              <div className="stats-label">Total Listings</div>
            </div>
          </div>
        </div>
        <div className="col-6 col-md-3 mb-4">
          <div className="d-flex align-items-center">
            <div className="stats-icon-container bg-primary text-white">
              <FaUsers size={28} />
            </div>
            <div className="stats-text-container">
              <div className="stats-value">8.7K</div>
              <div className="stats-label">Total Members</div>
            </div>
          </div>
        </div>
        <div className="col-12 col-md-6 d-flex align-items-center justify-content-md-end gap-2">
          <span className="me-2 fw-bold">Follow us:</span>
          <a href="#" className="btn btn-secondary social-icon">
            <FaXTwitter size={20} />
          </a>
          <a href="#" className="btn btn-secondary social-icon">
            <FaInstagram size={20} />
          </a>
          <a href="#" className="btn btn-secondary social-icon">
            <FaWhatsapp size={20} />
          </a>
          <a href="#" className="btn btn-secondary social-icon">
            <FaFacebookF size={20} />
          </a>
        </div>
      </div>
    </section>
  );
}

export default StatsAndSocialSection;
