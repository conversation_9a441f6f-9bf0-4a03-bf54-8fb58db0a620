import React, { useEffect, useState } from "react";
import axiosInstance from "../api/axiosInstance";
import Navbar from "../components/Navbar";
import Loader from "../components/Loader";
import { useAuth } from "../api/AuthContext";

function AdminDashboard() {
  const [produce, setProduce] = useState([]);
  const [marketPrices, setMarketPrices] = useState([]);
  const [loading, setLoading] = useState(true);
  const { hasRole } = useAuth();

  useEffect(() => {
    async function fetchData() {
      try {
        const [produceRes, pricesRes] = await Promise.all([
          axiosInstance.get("/produce/pending/"),
          axiosInstance.get("/market-prices/")
        ]);
        setProduce(produceRes.data.results || produceRes.data);
        setMarketPrices(pricesRes.data.results || pricesRes.data);
      } catch (err) {
        console.error("Error fetching admin dashboard data:", err);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  const handleApprove = async (id) => {
    try {
      await axiosInstance.post(`/produce/${id}/approve/`);
      setProduce(produce.filter(p => p.id !== id));
    } catch (err) {
      console.error("Error approving produce:", err);
    }
  };

  const handleReject = async (id) => {
    try {
      await axiosInstance.post(`/produce/${id}/reject/`);
      setProduce(produce.filter(p => p.id !== id));
    } catch (err) {
      console.error("Error rejecting produce:", err);
    }
  };

  if (loading) return <div className="container py-5 text-center"><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>;

  if (!hasRole("admin")) {
    return (
      <>
        <Navbar />
        <div className="nav-spacer" />
        <div className="container py-5 text-center">
          <div className="alert alert-danger">You do not have permission to view this page.</div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="nav-spacer" />
      <div className="container py-5">
        <div className="row g-4">
          <div className="col-lg-6">
            <div className="card shadow-sm h-100">
              <div className="card-header bg-warning bg-opacity-25 border-0">
                <h4 className="mb-0"><span className="material-icons align-middle me-2 text-warning">pending_actions</span>Pending Produce Listings</h4>
              </div>
              <ul className="list-group list-group-flush">
                {produce.length === 0 && (
                  <li className="list-group-item text-muted">No pending listings.</li>
                )}
                {produce.map(p => (
                  <li key={p.id} className="list-group-item">
                    <div className="d-flex justify-content-between align-items-start">
                      <div>
                        <strong>{p.name}</strong> - {p.description}
                        <br />
                        <small className="text-muted">
                          By: {p.farmer?.username} | Price: ${p.price} per {p.unit}
                        </small>
                      </div>
                      <div className="btn-group btn-group-sm">
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => handleApprove(p.id)}
                        >
                          Approve
                        </button>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleReject(p.id)}
                        >
                          Reject
                        </button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="col-lg-6">
            <div className="card shadow-sm h-100">
              <div className="card-header bg-info bg-opacity-25 border-0">
                <h4 className="mb-0"><span className="material-icons align-middle me-2 text-info">price_check</span>Market Prices</h4>
              </div>
              <ul className="list-group list-group-flush">
                {marketPrices.length === 0 && (
                  <li className="list-group-item text-muted">No market prices available.</li>
                )}
                {marketPrices.map(mp => (
                  <li key={mp.id} className="list-group-item">
                    <strong>{mp.crop_name}</strong>: ${mp.price}
                    <span className={`badge ms-2 ${mp.approved ? 'bg-success' : 'bg-warning'}`}>
                      {mp.approved ? 'Verified' : 'Pending'}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default AdminDashboard;
