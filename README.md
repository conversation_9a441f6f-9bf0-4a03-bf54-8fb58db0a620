# AgriMarket SaaS Platform

A comprehensive full-stack SaaS platform for agricultural market management in Zimbabwe, connecting farmers directly with buyers and providing real-time market information.

## 🌟 Features

- **Direct Market Access**: Connect farmers with buyers, eliminating middlemen
- **Real-time Pricing**: Live market prices for informed decision making
- **Produce Management**: Complete CRUD operations for agricultural products
- **User Roles**: Separate interfaces for farmers, buyers, and administrators
- **Advanced Search**: Filter and search produce by category, location, and more
- **Collections & Favorites**: Organize and save preferred produce listings
- **Responsive Design**: Mobile-first approach with Bootstrap integration
- **Admin Dashboard**: Comprehensive management tools for platform oversight

## 🛠 Technology Stack

### Backend
- **Django 5.2** - Web framework
- **Django REST Framework** - API development
- **SQLite/PostgreSQL** - Database
- **Django CORS Headers** - Cross-origin resource sharing
- **Django Filter** - Advanced filtering capabilities
- **Pillow** - Image processing

### Frontend
- **React 19** - UI library
- **Vite** - Build tool and development server
- **React Router DOM** - Client-side routing
- **Bootstrap 5** - CSS framework
- **Axios** - HTTP client
- **React Icons** - Icon library

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- Node.js 18+
- npm or yarn

### Automated Setup
Run the setup script from the project root:
```bash
python setup.py
```

### Manual Setup

#### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser  # Optional
python manage.py runserver
```

#### Frontend Setup
```bash
cd frontend/agrimarket_frontend
npm install
npm run dev
```

## 📱 Usage

1. **Backend API**: http://127.0.0.1:8000
   - Admin panel: http://127.0.0.1:8000/admin
   - API endpoints: http://127.0.0.1:8000/api

2. **Frontend Application**: http://127.0.0.1:5173

## 🔗 API Endpoints

### Authentication
- `POST /api/register/` - User registration
- `POST /api/login/` - User login

### Produce Management
- `GET /api/produce/` - List all produce
- `GET /api/produce/approved/` - List approved produce only
- `GET /api/produce/organic/` - List organic produce only
- `POST /api/produce/` - Create new produce listing
- `PUT /api/produce/{id}/` - Update produce listing
- `DELETE /api/produce/{id}/` - Delete produce listing

### Categories & Filtering
- `GET /api/categories/` - List produce categories
- `GET /api/prices/` - Market prices
- `GET /api/collections/` - User collections
- `GET /api/favorites/` - User favorites
- `GET /api/watchlist/` - User watchlist

## 🏗 Project Structure

```
AgriMarket/
├── backend/
��   ├── agrimarket_backend/     # Django project settings
│   ├── core/                   # Main Django app
│   │   ├── models.py          # Database models
│   │   ├── views.py           # API views
│   │   ├── serializers.py     # DRF serializers
│   │   ├── urls.py            # URL routing
│   │   └── admin.py           # Admin configuration
│   ├── requirements.txt       # Python dependencies
│   └── manage.py              # Django management script
├── frontend/
│   └── agrimarket_frontend/
│       ├── src/
│       │   ├── components/    # Reusable React components
│       │   ├── pages/         # Page components
│       │   ├── api/           # API configuration
│       │   └── assets/        # Static assets
│       ├── package.json       # Node.js dependencies
│       └── vite.config.js     # Vite configuration
├── setup.py                   # Automated setup script
└── README.md                  # This file
```

## 🔧 Development

### Backend Development
- Models are defined in `backend/core/models.py`
- API views in `backend/core/views.py`
- URL routing in `backend/core/urls.py`
- Admin interface customization in `backend/core/admin.py`

### Frontend Development
- Components in `frontend/agrimarket_frontend/src/components/`
- Pages in `frontend/agrimarket_frontend/src/pages/`
- API configuration in `frontend/agrimarket_frontend/src/api/`

### Key Features Implemented
- ✅ User authentication with role-based access
- ✅ Produce CRUD operations with image upload
- ✅ Advanced filtering and search
- ✅ Admin dashboard with approval workflows
- ✅ Responsive design with Bootstrap
- ✅ Real-time market pricing
- ✅ Collections and favorites system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in individual component files
- Review the API endpoints documentation

---

**AgriMarket** - Empowering Zimbabwe's Agricultural Community 🌾
