import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.jsx';

import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import './index.css';
import './assets/css/theme.min.css';
import './assets/css/custom.css';  // Import our new custom styles
import './assets/css/utilities.css';  // Import utility classes
import './assets/vendor/node_modules/css/aos.css';
import './assets/vendor/node_modules/css/swiper-bundle.min.css';
import './assets/css/loader.css';  // Import loader CSS globally
import './assets/css/admin-dashboard.css';  // Import admin dashboard styles

// Handle extension-related errors to prevent console spam
window.addEventListener('error', (event) => {
  if (event.message && (
      event.message.includes('Extension context invalidated') ||
      event.message.includes('message channel closed') ||
      event.message.includes('listener indicated an asynchronous response') ||
      event.message.includes('chrome-extension://')
  )) {
    // Suppress extension-related errors
    event.preventDefault();
    return false;
  }
});

// Handle unhandled promise rejections from extensions
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && (
      event.reason.message.includes('Extension context invalidated') ||
      event.reason.message.includes('message channel closed') ||
      event.reason.message.includes('listener indicated an asynchronous response') ||
      event.reason.message.includes('chrome-extension://')
  )) {
    // Suppress extension-related promise rejections
    event.preventDefault();
    return false;
  }
});

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>
);
