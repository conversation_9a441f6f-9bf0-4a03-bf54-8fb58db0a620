import React from "react";
import { Link } from "react-router-dom";
import TopCollectionsSection from "../components/TopCollectionsSection";
import PopularProduceSection from "../components/PopularProduceSection";
import ProduceList from "../components/ProduceList";
import HowItWorksSection from "../components/HowItWorksSection";
import StatsAndSocialSection from "../components/StatsAndSocialSection";
import NewsletterSection from "../components/NewsletterSection";
import Footer from "../components/Footer";
import BackToTopButton from "../components/BackToTopButton";
import SearchModal from "../components/SearchModal";
import "../assets/css/home-enhancements.css";

function Home() {
  return (
    <>
      <main>
        {/* Enhanced Hero Section */}
        <section
          className="hero-section d-flex align-items-center justify-content-center text-center text-white position-relative fade-in-up"
          style={{
            minHeight: "75vh",
            marginTop: "-80px", /* Offset the body padding for full-height hero */
            paddingTop: "80px", /* Add padding back for content */
            background:
              "linear-gradient(135deg, rgba(33, 92, 36, 0.47) 0%, rgba(67, 160, 72, 0.25) 50%, rgba(102, 187, 106, 0.23) 100%), url('/src/assets/img/heroSectionBg.jpg') center/cover no-repeat",
            boxShadow: "0 8px 40px rgba(34,139,34,0.2)",
          }}
        >
          <div className="container py-5">
            <h1 className="display-3 fw-bold mb-4 text-white">
              Empowering Zimbabwe's Agriculture
            </h1>
            <p className="lead mb-5 text-light fs-4">
              The modern marketplace for farmers and buyers. Discover fresh produce, real-time prices, and connect directly with trusted sellers.
            </p>
            
            <div className="d-flex flex-wrap justify-content-center gap-3 mb-5">
              <Link to="/register" className="btn btn-warning text-white btn-lg px-5 py-3 shadow-lg">
                <span className="material-icons me-2 align-middle">agriculture</span>
                Join as Farmer
              </Link>
              <Link to="/register" className="btn btn-outline-light text-white btn-lg px-5 py-3 shadow-lg">
                <span className="material-icons me-2 align-middle">shopping_cart</span>
                Join as Buyer
              </Link>
              <Link to="/produce-explore" className="btn btn-success text-white btn-lg px-5 py-3 shadow-lg">
                <span className="material-icons me-2 align-middle">store</span>
                Browse Market
              </Link>
            </div>
            
            <div className="row justify-content-center g-4 mt-4">
              <div className="col-lg-3 col-md-6">
                <div className="bg-dark bg-opacity-75 rounded-4 p-4 shadow-lg h-100 d-flex flex-column align-items-center">
                  <span className="material-icons text-success fs-1 mb-3">local_florist</span>
                  <div className="fw-bold fs-5 mb-2">Fresh & Local</div>
                  <div className="small text-light text-center">Direct from Zimbabwean farms</div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="bg-dark bg-opacity-75 rounded-4 p-4 shadow-lg h-100 d-flex flex-column align-items-center">
                  <span className="material-icons text-success fs-1 mb-3">trending_up</span>
                  <div className="fw-bold fs-5 mb-2">Real-Time Prices</div>
                  <div className="small text-light text-center">Transparent, fair, and up-to-date</div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="bg-dark bg-opacity-75 rounded-4 p-4 shadow-lg h-100 d-flex flex-column align-items-center">
                  <span className="material-icons text-success fs-1 mb-3">groups</span>
                  <div className="fw-bold fs-5 mb-2">Community</div>
                  <div className="small text-light text-center">Connecting farmers & buyers</div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6">
                <div className="bg-dark bg-opacity-75 rounded-4 p-4 shadow-lg h-100 d-flex flex-column align-items-center">
                  <span className="material-icons text-success fs-1 mb-3">verified</span>
                  <div className="fw-bold fs-5 mb-2">Trusted Platform</div>
                  <div className="small text-light text-center">Verified listings & secure deals</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Social Proof & Stats */}
        <section className="container py-5">
          <div className="stats-section">
            <div className="row text-center g-4">
              <div className="col-md-3">
                <div className="stats-item">
                  <div className="display-4 fw-bold text-success mb-2">500+</div>
                  <div className="text-secondary fs-5">Registered Farmers</div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="stats-item">
                  <div className="display-4 fw-bold text-success mb-2">1000+</div>
                  <div className="text-secondary fs-5">Successful Transactions</div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="stats-item">
                  <div className="display-4 fw-bold text-success mb-2">50+</div>
                  <div className="text-secondary fs-5">Partner Organizations</div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="stats-item">
                  <div className="display-4 fw-bold text-success mb-2">24/7</div>
                  <div className="text-secondary fs-5">Support & Help</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Feature Highlights */}
        <section className="container feature-section">
          <div className="row align-items-center g-5">
            <div className="col-lg-6 order-lg-2 text-center slide-in-right">
              <img 
                src="/src/assets/img/collection/1.png" 
                alt="AgriMarket Platform" 
                className="img-fluid rounded-4 shadow-lg" 
                style={{maxHeight: 400, width: '100%', objectFit: 'cover'}} 
              />
            </div>
            <div className="col-lg-6 order-lg-1 slide-in-left">
              <h2 className="fw-bold mb-4">Why Choose AgriMarket?</h2>
              <ul className="list-unstyled fs-5 mb-5">
                <li className="mb-4 d-flex align-items-center">
                  <span className="material-icons text-success me-3 fs-3">bolt</span>
                  <span>Fast, easy produce posting for farmers</span>
                </li>
                <li className="mb-4 d-flex align-items-center">
                  <span className="material-icons text-success me-3 fs-3">search</span>
                  <span>Powerful search & filtering for buyers</span>
                </li>
                <li className="mb-4 d-flex align-items-center">
                  <span className="material-icons text-success me-3 fs-3">security</span>
                  <span>Secure transactions & verified users</span>
                </li>
                <li className="mb-4 d-flex align-items-center">
                  <span className="material-icons text-success me-3 fs-3">support_agent</span>
                  <span>Friendly support & community</span>
                </li>
              </ul>
              <Link to="/register" className="btn btn-success btn-lg px-5 py-3 shadow-lg">
                <span className="material-icons me-2 align-middle">rocket_launch</span>
                Get Started Free
              </Link>
            </div>
          </div>
        </section>

        {/* Enhanced Top Collections Section */}
        <div className="top-collections-section">
          <TopCollectionsSection />
          <div className="text-center mb-4">
            <Link to="/collections" className="btn btn-outline-success px-5 py-3 fw-bold shadow-lg">
              <span className="material-icons me-2 align-middle">collections</span>
              View All Collections
            </Link>
          </div>
        </div>

        {/* Enhanced Popular Produce Section */}
        <div className="popular-produce-section">
          <PopularProduceSection />
        </div>

        {/* All Produce List */}
        <section className="container py-5">
          <div className="text-center mb-5">
            <h2 className="display-6 fw-bold mb-3">All Available Produce</h2>
            <p className="lead text-muted">Browse through our complete selection of fresh produce</p>
          </div>
          <ProduceList />
        </section>

        {/* How It Works Section */}
        <HowItWorksSection />

        {/* Platform Stats & Social Section */}
        <StatsAndSocialSection />

        {/* Newsletter Signup Section */}
        <NewsletterSection />
      </main>

      {/* Footer is rendered in App.jsx, not here */}

      {/* Back To Top Button */}
      <BackToTopButton />

      {/* Search Modal (global, not visible by default) */}
      <SearchModal />
    </>
  );
}

export default Home;
