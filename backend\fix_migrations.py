#!/usr/bin/env python3
"""
Fix migrations and database issues for AgriMarket
"""

import os
import sys
import django
import subprocess
from pathlib import Path

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    django.setup()

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=True)
        if result.stdout.strip():
            print(f"✅ {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def fix_migrations():
    """Fix migration issues"""
    print("🗄️  Fixing Database Migrations")
    print("=" * 40)
    
    # Remove existing migration files (except __init__.py)
    migrations_dir = Path("core/migrations")
    if migrations_dir.exists():
        for file in migrations_dir.glob("*.py"):
            if file.name != "__init__.py":
                try:
                    file.unlink()
                    print(f"Removed {file.name}")
                except:
                    pass
    
    # Remove database file
    db_file = Path("db.sqlite3")
    if db_file.exists():
        try:
            db_file.unlink()
            print("Removed existing database")
        except:
            pass
    
    # Create fresh migrations
    commands = [
        ("python manage.py makemigrations core", "Creating fresh migrations"),
        ("python manage.py migrate", "Running migrations"),
        ("python manage.py collectstatic --noinput", "Collecting static files"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def create_initial_data():
    """Create initial data"""
    print("\n📊 Creating Initial Data")
    print("=" * 40)
    
    setup_django()
    
    from core.models import ProduceCategory, User
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    
    # Create superuser
    if not User.objects.filter(is_superuser=True).exists():
        try:
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_farmer=False,
                is_buyer=False
            )
            print("✅ Created superuser: admin/admin123")
        except Exception as e:
            print(f"⚠️  Superuser creation failed: {e}")
    
    # Create test farmer and buyer
    try:
        if not User.objects.filter(username='farmer1').exists():
            farmer = User.objects.create_user(
                username='farmer1',
                email='<EMAIL>',
                password='farmer123',
                is_farmer=True,
                is_buyer=False,
                first_name='John',
                last_name='Farmer',
                phone='+263771234567',
                location='Harare, Zimbabwe'
            )
            print("✅ Created test farmer: farmer1/farmer123")
        
        if not User.objects.filter(username='buyer1').exists():
            buyer = User.objects.create_user(
                username='buyer1',
                email='<EMAIL>',
                password='buyer123',
                is_farmer=False,
                is_buyer=True,
                first_name='Jane',
                last_name='Buyer',
                phone='+263777654321',
                location='Bulawayo, Zimbabwe'
            )
            print("✅ Created test buyer: buyer1/buyer123")
    except Exception as e:
        print(f"⚠️  Test user creation failed: {e}")
    
    # Create categories
    categories = [
        {"name": "Vegetables", "description": "Fresh vegetables and leafy greens", "icon": "🥬"},
        {"name": "Fruits", "description": "Fresh fruits and berries", "icon": "🍎"},
        {"name": "Grains", "description": "Cereals, rice, wheat, and other grains", "icon": "🌾"},
        {"name": "Herbs", "description": "Herbs, spices, and medicinal plants", "icon": "🌿"},
        {"name": "Livestock", "description": "Cattle, poultry, and other livestock", "icon": "🐄"},
        {"name": "Dairy", "description": "Milk, cheese, and dairy products", "icon": "🥛"},
        {"name": "Nuts & Seeds", "description": "Nuts, seeds, and legumes", "icon": "🥜"},
        {"name": "Root Vegetables", "description": "Potatoes, carrots, and other root vegetables", "icon": "🥕"},
    ]
    
    for cat_data in categories:
        try:
            category, created = ProduceCategory.objects.get_or_create(
                name=cat_data["name"],
                defaults={
                    "description": cat_data["description"],
                    "icon": cat_data["icon"]
                }
            )
            if created:
                print(f"✅ Created category: {category.name}")
            else:
                print(f"📋 Category already exists: {category.name}")
        except Exception as e:
            print(f"⚠️  Category creation failed for {cat_data['name']}: {e}")
    
    print("✅ Initial data setup complete!")

def test_setup():
    """Test the setup"""
    print("\n🧪 Testing Setup")
    print("=" * 40)
    
    setup_django()
    
    from core.models import ProduceCategory, User
    
    # Test models
    try:
        user_count = User.objects.count()
        category_count = ProduceCategory.objects.count()
        admin_count = User.objects.filter(is_superuser=True).count()
        
        print(f"✅ Users: {user_count}")
        print(f"✅ Categories: {category_count}")
        print(f"✅ Admins: {admin_count}")
        
        if user_count > 0 and category_count > 0 and admin_count > 0:
            print("✅ Database setup successful!")
            return True
        else:
            print("⚠️  Database setup incomplete")
            return False
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 AgriMarket Database Fix")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("manage.py").exists():
        print("❌ Please run this script from the backend directory")
        sys.exit(1)
    
    # Fix migrations
    if not fix_migrations():
        print("❌ Migration fix failed!")
        sys.exit(1)
    
    # Create initial data
    create_initial_data()
    
    # Test setup
    if test_setup():
        print("\n🎉 Database fix complete!")
        print("\n📋 Test accounts created:")
        print("👤 Admin: admin/admin123")
        print("🌾 Farmer: farmer1/farmer123")
        print("🛒 Buyer: buyer1/buyer123")
        print("\n🚀 You can now start the server:")
        print("python manage.py runserver 127.0.0.1:8000")
    else:
        print("❌ Setup test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
