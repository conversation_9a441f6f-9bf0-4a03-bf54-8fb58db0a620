#!/usr/bin/env python3
"""
Debug Django setup and URL routing
"""

import os
import sys
import django
from pathlib import Path

def setup_django():
    """Setup Django environment"""
    # Add the backend directory to Python path
    backend_dir = Path("backend")
    if backend_dir.exists():
        sys.path.insert(0, str(backend_dir.absolute()))
        os.chdir(backend_dir)
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
    
    try:
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def check_urls():
    """Check URL patterns"""
    try:
        from django.urls import get_resolver
        from django.conf import settings
        
        print("\n🔍 Checking URL patterns...")
        
        # Get the root URL resolver
        resolver = get_resolver()
        
        print(f"Root URLconf: {settings.ROOT_URLCONF}")
        
        # Print all URL patterns
        def print_urls(urlpatterns, prefix=""):
            for pattern in urlpatterns:
                if hasattr(pattern, 'url_patterns'):
                    # This is an include() pattern
                    print(f"{prefix}{pattern.pattern} -> INCLUDE")
                    print_urls(pattern.url_patterns, prefix + "  ")
                else:
                    # This is a regular pattern
                    print(f"{prefix}{pattern.pattern} -> {pattern.callback}")
        
        print("\nURL Patterns:")
        print_urls(resolver.url_patterns)
        
        return True
    except Exception as e:
        print(f"❌ Error checking URLs: {e}")
        return False

def check_apps():
    """Check installed apps"""
    try:
        from django.conf import settings
        
        print("\n📱 Installed Apps:")
        for app in settings.INSTALLED_APPS:
            print(f"  - {app}")
        
        return True
    except Exception as e:
        print(f"❌ Error checking apps: {e}")
        return False

def check_models():
    """Check if models are properly loaded"""
    try:
        from core.models import ProduceCategory
        
        print("\n🗄️  Checking models...")
        
        # Check if we can query the model
        count = ProduceCategory.objects.count()
        print(f"✅ ProduceCategory model works, {count} records found")
        
        # List all categories
        categories = ProduceCategory.objects.all()
        for cat in categories:
            print(f"  - {cat.name}: {cat.description}")
        
        return True
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False

def check_views():
    """Check if views are importable"""
    try:
        from core.views import ProduceCategoryViewSet, health_check
        
        print("\n👁️  Checking views...")
        print(f"✅ ProduceCategoryViewSet: {ProduceCategoryViewSet}")
        print(f"✅ health_check: {health_check}")
        
        return True
    except Exception as e:
        print(f"❌ Error checking views: {e}")
        return False

def test_api_manually():
    """Test API endpoints manually"""
    try:
        from django.test import Client
        from django.urls import reverse
        
        print("\n🧪 Testing API endpoints...")
        
        client = Client()
        
        # Test different endpoints
        endpoints = [
            '/api/',
            '/api/categories/',
            '/admin/',
        ]
        
        for endpoint in endpoints:
            try:
                response = client.get(endpoint)
                print(f"  {endpoint} -> HTTP {response.status_code}")
                if response.status_code == 200:
                    print(f"    ✅ Success")
                elif response.status_code == 404:
                    print(f"    ❌ Not Found")
                else:
                    print(f"    ⚠️  Status: {response.status_code}")
            except Exception as e:
                print(f"  {endpoint} -> Error: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔍 Django Diagnostic Tool")
    print("=" * 40)
    
    # Check current directory
    print(f"Current directory: {os.getcwd()}")
    
    # Setup Django
    if not setup_django():
        return
    
    # Run all checks
    check_apps()
    check_models()
    check_views()
    check_urls()
    test_api_manually()
    
    print("\n" + "=" * 40)
    print("🏁 Diagnostic complete!")

if __name__ == "__main__":
    main()
