from django.core.management.base import BaseCommand
from core.models import Region

class Command(BaseCommand):
    help = 'Seed initial regions, cities, and towns for Zimbabwe'

    def handle(self, *args, **options):
        # Clear existing regions to avoid duplicates
        Region.objects.all().delete()
        self.stdout.write(self.style.WARNING('Existing regions deleted.'))

        # Define regions, cities, and towns in Zimbabwe
        regions_data = [
            # Regions/Provinces
            {'name': 'Bulawayo', 'type': 'region', 'parent': None},
            {'name': 'Harare', 'type': 'region', 'parent': None},
            {'name': 'Manicaland', 'type': 'region', 'parent': None},
            {'name': 'Mashonaland Central', 'type': 'region', 'parent': None},
            {'name': 'Mashonaland East', 'type': 'region', 'parent': None},
            {'name': 'Mashonaland West', 'type': 'region', 'parent': None},
            {'name': 'Masvingo', 'type': 'region', 'parent': None},
            {'name': 'Matabeleland North', 'type': 'region', 'parent': None},
            {'name': 'Matabeleland South', 'type': 'region', 'parent': None},
            {'name': 'Midlands', 'type': 'region', 'parent': None},
        ]

        cities_data = [
            {'name': 'Bulawayo', 'type': 'city', 'parent': 'Bulawayo'},
            {'name': 'Harare', 'type': 'city', 'parent': 'Harare'},
            {'name': 'Mutare', 'type': 'city', 'parent': 'Manicaland'},
            {'name': 'Gweru', 'type': 'city', 'parent': 'Midlands'},
            {'name': 'Kwekwe', 'type': 'city', 'parent': 'Midlands'},
            {'name': 'Kadoma', 'type': 'city', 'parent': 'Mashonaland West'},
            {'name': 'Masvingo', 'type': 'city', 'parent': 'Masvingo'},
            {'name': 'Chinhoyi', 'type': 'city', 'parent': 'Mashonaland West'},
            {'name': 'Bindura', 'type': 'city', 'parent': 'Mashonaland Central'},
            {'name': 'Marondera', 'type': 'city', 'parent': 'Mashonaland East'},
        ]

        towns_data = [
            {'name': 'Victoria Falls', 'type': 'town', 'parent': 'Matabeleland North'},
            {'name': 'Hwange', 'type': 'town', 'parent': 'Matabeleland North'},
            {'name': 'Beitbridge', 'type': 'town', 'parent': 'Matabeleland South'},
            {'name': 'Gwanda', 'type': 'town', 'parent': 'Matabeleland South'},
            {'name': 'Plumtree', 'type': 'town', 'parent': 'Matabeleland South'},
            {'name': 'Rusape', 'type': 'town', 'parent': 'Manicaland'},
            {'name': 'Chipinge', 'type': 'town', 'parent': 'Manicaland'},
            {'name': 'Shurugwi', 'type': 'town', 'parent': 'Midlands'},
            {'name': 'Gokwe', 'type': 'town', 'parent': 'Midlands'},
            {'name': 'Zvishavane', 'type': 'town', 'parent': 'Midlands'},
            {'name': 'Murewa', 'type': 'town', 'parent': 'Mashonaland East'},
            {'name': 'Mutoko', 'type': 'town', 'parent': 'Mashonaland East'},
            {'name': 'Mazowe', 'type': 'town', 'parent': 'Mashonaland Central'},
            {'name': 'Glendale', 'type': 'town', 'parent': 'Mashonaland Central'},
            {'name': 'Chegutu', 'type': 'town', 'parent': 'Mashonaland West'},
            {'name': 'Kariba', 'type': 'town', 'parent': 'Mashonaland West'},
            {'name': 'Karoi', 'type': 'town', 'parent': 'Mashonaland West'},
            {'name': 'Chiredzi', 'type': 'town', 'parent': 'Masvingo'},
            {'name': 'Triangle', 'type': 'town', 'parent': 'Masvingo'},
        ]

        # Create regions first
        region_map = {}
        for data in regions_data:
            region = Region.objects.create(name=data['name'], type=data['type'], parent_region=None)
            region_map[data['name']] = region
            self.stdout.write(self.style.SUCCESS(f"Created region: {region.name} ({region.type})"))

        # Create cities with parent regions
        for data in cities_data:
            parent = region_map.get(data['parent'])
            if parent:
                city = Region.objects.create(name=data['name'], type=data['type'], parent_region=parent)
                self.stdout.write(self.style.SUCCESS(f"Created city: {city.name} under {parent.name}"))
            else:
                self.stdout.write(self.style.ERROR(f"Parent region {data['parent']} not found for city {data['name']}"))

        # Create towns with parent regions
        for data in towns_data:
            parent = region_map.get(data['parent'])
            if parent:
                town = Region.objects.create(name=data['name'], type=data['type'], parent_region=parent)
                self.stdout.write(self.style.SUCCESS(f"Created town: {town.name} under {parent.name}"))
            else:
                self.stdout.write(self.style.ERROR(f"Parent region {data['parent']} not found for town {data['name']}"))

        self.stdout.write(self.style.SUCCESS('Regions, cities, and towns for Zimbabwe have been seeded successfully.'))
