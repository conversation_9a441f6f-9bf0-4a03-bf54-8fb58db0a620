from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Assign admin role to an existing user'

    def add_arguments(self, parser):
        parser.add_argument(
            'username',
            type=str,
            help='Username of the user to assign admin role to',
        )
        parser.add_argument(
            '--revoke',
            action='store_true',
            help='Revoke admin role instead of assigning it',
        )

    def handle(self, *args, **options):
        username = options['username']
        revoke = options.get('revoke', False)
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User "{username}" does not exist!')
            )
            return
        
        # Prevent modifying superuser roles
        if user.is_superuser:
            self.stdout.write(
                self.style.ERROR('Cannot modify superuser roles through this command!')
            )
            return
        
        if revoke:
            if not user.is_staff:
                self.stdout.write(
                    self.style.WARNING(f'User "{username}" is not an admin.')
                )
                return
            
            user.is_staff = False
            user.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'Admin role revoked from user "{username}".')
            )
        else:
            if user.is_staff:
                self.stdout.write(
                    self.style.WARNING(f'User "{username}" is already an admin.')
                )
                return
            
            user.is_staff = True
            user.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'Admin role assigned to user "{username}".')
            )
        
        # Display user info
        self.stdout.write(
            self.style.HTTP_INFO(f'User Info:')
        )
        self.stdout.write(f'  Username: {user.username}')
        self.stdout.write(f'  Email: {user.email}')
        self.stdout.write(f'  Is Farmer: {user.is_farmer}')
        self.stdout.write(f'  Is Buyer: {user.is_buyer}')
        self.stdout.write(f'  Is Admin: {user.is_staff}')
        self.stdout.write(f'  Is Superuser: {user.is_superuser}')