from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import UserProfile

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test buyer account for debugging login issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='testbuyer',
            help='Username for the test buyer (default: testbuyer)'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='buyer123',
            help='Password for the test buyer (default: buyer123)'
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email for the test buyer'
        )

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']
        email = options['email']

        self.stdout.write(
            self.style.SUCCESS('🛒 Creating Test Buyer Account')
        )
        self.stdout.write('=' * 40)

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            user = User.objects.get(username=username)
            self.stdout.write(
                self.style.WARNING(f'User {username} already exists!')
            )
            self.stdout.write(f'   🛒 Is Buyer: {user.is_buyer}')
            self.stdout.write(f'   ✅ Is Active: {user.is_active}')
            
            # Update to buyer if not already
            if not user.is_buyer:
                user.is_buyer = True
                user.save()
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Updated {username} to buyer role')
                )
            
            # Ensure profile exists
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={'bio': f'Test buyer account for {username}'}
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created profile for {username}')
                )
            
            return

        try:
            # Create new buyer user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name='Test',
                last_name='Buyer',
                is_buyer=True,
                is_active=True,
                phone='+************',
                location='Harare, Zimbabwe'
            )

            # Create user profile
            profile = UserProfile.objects.create(
                user=user,
                bio=f'Test buyer account created for debugging login issues'
            )

            self.stdout.write(
                self.style.SUCCESS(f'✅ Successfully created test buyer account!')
            )
            self.stdout.write(f'   👤 Username: {user.username}')
            self.stdout.write(f'   📧 Email: {user.email}')
            self.stdout.write(f'   🔑 Password: {password}')
            self.stdout.write(f'   🛒 Is Buyer: {user.is_buyer}')
            self.stdout.write(f'   ✅ Is Active: {user.is_active}')
            self.stdout.write(f'   📄 Profile Created: Yes')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error creating test buyer: {e}')
            )
            return

        # Test authentication
        self.stdout.write('\n🧪 Testing Authentication')
        self.stdout.write('=' * 40)

        from django.contrib.auth import authenticate
        auth_user = authenticate(username=username, password=password)

        if auth_user:
            self.stdout.write(
                self.style.SUCCESS('✅ Authentication test: PASSED')
            )
            self.stdout.write(f'   👤 Authenticated as: {auth_user.username}')
            self.stdout.write(f'   🛒 Is Buyer: {auth_user.is_buyer}')
        else:
            self.stdout.write(
                self.style.ERROR('❌ Authentication test: FAILED')
            )

        # Show all buyers
        self.stdout.write('\n📊 All Buyer Accounts')
        self.stdout.write('=' * 40)

        buyers = User.objects.filter(is_buyer=True)
        if buyers.exists():
            self.stdout.write(f'Found {buyers.count()} buyer account(s):')
            for buyer in buyers:
                self.stdout.write(f'   👤 {buyer.username} ({buyer.email})')
        else:
            self.stdout.write(
                self.style.WARNING('❌ No buyer accounts found!')
            )

        # Instructions
        self.stdout.write('\n🎯 Testing Instructions')
        self.stdout.write('=' * 40)
        self.stdout.write('1. Go to: http://127.0.0.1:5173/login')
        self.stdout.write(f'2. Username: {username}')
        self.stdout.write(f'3. Password: {password}')
        self.stdout.write('4. Role: Buyer')
        self.stdout.write('5. Click Login')
        self.stdout.write('6. Should redirect to home page')
        
        self.stdout.write('\nIf login still fails:')
        self.stdout.write('- Check browser console for JavaScript errors')
        self.stdout.write('- Check Django server logs')
        self.stdout.write('- Verify frontend AuthContext buyer handling')
        
        self.stdout.write(
            self.style.SUCCESS('\n🎉 Test buyer account ready for testing!')
        )
