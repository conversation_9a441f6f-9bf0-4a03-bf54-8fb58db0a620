# AgriMarket Security Guidelines

## User Registration Security

### Public Registration Restrictions

For security purposes, the AgriMarket platform has implemented the following restrictions on public user registration:

#### ✅ **Allowed Roles for Public Registration**
- **Farmer** - Users who want to sell agricultural produce
- **Buyer** - Users who want to purchase agricultural produce

#### ❌ **Restricted Roles**
- **Admin/Staff** - Cannot be created through public registration

### Admin Account Creation

Admin accounts can only be created through secure methods:

#### Method 1: Django Management Command
```bash
cd backend
python manage.py create_admin
```

#### Method 2: Django Admin Interface
```bash
cd backend
python manage.py createsuperuser
```

#### Method 3: Custom Management Command
```bash
cd backend
python manage.py create_admin --username admin --email <EMAIL>
```

### Security Features Implemented

#### Frontend Security
- ✅ Admin option removed from registration form
- ✅ Only Farmer and Buyer roles available for signup
- ✅ Clear role descriptions to help users choose correctly
- ✅ Client-side validation for role selection

#### Backend Security
- ✅ Server-side validation prevents admin registration
- ✅ Explicit `is_staff=False` for all public registrations
- ✅ HTTP 403 Forbidden response for admin registration attempts
- ✅ Enhanced error messages for security violations
- ✅ Input validation for required fields

### Registration Flow

#### For Regular Users (Farmers/Buyers)
1. Visit `/register` page
2. Choose between Farmer or Buyer role
3. Fill in required information
4. Account created with appropriate permissions

#### For Admin Users
1. Contact system administrator
2. Admin creates account using management commands
3. Secure credentials provided through secure channels

### API Security

#### Registration Endpoint (`/api/register/`)
```json
{
  "username": "required",
  "email": "required", 
  "password": "required",
  "is_farmer": true,
  "is_buyer": false,
  "is_staff": false  // Always forced to false
}
```

#### Security Validations
- Prevents `is_staff: true` in registration requests
- Validates exactly one role is selected (farmer OR buyer)
- Validates all required fields are present
- Returns appropriate error messages for security violations

### Error Messages

#### Admin Registration Attempt
```json
{
  "error": "Admin registration is not allowed through public signup. Contact system administrator."
}
```

#### Invalid Role Selection
```json
{
  "error": "Please select exactly one role: either Farmer or Buyer."
}
```

### Best Practices

#### For Developers
1. Never expose admin creation endpoints publicly
2. Use management commands for admin account creation
3. Implement proper role-based access control
4. Validate user roles on both frontend and backend
5. Log security-related events

#### For System Administrators
1. Create admin accounts through secure channels only
2. Use strong passwords for admin accounts
3. Regularly audit user roles and permissions
4. Monitor for unauthorized access attempts
5. Keep admin credentials secure and confidential

#### For Users
1. Choose the appropriate role during registration
2. Use strong passwords
3. Keep account credentials secure
4. Report suspicious activity

### Role-Based Access Control

#### Farmer Permissions
- Create and manage produce listings
- View market prices and trends
- Access farmer-specific features

#### Buyer Permissions  
- Browse and search produce
- Save favorites and watchlists
- Contact farmers for purchases

#### Admin Permissions
- Full system access
- User management
- Content moderation
- System configuration

### Monitoring and Auditing

The system logs the following security events:
- Failed admin registration attempts
- Invalid role selection attempts
- Authentication failures
- Unauthorized access attempts

### Future Security Enhancements

Planned security improvements:
- Two-factor authentication for admin accounts
- Account lockout after failed login attempts
- Password complexity requirements
- Session management improvements
- Enhanced audit logging

### Reporting Security Issues

If you discover a security vulnerability:
1. Do not disclose publicly
2. Contact the development team privately
3. Provide detailed information about the issue
4. Allow time for proper remediation

---

**Remember**: Security is everyone's responsibility. Always follow secure coding practices and report any security concerns immediately.