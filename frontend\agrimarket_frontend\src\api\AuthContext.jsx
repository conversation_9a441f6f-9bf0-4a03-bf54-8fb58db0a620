import React, { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null); // {username, role, ...}
  const [token, setToken] = useState(() => localStorage.getItem("token"));
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (token && !user) {
      // Optionally fetch user profile with token
      fetchUserProfile(token);
    }
    // eslint-disable-next-line
  }, [token]);

  const fetchUserProfile = async (token) => {
    setLoading(true);
    try {
      const res = await fetch("http://127.0.0.1:8000/api/profile/me/", {
        headers: { Authorization: `Token ${token}` },
      });
      if (res.ok) {
        const data = await res.json();
        setUser(data.user || data); // adjust as per backend response
      } else {
        console.warn("Failed to fetch user profile, clearing auth state");
        setUser(null);
        setToken(null);
        localStorage.removeItem("token");
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      setUser(null);
      setToken(null);
      localStorage.removeItem("token");
    } finally {
      setLoading(false);
    }
  };

  const login = (userData, token) => {
    setUser(userData);
    setToken(token);
    localStorage.setItem("token", token);
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem("token");
  };

  // Helper to check user role
  const hasRole = (role) => {
    if (!user) return false;
    if (role === "admin" || role === "staff") return !!user.is_staff;
    if (role === "farmer") return !!user.is_farmer;
    if (role === "buyer") return !!user.is_buyer;
    if (role === "logistics") return !!user.is_logistics_partner;
    return false;
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, loading, hasRole }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}
