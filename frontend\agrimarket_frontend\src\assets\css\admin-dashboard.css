/* Admin Dashboard Styles */

.admin-dashboard {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Admin Dashboard Navbar Link */
.admin-dashboard-link {
  transition: all 0.3s ease !important;
}

.admin-dashboard-link:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
  color: white !important;
}

/* Enhanced Navbar Dropdowns */
.navbar .dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 220px;
}

.navbar .dropdown-item {
  padding: 0.75rem 1.25rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.navbar .dropdown-item:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(5px);
}

.navbar .dropdown-item .material-icons {
  font-size: 18px;
  opacity: 0.8;
}

.navbar .dropdown-item:hover .material-icons {
  opacity: 1;
}

.navbar .dropdown-divider {
  margin: 0.5rem 1rem;
  border-color: rgba(0, 0, 0, 0.1);
}

/* Role-specific dropdown styling */
.navbar .nav-link.dropdown-toggle {
  font-weight: 500;
  transition: all 0.3s ease;
  color: var(--bs-dark) !important;
}

.navbar .nav-link.dropdown-toggle:hover {
  color: var(--bs-primary) !important;
  transform: translateY(-1px);
}

/* Ensure all navbar dropdowns have consistent styling */
.navbar .dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  min-width: 220px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.navbar .dropdown-item {
  padding: 0.75rem 1.25rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
  color: var(--bs-dark);
}

.navbar .dropdown-item:hover {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
  color: white;
  transform: translateX(5px);
}

.navbar .dropdown-item .material-icons {
  font-size: 18px;
  opacity: 0.8;
  color: inherit;
}

.navbar .dropdown-item:hover .material-icons {
  opacity: 1;
  color: white;
}

.navbar .dropdown-divider {
  margin: 0.5rem 1rem;
  border-color: rgba(0, 0, 0, 0.1);
}

.admin-sidebar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-height: calc(100vh - 70px);
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.admin-sidebar .card {
  background: transparent;
  border: none;
}

.admin-sidebar .card-header {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
}

.admin-sidebar .list-group-item {
  background: transparent;
  border: none;
  color: rgba(255,255,255,0.8);
  transition: all 0.3s ease;
}

.admin-sidebar .list-group-item:hover {
  background: rgba(255,255,255,0.1);
  color: white;
  transform: translateX(5px);
}

.admin-sidebar .list-group-item.active {
  background: rgba(255,255,255,0.2);
  color: white;
  border-left: 4px solid #ffc107;
}

.stat-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  border-radius: 15px;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-card .card-body {
  padding: 2rem;
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
  color: white;
}

.stat-card.border-success .card-body {
  background: linear-gradient(135deg, var(--bs-success) 0%, #198754 100%);
}

.stat-card.border-info .card-body {
  background: linear-gradient(135deg, var(--bs-info) 0%, #0dcaf0 100%);
}

.stat-card.border-warning .card-body {
  background: linear-gradient(135deg, var(--bs-warning) 0%, #ffc107 100%);
  color: #000;
}

.stat-card.border-danger .card-body {
  background: linear-gradient(135deg, var(--bs-danger) 0%, #dc3545 100%);
}

.approval-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.approval-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.admin-table {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.admin-table tbody tr:hover {
  background-color: #f8f9fa;
}

.admin-content {
  padding: 2rem;
}

.admin-header {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.admin-alert {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-action-btn {
  border-radius: 10px;
  padding: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.quick-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  border-color: currentColor;
}

.system-health-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  border-radius: 15px;
}

.health-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.health-indicator.healthy {
  background-color: #28a745;
  box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.health-indicator.warning {
  background-color: #ffc107;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.health-indicator.danger {
  background-color: #dc3545;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.admin-badge {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.admin-dashboard-title {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.admin-dashboard-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    min-height: auto;
    margin-bottom: 1rem;
  }
  
  .admin-content {
    padding: 1rem;
  }
  
  .stat-card .card-body {
    padding: 1.5rem;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Custom scrollbar for sidebar */
.admin-sidebar::-webkit-scrollbar {
  width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
  border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.5);
}
