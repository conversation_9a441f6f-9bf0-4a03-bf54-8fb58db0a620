#!/usr/bin/env python3
"""
Diagnostic script to check buyer login issues
Run this from the backend directory: python manage.py shell < buyer_login_diagnostic.py
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agrimarket_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import UserProfile

User = get_user_model()

def diagnose_buyer_accounts():
    """Diagnose buyer account issues"""
    print("🔍 BUYER LOGIN DIAGNOSTIC REPORT")
    print("=" * 50)
    
    # Check all users
    all_users = User.objects.all()
    print(f"📊 Total users in database: {all_users.count()}")
    
    # Check buyer accounts
    buyer_users = User.objects.filter(is_buyer=True)
    print(f"👥 Total buyer accounts: {buyer_users.count()}")
    
    # Check farmer accounts for comparison
    farmer_users = User.objects.filter(is_farmer=True)
    print(f"🌾 Total farmer accounts: {farmer_users.count()}")
    
    # Check admin accounts
    admin_users = User.objects.filter(is_staff=True)
    print(f"👑 Total admin accounts: {admin_users.count()}")
    
    print("\n" + "=" * 50)
    print("📋 DETAILED USER ANALYSIS")
    print("=" * 50)
    
    for user in all_users:
        print(f"\n👤 User: {user.username}")
        print(f"   📧 Email: {user.email}")
        print(f"   🌾 Is Farmer: {user.is_farmer}")
        print(f"   🛒 Is Buyer: {user.is_buyer}")
        print(f"   🚚 Is Logistics: {user.is_logistics_partner}")
        print(f"   👑 Is Staff: {user.is_staff}")
        print(f"   ✅ Is Active: {user.is_active}")
        print(f"   🔑 Has Usable Password: {user.has_usable_password()}")
        
        # Check if user has profile
        try:
            profile = UserProfile.objects.get(user=user)
            print(f"   📄 Has Profile: Yes")
        except UserProfile.DoesNotExist:
            print(f"   📄 Has Profile: No")
    
    print("\n" + "=" * 50)
    print("🧪 BUYER ACCOUNT TESTS")
    print("=" * 50)
    
    if buyer_users.count() == 0:
        print("❌ NO BUYER ACCOUNTS FOUND!")
        print("🔧 Creating a test buyer account...")
        
        # Create test buyer account
        test_buyer = User.objects.create_user(
            username='testbuyer',
            email='<EMAIL>',
            password='testpass123',
            is_buyer=True,
            is_active=True
        )
        
        # Create profile for test buyer
        UserProfile.objects.create(
            user=test_buyer,
            bio="Test buyer account"
        )
        
        print(f"✅ Created test buyer: {test_buyer.username}")
        print("🔑 Password: testpass123")
        print("📧 Email: <EMAIL>")
        
    else:
        print(f"✅ Found {buyer_users.count()} buyer account(s)")
        
        for buyer in buyer_users:
            print(f"\n🛒 Buyer: {buyer.username}")
            
            # Test authentication
            from django.contrib.auth import authenticate
            
            # We can't test with actual password, but we can check other things
            print(f"   ✅ Is Active: {buyer.is_active}")
            print(f"   🔑 Has Usable Password: {buyer.has_usable_password()}")
            print(f"   📧 Email: {buyer.email}")
            
            # Check profile
            try:
                profile = UserProfile.objects.get(user=buyer)
                print(f"   📄 Profile: Exists")
            except UserProfile.DoesNotExist:
                print(f"   📄 Profile: Missing - Creating...")
                UserProfile.objects.create(
                    user=buyer,
                    bio=f"Profile for {buyer.username}"
                )
                print(f"   ✅ Profile created")
    
    print("\n" + "=" * 50)
    print("🔧 RECOMMENDATIONS")
    print("=" * 50)
    
    if buyer_users.count() == 0:
        print("1. ❌ No buyer accounts exist - users need to register as buyers")
        print("2. ✅ Test buyer account created - try logging in with:")
        print("   Username: testbuyer")
        print("   Password: testpass123")
    else:
        print("1. ✅ Buyer accounts exist")
        print("2. 🧪 Try logging in with existing buyer credentials")
        print("3. 🔍 Check browser console for JavaScript errors")
        print("4. 📊 Check Django logs for authentication errors")
    
    print("\n📋 NEXT STEPS:")
    print("1. Test login with buyer credentials")
    print("2. Check browser network tab for API calls")
    print("3. Verify frontend role detection logic")
    print("4. Check AuthContext buyer role handling")

if __name__ == "__main__":
    diagnose_buyer_accounts()
