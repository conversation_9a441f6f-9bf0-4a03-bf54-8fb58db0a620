import React from "react";
import { Link } from "react-router-dom";

function Categories() {
  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <h1 className="mb-4 text-primary">Categories</h1>
        <p className="lead">
          Browse produce by category. Click a category to see available listings.
        </p>
        <div className="row g-3">
          <div className="col-md-3">
            <Link to="/fruits" className="btn btn-outline-warning w-100 py-3">
              Fruits
            </Link>
          </div>
          <div className="col-md-3">
            <Link to="/vegetables" className="btn btn-outline-success w-100 py-3">
              Vegetables
            </Link>
          </div>
          <div className="col-md-3">
            <Link to="/grains" className="btn btn-outline-primary w-100 py-3">
              Grains
            </Link>
          </div>
          <div className="col-md-3">
            <Link to="/organic" className="btn btn-outline-success w-100 py-3">
              Organic
            </Link>
          </div>
          <div className="col-md-3">
            <Link to="/collections" className="btn btn-outline-success w-100 py-3">
              Collections
            </Link>
          </div>
        </div>
        <div className="alert alert-info mt-4">
          More categories coming soon!
        </div>
      </div>
    </>
  );
}

export default Categories;
