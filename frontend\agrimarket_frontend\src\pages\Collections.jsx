import React, { useState } from "react";
import CollectionsTable from "../components/CollectionsTable";
import CollectionsHowItWorks from "../components/CollectionsHowItWorks";

function Collections() {
  const [view, setView] = useState("Collections");
  return (
    <>
      <div className="nav-spacer" />
      <div className="container py-5">
        <div className="d-flex pb-4 pt-3 justify-content-center align-items-center">
          <h2 className="me-3 mb-0 display-6 fw-normal">Top</h2>
          <select
            className="form-select collection-select text-muted border-0 bg-transparent"
            value={view}
            onChange={e => setView(e.target.value)}
          >
            <option value="Collections">Collections</option>
            <option value="Sellers">Sellers</option>
          </select>
        </div>
        <CollectionsTable view={view} />
        <CollectionsHowItWorks />
      </div>
    </>
  );
}

export default Collections;
